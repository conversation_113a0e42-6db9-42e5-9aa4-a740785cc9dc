import { createServerSupabase } from './supabase-server';
import { createClientSupabase } from './supabase-client';
import { redirect } from 'next/navigation';
import type { User } from '@/types/database';

// Server-side authentication helpers
export async function getServerSession() {
  const supabase = createServerSupabase();
  const { data: { session }, error } = await supabase.auth.getSession();

  if (error) {
    console.error('Error getting session:', error);
    return null;
  }

  return session;
}

export async function getServerUser(): Promise<User | null> {
  const supabase = createServerSupabase();

  // Use getUser() for secure authentication
  const { data: { user: authUser }, error: authError } = await supabase.auth.getUser();

  if (authError || !authUser) {
    console.error('Error getting authenticated user:', authError);
    return null;
  }

  // Fetch user profile from database
  const { data: user, error } = await supabase
    .from('users')
    .select('*')
    .eq('id', authUser.id)
    .single();

  if (error) {
    console.error('Error getting user profile:', error);
    return null;
  }

  return user;
}

export async function requireAuth() {
  const supabase = createServerSupabase();
  const { data: { user }, error } = await supabase.auth.getUser();

  if (error || !user) {
    redirect('/auth/login');
  }

  return user;
}

export async function requireAdmin() {
  const user = await getServerUser();
  if (!user || user.role !== 'admin') {
    redirect('/unauthorized');
  }
  return user;
}

// Client-side authentication helpers
export const signUp = async (email: string, password: string, userData: Partial<User>) => {
  const supabase = createClientSupabase();

  const { data, error } = await supabase.auth.signUp({
    email,
    password,
    options: {
      data: {
        full_name: userData.full_name,
        phone: userData.phone,
      },
    },
  });

  if (error) {
    return { data: null, error: error.message };
  }

  // Create user profile
  if (data.user) {
    const { error: profileError } = await supabase
      .from('users')
      .insert({
        id: data.user.id,
        email: data.user.email!,
        full_name: userData.full_name,
        phone: userData.phone,
        role: 'customer',
      });

    if (profileError) {
      console.error('Error creating user profile:', profileError);
      return { data: null, error: 'Failed to create user profile' };
    }
  }

  return { data, error: null };
};

export const signIn = async (email: string, password: string) => {
  const supabase = createClientSupabase();

  const { data, error } = await supabase.auth.signInWithPassword({
    email,
    password,
  });

  if (error) {
    return { data: null, error: error.message };
  }

  return { data, error: null };
};

export const signOut = async () => {
  const supabase = createClientSupabase();

  const { error } = await supabase.auth.signOut();

  if (error) {
    return { error: error.message };
  }

  return { error: null };
};

export const resetPassword = async (email: string) => {
  const supabase = createClientSupabase();

  const { error } = await supabase.auth.resetPasswordForEmail(email, {
    redirectTo: `${window.location.origin}/auth/reset-password`,
  });

  if (error) {
    return { error: error.message };
  }

  return { error: null };
};

export const updatePassword = async (password: string) => {
  const supabase = createClientSupabase();

  const { error } = await supabase.auth.updateUser({
    password,
  });

  if (error) {
    return { error: error.message };
  }

  return { error: null };
};

export const signInWithProvider = async (provider: 'google' | 'github') => {
  const supabase = createClientSupabase();

  const { error } = await supabase.auth.signInWithOAuth({
    provider,
    options: {
      redirectTo: `${window.location.origin}/auth/callback`,
    },
  });

  if (error) {
    return { error: error.message };
  }

  return { error: null };
};

// Profile management
export const updateProfile = async (userId: string, updates: Partial<User>) => {
  const supabase = createClientSupabase();

  const { data, error } = await supabase
    .from('users')
    .update({
      ...updates,
      updated_at: new Date().toISOString(),
    })
    .eq('id', userId)
    .select()
    .single();

  if (error) {
    return { data: null, error: error.message };
  }

  return { data, error: null };
};

export const uploadProfileImage = async (userId: string, file: File) => {
  const supabase = createClientSupabase();

  const fileExt = file.name.split('.').pop();
  const fileName = `${userId}/profile.${fileExt}`;

  const { data: uploadData, error: uploadError } = await supabase.storage
    .from('profiles')
    .upload(fileName, file, { upsert: true });

  if (uploadError) {
    return { data: null, error: uploadError.message };
  }

  const { data: { publicUrl } } = supabase.storage
    .from('profiles')
    .getPublicUrl(fileName);

  // Update user profile with image URL
  const { data, error } = await updateProfile(userId, {
    profile_image_url: publicUrl,
  });

  if (error) {
    return { data: null, error };
  }

  return { data: { ...data, profile_image_url: publicUrl }, error: null };
};

// Session management
export const refreshSession = async () => {
  const supabase = createClientSupabase();

  const { data, error } = await supabase.auth.refreshSession();

  if (error) {
    return { data: null, error: error.message };
  }

  return { data, error: null };
};

// Email verification
export const resendEmailVerification = async () => {
  const supabase = createClientSupabase();

  const { error } = await supabase.auth.resend({
    type: 'signup',
    email: '', // Will use the current user's email
  });

  if (error) {
    return { error: error.message };
  }

  return { error: null };
};
