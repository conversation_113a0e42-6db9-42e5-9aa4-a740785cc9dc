// Test script to create users and test authentication
const BASE_URL = 'http://localhost:3001';

async function testRegistration(userData) {
  try {
    console.log(`\n🔄 Testing registration for ${userData.email}...`);

    const response = await fetch(`${BASE_URL}/api/auth/register`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(userData),
    });

    const data = await response.json();

    if (!response.ok) {
      console.error(`❌ Registration failed for ${userData.email}:`, data.error);
      return false;
    }

    console.log(`✅ Registration successful for ${userData.email}`);
    return true;
  } catch (error) {
    console.error(`❌ Registration error for ${userData.email}:`, error.message);
    return false;
  }
}

async function testLogin(email, password) {
  try {
    console.log(`\n🔄 Testing login for ${email}...`);

    const response = await fetch(`${BASE_URL}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include',
      body: JSON.stringify({ email, password }),
    });

    const data = await response.json();

    if (!response.ok) {
      console.error(`❌ Login failed for ${email}:`, data.error);
      return false;
    }

    console.log(`✅ Login successful for ${email}`);
    console.log(`   User: ${data.user.full_name} (${data.user.role})`);
    return true;
  } catch (error) {
    console.error(`❌ Login error for ${email}:`, error.message);
    return false;
  }
}

async function testSession() {
  try {
    console.log(`\n🔄 Testing session check...`);

    const response = await fetch(`${BASE_URL}/api/auth/session`, {
      method: 'GET',
      credentials: 'include',
    });

    const data = await response.json();

    if (!response.ok) {
      console.error(`❌ Session check failed:`, data.error);
      return false;
    }

    if (data.user) {
      console.log(`✅ Session active for: ${data.user.full_name} (${data.user.role})`);
    } else {
      console.log(`ℹ️ No active session`);
    }
    return true;
  } catch (error) {
    console.error(`❌ Session check error:`, error.message);
    return false;
  }
}

async function runTests() {
  console.log('🚀 Starting authentication tests...\n');

  // Test data
  const adminUser = {
    email: '<EMAIL>',
    password: 'admin123456',
    fullName: 'Admin User',
    phone: '+91-9876543210'
  };

  const customerUser = {
    email: '<EMAIL>',
    password: 'customer123456',
    fullName: 'John Doe',
    phone: '+91-9123456789'
  };

  // Test registration
  await testRegistration(adminUser);
  await testRegistration(customerUser);

  // Test login
  await testLogin(adminUser.email, adminUser.password);
  await testSession();

  await testLogin(customerUser.email, customerUser.password);
  await testSession();

  console.log('\n✨ Authentication tests completed!');
}

// Run the tests
runTests().catch(console.error);
