# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# Email Configuration for Inquiry Notifications
ADMIN_EMAIL=<EMAIL>

# Email Service Configuration (Gmail - Free Tier)
# Set these to enable email notifications, otherwise inquiries will be logged to console


# Note: For Gmail, you need to:
# 1. Enable 2-Factor Authentication on your Google account
# 2. Generate an App Password (not your regular password)
# 3. Use the App Password in EMAIL_PASSWORD field
