"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/inquiries/route";
exports.ids = ["app/api/inquiries/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

module.exports = require("child_process");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "dns":
/*!**********************!*\
  !*** external "dns" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("dns");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("net");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Finquiries%2Froute&page=%2Fapi%2Finquiries%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Finquiries%2Froute.ts&appDir=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Finquiries%2Froute&page=%2Fapi%2Finquiries%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Finquiries%2Froute.ts&appDir=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_peebs_Documents_projects_p7_comprehensive_app_api_inquiries_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/inquiries/route.ts */ \"(rsc)/./app/api/inquiries/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/inquiries/route\",\n        pathname: \"/api/inquiries\",\n        filename: \"route\",\n        bundlePath: \"app/api/inquiries/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\api\\\\inquiries\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_peebs_Documents_projects_p7_comprehensive_app_api_inquiries_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/inquiries/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Finquiries%2Froute&page=%2Fapi%2Finquiries%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Finquiries%2Froute.ts&appDir=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/inquiries/route.ts":
/*!************************************!*\
  !*** ./app/api/inquiries/route.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var _lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase-server */ \"(rsc)/./lib/supabase-server.ts\");\n/* harmony import */ var _lib_email__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/email */ \"(rsc)/./lib/email.ts\");\n\n\n\n// GET /api/inquiries - Get inquiries (Admin only)\nasync function GET(request) {\n    try {\n        const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createServerSupabase)();\n        const { searchParams } = new URL(request.url);\n        // Parse query parameters\n        const page = parseInt(searchParams.get(\"page\") || \"1\");\n        const limit = parseInt(searchParams.get(\"limit\") || \"10\");\n        const status = searchParams.get(\"status\");\n        const inquiryType = searchParams.get(\"inquiryType\");\n        // Build query\n        let query = supabase.from(\"inquiries\").select(`\n        id,\n        name,\n        email,\n        phone,\n        subject,\n        message,\n        inquiry_type,\n        status,\n        created_at\n      `, {\n            count: \"exact\"\n        });\n        // Apply filters\n        if (status && status !== \"all\") {\n            query = query.eq(\"status\", status);\n        }\n        if (inquiryType && inquiryType !== \"all\") {\n            query = query.eq(\"inquiry_type\", inquiryType);\n        }\n        // Apply pagination\n        const from = (page - 1) * limit;\n        const to = from + limit - 1;\n        query = query.order(\"created_at\", {\n            ascending: false\n        }).range(from, to);\n        const { data: inquiries, error, count } = await query;\n        if (error) {\n            console.error(\"Error fetching inquiries:\", error);\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Failed to fetch inquiries\"\n            }, {\n                status: 500\n            });\n        }\n        const totalPages = Math.ceil((count || 0) / limit);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            data: inquiries || [],\n            pagination: {\n                page,\n                limit,\n                total: count || 0,\n                totalPages\n            }\n        });\n    } catch (error) {\n        console.error(\"Error in GET /api/inquiries:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n// POST /api/inquiries - Create a new inquiry\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        // Basic validation\n        const requiredFields = [\n            \"name\",\n            \"email\",\n            \"message\"\n        ];\n        for (const field of requiredFields){\n            if (!body[field]) {\n                return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                    error: `Missing required field: ${field}`\n                }, {\n                    status: 400\n                });\n            }\n        }\n        // Validate email format\n        const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n        if (!emailRegex.test(body.email)) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Invalid email format\"\n            }, {\n                status: 400\n            });\n        }\n        const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createServerSupabase)();\n        // Save inquiry to database\n        const { data: inquiry, error: dbError } = await supabase.from(\"inquiries\").insert({\n            name: body.name,\n            email: body.email,\n            phone: body.phone || null,\n            subject: body.subject || null,\n            message: body.message,\n            inquiry_type: body.inquiry_type || \"General Inquiry\",\n            status: \"new\"\n        }).select().single();\n        if (dbError) {\n            console.error(\"Error saving inquiry to database:\", dbError);\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Failed to save inquiry\"\n            }, {\n                status: 500\n            });\n        }\n        // Log the saved inquiry to terminal\n        console.log(\"\\uD83D\\uDCBE INQUIRY SAVED TO DATABASE:\");\n        console.log(\"━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\");\n        console.log(\"Database Row:\", JSON.stringify(inquiry, null, 2));\n        console.log(\"━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\");\n        // Send email notification\n        try {\n            await (0,_lib_email__WEBPACK_IMPORTED_MODULE_2__.sendInquiryNotification)(inquiry);\n        } catch (emailError) {\n            console.error(\"❌ Error sending email notification:\", emailError);\n        // Don't fail the request if email fails, just log it\n        }\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            message: \"Inquiry submitted successfully. We will get back to you soon!\",\n            data: inquiry\n        }, {\n            status: 201\n        });\n    } catch (error) {\n        console.error(\"Error in POST /api/inquiries:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/inquiries/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/email.ts":
/*!**********************!*\
  !*** ./lib/email.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   sendInquiryNotification: () => (/* binding */ sendInquiryNotification)\n/* harmony export */ });\n/* harmony import */ var nodemailer__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! nodemailer */ \"(rsc)/./node_modules/nodemailer/lib/nodemailer.js\");\n\n// Email configuration\nconst createTransporter = ()=>{\n    const emailService = process.env.EMAIL_SERVICE || \"gmail\";\n    const emailUser = process.env.EMAIL_USER;\n    const emailPassword = process.env.EMAIL_PASSWORD;\n    if (!emailUser || !emailPassword) {\n        console.log(\"Email credentials not configured. Emails will be logged to console only.\");\n        return null;\n    }\n    return nodemailer__WEBPACK_IMPORTED_MODULE_0__.createTransport({\n        service: emailService,\n        auth: {\n            user: emailUser,\n            pass: emailPassword\n        }\n    });\n};\nasync function sendInquiryNotification(inquiry) {\n    const adminEmail = process.env.ADMIN_EMAIL;\n    if (!adminEmail) {\n        console.log(\"❌ ADMIN_EMAIL not configured, skipping email notification\");\n        return false;\n    }\n    const transporter = createTransporter();\n    if (!transporter) {\n        console.log(\"\\uD83D\\uDCE7 Email service not configured, logging inquiry details:\");\n        console.log(\"━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\");\n        console.log(\"\\uD83D\\uDCCB NEW INQUIRY RECEIVED\");\n        console.log(\"━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\");\n        console.log(`👤 Name: ${inquiry.name}`);\n        console.log(`📧 Email: ${inquiry.email}`);\n        console.log(`📱 Phone: ${inquiry.phone || \"Not provided\"}`);\n        console.log(`📝 Subject: ${inquiry.subject || \"No subject\"}`);\n        console.log(`🏷️  Type: ${inquiry.inquiry_type}`);\n        console.log(`📅 Date: ${new Date(inquiry.created_at).toLocaleString()}`);\n        console.log(`🆔 ID: ${inquiry.id}`);\n        console.log(\"━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\");\n        console.log(\"\\uD83D\\uDCAC MESSAGE:\");\n        console.log(inquiry.message);\n        console.log(\"━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\");\n        return false;\n    }\n    try {\n        const mailOptions = {\n            from: process.env.EMAIL_USER,\n            to: adminEmail,\n            subject: `New Inquiry: ${inquiry.inquiry_type} - ${inquiry.name}`,\n            html: `\n        <!DOCTYPE html>\n        <html>\n        <head>\n          <meta charset=\"utf-8\">\n          <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n          <title>New Inquiry - Positive7</title>\n        </head>\n        <body style=\"font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;\">\n          <div style=\"background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 10px 10px 0 0; text-align: center;\">\n            <h1 style=\"margin: 0; font-size: 28px;\">New Inquiry Received</h1>\n            <p style=\"margin: 10px 0 0 0; opacity: 0.9;\">Positive7 Educational Tours</p>\n          </div>\n\n          <div style=\"background: #f8f9fa; padding: 30px; border-radius: 0 0 10px 10px; border: 1px solid #e9ecef;\">\n            <div style=\"background: white; padding: 25px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);\">\n              <h2 style=\"color: #495057; margin-top: 0; border-bottom: 2px solid #e9ecef; padding-bottom: 10px;\">Contact Information</h2>\n\n              <table style=\"width: 100%; border-collapse: collapse;\">\n                <tr>\n                  <td style=\"padding: 8px 0; font-weight: bold; color: #6c757d; width: 120px;\">Name:</td>\n                  <td style=\"padding: 8px 0;\">${inquiry.name}</td>\n                </tr>\n                <tr>\n                  <td style=\"padding: 8px 0; font-weight: bold; color: #6c757d;\">Email:</td>\n                  <td style=\"padding: 8px 0;\"><a href=\"mailto:${inquiry.email}\" style=\"color: #007bff; text-decoration: none;\">${inquiry.email}</a></td>\n                </tr>\n                <tr>\n                  <td style=\"padding: 8px 0; font-weight: bold; color: #6c757d;\">Phone:</td>\n                  <td style=\"padding: 8px 0;\">${inquiry.phone || \"Not provided\"}</td>\n                </tr>\n                <tr>\n                  <td style=\"padding: 8px 0; font-weight: bold; color: #6c757d;\">Subject:</td>\n                  <td style=\"padding: 8px 0;\">${inquiry.subject || \"No subject\"}</td>\n                </tr>\n                <tr>\n                  <td style=\"padding: 8px 0; font-weight: bold; color: #6c757d;\">Type:</td>\n                  <td style=\"padding: 8px 0;\"><span style=\"background: #e3f2fd; color: #1976d2; padding: 4px 8px; border-radius: 4px; font-size: 12px;\">${inquiry.inquiry_type}</span></td>\n                </tr>\n                <tr>\n                  <td style=\"padding: 8px 0; font-weight: bold; color: #6c757d;\">Date:</td>\n                  <td style=\"padding: 8px 0;\">${new Date(inquiry.created_at).toLocaleString()}</td>\n                </tr>\n                <tr>\n                  <td style=\"padding: 8px 0; font-weight: bold; color: #6c757d;\">ID:</td>\n                  <td style=\"padding: 8px 0; font-family: monospace; background: #f8f9fa; padding: 4px 8px; border-radius: 4px;\">${inquiry.id}</td>\n                </tr>\n              </table>\n            </div>\n\n            <div style=\"background: white; padding: 25px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); margin-top: 20px;\">\n              <h2 style=\"color: #495057; margin-top: 0; border-bottom: 2px solid #e9ecef; padding-bottom: 10px;\">Message</h2>\n              <div style=\"background: #f8f9fa; padding: 15px; border-radius: 6px; border-left: 4px solid #007bff;\">\n                <p style=\"margin: 0; white-space: pre-wrap;\">${inquiry.message}</p>\n              </div>\n            </div>\n\n            <div style=\"text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #e9ecef;\">\n              <p style=\"color: #6c757d; font-size: 14px; margin: 0;\">\n                This inquiry was submitted through the Positive7 website contact form.\n              </p>\n            </div>\n          </div>\n        </body>\n        </html>\n      `\n        };\n        await transporter.sendMail(mailOptions);\n        console.log(`✅ Email notification sent successfully to ${adminEmail}`);\n        return true;\n    } catch (error) {\n        console.error(\"❌ Error sending email notification:\", error);\n        // Still log to console as fallback\n        console.log(\"\\uD83D\\uDCE7 Falling back to console logging:\");\n        console.log(\"━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\");\n        console.log(\"\\uD83D\\uDCCB NEW INQUIRY RECEIVED\");\n        console.log(\"━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\");\n        console.log(`👤 Name: ${inquiry.name}`);\n        console.log(`📧 Email: ${inquiry.email}`);\n        console.log(`📱 Phone: ${inquiry.phone || \"Not provided\"}`);\n        console.log(`📝 Subject: ${inquiry.subject || \"No subject\"}`);\n        console.log(`🏷️  Type: ${inquiry.inquiry_type}`);\n        console.log(`📅 Date: ${new Date(inquiry.created_at).toLocaleString()}`);\n        console.log(`🆔 ID: ${inquiry.id}`);\n        console.log(\"━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\");\n        console.log(\"\\uD83D\\uDCAC MESSAGE:\");\n        console.log(inquiry.message);\n        console.log(\"━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\");\n        return false;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/email.ts\n");

/***/ }),

/***/ "(rsc)/./lib/supabase-server.ts":
/*!********************************!*\
  !*** ./lib/supabase-server.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createClientSupabase: () => (/* binding */ createClientSupabase),\n/* harmony export */   createServerSupabase: () => (/* binding */ createServerSupabase)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\n// Create a server-side Supabase client\nfunction createServerSupabase() {\n    const supabaseUrl = \"https://soaoagcuubtzojytoati.supabase.co\";\n    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\n    if (!supabaseUrl || !supabaseServiceKey) {\n        throw new Error(\"Missing Supabase environment variables\");\n    }\n    return (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseServiceKey, {\n        auth: {\n            autoRefreshToken: false,\n            persistSession: false\n        }\n    });\n}\n// Create a client-side Supabase client\nfunction createClientSupabase() {\n    const supabaseUrl = \"https://soaoagcuubtzojytoati.supabase.co\";\n    const supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNvYW9hZ2N1dWJ0em9qeXRvYXRpIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg0ODQyOTksImV4cCI6MjA2NDA2MDI5OX0.h0NpruyXbMY9bN-RB_Ng_s_uscP6G3VW_R0rM91DtW0\";\n    if (!supabaseUrl || !supabaseAnonKey) {\n        throw new Error(\"Missing Supabase environment variables\");\n    }\n    return (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/supabase-server.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions","vendor-chunks/nodemailer"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Finquiries%2Froute&page=%2Fapi%2Finquiries%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Finquiries%2Froute.ts&appDir=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();