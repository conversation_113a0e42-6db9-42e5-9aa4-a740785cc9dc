"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/inquiries/route";
exports.ids = ["app/api/inquiries/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

module.exports = require("child_process");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "dns":
/*!**********************!*\
  !*** external "dns" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("dns");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("net");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Finquiries%2Froute&page=%2Fapi%2Finquiries%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Finquiries%2Froute.ts&appDir=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Finquiries%2Froute&page=%2Fapi%2Finquiries%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Finquiries%2Froute.ts&appDir=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_peebs_Documents_projects_p7_comprehensive_app_api_inquiries_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/inquiries/route.ts */ \"(rsc)/./app/api/inquiries/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/inquiries/route\",\n        pathname: \"/api/inquiries\",\n        filename: \"route\",\n        bundlePath: \"app/api/inquiries/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\api\\\\inquiries\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_peebs_Documents_projects_p7_comprehensive_app_api_inquiries_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/inquiries/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Finquiries%2Froute&page=%2Fapi%2Finquiries%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Finquiries%2Froute.ts&appDir=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/inquiries/route.ts":
/*!************************************!*\
  !*** ./app/api/inquiries/route.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var _lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase-server */ \"(rsc)/./lib/supabase-server.ts\");\n/* harmony import */ var _lib_email__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/email */ \"(rsc)/./lib/email.ts\");\n\n\n\n// GET /api/inquiries - Get inquiries (Admin only)\nasync function GET(request) {\n    try {\n        const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createServerSupabase)();\n        const { searchParams } = new URL(request.url);\n        // Parse query parameters\n        const page = parseInt(searchParams.get(\"page\") || \"1\");\n        const limit = parseInt(searchParams.get(\"limit\") || \"10\");\n        const status = searchParams.get(\"status\");\n        const inquiryType = searchParams.get(\"inquiryType\");\n        // Build query\n        let query = supabase.from(\"inquiries\").select(`\n        id,\n        name,\n        email,\n        phone,\n        subject,\n        message,\n        inquiry_type,\n        status,\n        created_at\n      `, {\n            count: \"exact\"\n        });\n        // Apply filters\n        if (status && status !== \"all\") {\n            query = query.eq(\"status\", status);\n        }\n        if (inquiryType && inquiryType !== \"all\") {\n            query = query.eq(\"inquiry_type\", inquiryType);\n        }\n        // Apply pagination\n        const from = (page - 1) * limit;\n        const to = from + limit - 1;\n        query = query.order(\"created_at\", {\n            ascending: false\n        }).range(from, to);\n        const { data: inquiries, error, count } = await query;\n        if (error) {\n            console.error(\"Error fetching inquiries:\", error);\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Failed to fetch inquiries\"\n            }, {\n                status: 500\n            });\n        }\n        const totalPages = Math.ceil((count || 0) / limit);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            data: inquiries || [],\n            pagination: {\n                page,\n                limit,\n                total: count || 0,\n                totalPages\n            }\n        });\n    } catch (error) {\n        console.error(\"Error in GET /api/inquiries:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n// POST /api/inquiries - Create a new inquiry\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        // Basic validation\n        const requiredFields = [\n            \"name\",\n            \"email\",\n            \"message\"\n        ];\n        for (const field of requiredFields){\n            if (!body[field]) {\n                return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                    error: `Missing required field: ${field}`\n                }, {\n                    status: 400\n                });\n            }\n        }\n        // Validate email format\n        const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n        if (!emailRegex.test(body.email)) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Invalid email format\"\n            }, {\n                status: 400\n            });\n        }\n        const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createServerSupabase)();\n        // Save inquiry to database\n        const { data: inquiry, error: dbError } = await supabase.from(\"inquiries\").insert({\n            name: body.name,\n            email: body.email,\n            phone: body.phone || null,\n            subject: body.subject || null,\n            message: body.message,\n            inquiry_type: body.inquiry_type || \"General Inquiry\",\n            status: \"new\"\n        }).select().single();\n        if (dbError) {\n            console.error(\"Error saving inquiry to database:\", dbError);\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Failed to save inquiry\"\n            }, {\n                status: 500\n            });\n        }\n        // Log the saved inquiry to terminal\n        console.log(\"\\uD83D\\uDCBE INQUIRY SAVED TO DATABASE:\");\n        console.log(\"━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\");\n        console.log(\"Database Row:\", JSON.stringify(inquiry, null, 2));\n        console.log(\"━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\");\n        // Send email notification\n        try {\n            await (0,_lib_email__WEBPACK_IMPORTED_MODULE_2__.sendInquiryNotification)(inquiry);\n        } catch (emailError) {\n            console.error(\"❌ Error sending email notification:\", emailError);\n        // Don't fail the request if email fails, just log it\n        }\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            message: \"Inquiry submitted successfully. We will get back to you soon!\",\n            data: inquiry\n        }, {\n            status: 201\n        });\n    } catch (error) {\n        console.error(\"Error in POST /api/inquiries:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/inquiries/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/email.ts":
/*!**********************!*\
  !*** ./lib/email.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   sendInquiryNotification: () => (/* binding */ sendInquiryNotification)\n/* harmony export */ });\n/* harmony import */ var nodemailer__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! nodemailer */ \"(rsc)/./node_modules/nodemailer/lib/nodemailer.js\");\n\n// Email configuration\nconst createTransporter = ()=>{\n    const emailService = process.env.EMAIL_SERVICE || \"gmail\";\n    const emailUser = process.env.EMAIL_USER;\n    const emailPassword = process.env.EMAIL_PASSWORD;\n    if (!emailUser || !emailPassword) {\n        console.log(\"Email credentials not configured. Emails will be logged to console only.\");\n        return null;\n    }\n    return nodemailer__WEBPACK_IMPORTED_MODULE_0__.createTransporter({\n        service: emailService,\n        auth: {\n            user: emailUser,\n            pass: emailPassword\n        }\n    });\n};\nasync function sendInquiryNotification(inquiry) {\n    const adminEmail = process.env.ADMIN_EMAIL;\n    if (!adminEmail) {\n        console.log(\"❌ ADMIN_EMAIL not configured, skipping email notification\");\n        return false;\n    }\n    const transporter = createTransporter();\n    if (!transporter) {\n        console.log(\"\\uD83D\\uDCE7 Email service not configured, logging inquiry details:\");\n        console.log(\"━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\");\n        console.log(\"\\uD83D\\uDCCB NEW INQUIRY RECEIVED\");\n        console.log(\"━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\");\n        console.log(`👤 Name: ${inquiry.name}`);\n        console.log(`📧 Email: ${inquiry.email}`);\n        console.log(`📱 Phone: ${inquiry.phone || \"Not provided\"}`);\n        console.log(`📝 Subject: ${inquiry.subject || \"No subject\"}`);\n        console.log(`🏷️  Type: ${inquiry.inquiry_type}`);\n        console.log(`📅 Date: ${new Date(inquiry.created_at).toLocaleString()}`);\n        console.log(`🆔 ID: ${inquiry.id}`);\n        console.log(\"━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\");\n        console.log(\"\\uD83D\\uDCAC MESSAGE:\");\n        console.log(inquiry.message);\n        console.log(\"━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\");\n        return false;\n    }\n    try {\n        const mailOptions = {\n            from: process.env.EMAIL_USER,\n            to: adminEmail,\n            subject: `New Inquiry: ${inquiry.inquiry_type} - ${inquiry.name}`,\n            html: `\n        <!DOCTYPE html>\n        <html>\n        <head>\n          <meta charset=\"utf-8\">\n          <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n          <title>New Inquiry - Positive7</title>\n        </head>\n        <body style=\"font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;\">\n          <div style=\"background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 10px 10px 0 0; text-align: center;\">\n            <h1 style=\"margin: 0; font-size: 28px;\">New Inquiry Received</h1>\n            <p style=\"margin: 10px 0 0 0; opacity: 0.9;\">Positive7 Educational Tours</p>\n          </div>\n          \n          <div style=\"background: #f8f9fa; padding: 30px; border-radius: 0 0 10px 10px; border: 1px solid #e9ecef;\">\n            <div style=\"background: white; padding: 25px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);\">\n              <h2 style=\"color: #495057; margin-top: 0; border-bottom: 2px solid #e9ecef; padding-bottom: 10px;\">Contact Information</h2>\n              \n              <table style=\"width: 100%; border-collapse: collapse;\">\n                <tr>\n                  <td style=\"padding: 8px 0; font-weight: bold; color: #6c757d; width: 120px;\">Name:</td>\n                  <td style=\"padding: 8px 0;\">${inquiry.name}</td>\n                </tr>\n                <tr>\n                  <td style=\"padding: 8px 0; font-weight: bold; color: #6c757d;\">Email:</td>\n                  <td style=\"padding: 8px 0;\"><a href=\"mailto:${inquiry.email}\" style=\"color: #007bff; text-decoration: none;\">${inquiry.email}</a></td>\n                </tr>\n                <tr>\n                  <td style=\"padding: 8px 0; font-weight: bold; color: #6c757d;\">Phone:</td>\n                  <td style=\"padding: 8px 0;\">${inquiry.phone || \"Not provided\"}</td>\n                </tr>\n                <tr>\n                  <td style=\"padding: 8px 0; font-weight: bold; color: #6c757d;\">Subject:</td>\n                  <td style=\"padding: 8px 0;\">${inquiry.subject || \"No subject\"}</td>\n                </tr>\n                <tr>\n                  <td style=\"padding: 8px 0; font-weight: bold; color: #6c757d;\">Type:</td>\n                  <td style=\"padding: 8px 0;\"><span style=\"background: #e3f2fd; color: #1976d2; padding: 4px 8px; border-radius: 4px; font-size: 12px;\">${inquiry.inquiry_type}</span></td>\n                </tr>\n                <tr>\n                  <td style=\"padding: 8px 0; font-weight: bold; color: #6c757d;\">Date:</td>\n                  <td style=\"padding: 8px 0;\">${new Date(inquiry.created_at).toLocaleString()}</td>\n                </tr>\n                <tr>\n                  <td style=\"padding: 8px 0; font-weight: bold; color: #6c757d;\">ID:</td>\n                  <td style=\"padding: 8px 0; font-family: monospace; background: #f8f9fa; padding: 4px 8px; border-radius: 4px;\">${inquiry.id}</td>\n                </tr>\n              </table>\n            </div>\n            \n            <div style=\"background: white; padding: 25px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); margin-top: 20px;\">\n              <h2 style=\"color: #495057; margin-top: 0; border-bottom: 2px solid #e9ecef; padding-bottom: 10px;\">Message</h2>\n              <div style=\"background: #f8f9fa; padding: 15px; border-radius: 6px; border-left: 4px solid #007bff;\">\n                <p style=\"margin: 0; white-space: pre-wrap;\">${inquiry.message}</p>\n              </div>\n            </div>\n            \n            <div style=\"text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #e9ecef;\">\n              <p style=\"color: #6c757d; font-size: 14px; margin: 0;\">\n                This inquiry was submitted through the Positive7 website contact form.\n              </p>\n            </div>\n          </div>\n        </body>\n        </html>\n      `\n        };\n        await transporter.sendMail(mailOptions);\n        console.log(`✅ Email notification sent successfully to ${adminEmail}`);\n        return true;\n    } catch (error) {\n        console.error(\"❌ Error sending email notification:\", error);\n        // Still log to console as fallback\n        console.log(\"\\uD83D\\uDCE7 Falling back to console logging:\");\n        console.log(\"━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\");\n        console.log(\"\\uD83D\\uDCCB NEW INQUIRY RECEIVED\");\n        console.log(\"━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\");\n        console.log(`👤 Name: ${inquiry.name}`);\n        console.log(`📧 Email: ${inquiry.email}`);\n        console.log(`📱 Phone: ${inquiry.phone || \"Not provided\"}`);\n        console.log(`📝 Subject: ${inquiry.subject || \"No subject\"}`);\n        console.log(`🏷️  Type: ${inquiry.inquiry_type}`);\n        console.log(`📅 Date: ${new Date(inquiry.created_at).toLocaleString()}`);\n        console.log(`🆔 ID: ${inquiry.id}`);\n        console.log(\"━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\");\n        console.log(\"\\uD83D\\uDCAC MESSAGE:\");\n        console.log(inquiry.message);\n        console.log(\"━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\");\n        return false;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/email.ts\n");

/***/ }),

/***/ "(rsc)/./lib/supabase-server.ts":
/*!********************************!*\
  !*** ./lib/supabase-server.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createClientSupabase: () => (/* binding */ createClientSupabase),\n/* harmony export */   createServerSupabase: () => (/* binding */ createServerSupabase)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\n// Create a server-side Supabase client\nfunction createServerSupabase() {\n    const supabaseUrl = \"https://soaoagcuubtzojytoati.supabase.co\";\n    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\n    if (!supabaseUrl || !supabaseServiceKey) {\n        throw new Error(\"Missing Supabase environment variables\");\n    }\n    return (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseServiceKey, {\n        auth: {\n            autoRefreshToken: false,\n            persistSession: false\n        }\n    });\n}\n// Create a client-side Supabase client\nfunction createClientSupabase() {\n    const supabaseUrl = \"https://soaoagcuubtzojytoati.supabase.co\";\n    const supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNvYW9hZ2N1dWJ0em9qeXRvYXRpIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg0ODQyOTksImV4cCI6MjA2NDA2MDI5OX0.h0NpruyXbMY9bN-RB_Ng_s_uscP6G3VW_R0rM91DtW0\";\n    if (!supabaseUrl || !supabaseAnonKey) {\n        throw new Error(\"Missing Supabase environment variables\");\n    }\n    return (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/supabase-server.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions","vendor-chunks/nodemailer"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Finquiries%2Froute&page=%2Fapi%2Finquiries%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Finquiries%2Froute.ts&appDir=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();