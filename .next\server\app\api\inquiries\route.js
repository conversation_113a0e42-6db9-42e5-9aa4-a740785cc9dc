"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/inquiries/route";
exports.ids = ["app/api/inquiries/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("net");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Finquiries%2Froute&page=%2Fapi%2Finquiries%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Finquiries%2Froute.ts&appDir=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Finquiries%2Froute&page=%2Fapi%2Finquiries%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Finquiries%2Froute.ts&appDir=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_peebs_Documents_projects_p7_comprehensive_app_api_inquiries_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/inquiries/route.ts */ \"(rsc)/./app/api/inquiries/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/inquiries/route\",\n        pathname: \"/api/inquiries\",\n        filename: \"route\",\n        bundlePath: \"app/api/inquiries/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\api\\\\inquiries\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_peebs_Documents_projects_p7_comprehensive_app_api_inquiries_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/inquiries/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Finquiries%2Froute&page=%2Fapi%2Finquiries%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Finquiries%2Froute.ts&appDir=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/inquiries/route.ts":
/*!************************************!*\
  !*** ./app/api/inquiries/route.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var _lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase-server */ \"(rsc)/./lib/supabase-server.ts\");\n\n\n// GET /api/inquiries - Get inquiries (Admin only)\nasync function GET(request) {\n    try {\n        const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createServerSupabase)();\n        const { searchParams } = new URL(request.url);\n        // Parse query parameters\n        const page = parseInt(searchParams.get(\"page\") || \"1\");\n        const limit = parseInt(searchParams.get(\"limit\") || \"10\");\n        const status = searchParams.get(\"status\");\n        const inquiryType = searchParams.get(\"inquiryType\");\n        // Build query\n        let query = supabase.from(\"inquiries\").select(`\n        id,\n        name,\n        email,\n        phone,\n        subject,\n        message,\n        inquiry_type,\n        status,\n        created_at\n      `, {\n            count: \"exact\"\n        });\n        // Apply filters\n        if (status && status !== \"all\") {\n            query = query.eq(\"status\", status);\n        }\n        if (inquiryType && inquiryType !== \"all\") {\n            query = query.eq(\"inquiry_type\", inquiryType);\n        }\n        // Apply pagination\n        const from = (page - 1) * limit;\n        const to = from + limit - 1;\n        query = query.order(\"created_at\", {\n            ascending: false\n        }).range(from, to);\n        const { data: inquiries, error, count } = await query;\n        if (error) {\n            console.error(\"Error fetching inquiries:\", error);\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Failed to fetch inquiries\"\n            }, {\n                status: 500\n            });\n        }\n        const totalPages = Math.ceil((count || 0) / limit);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            data: inquiries || [],\n            pagination: {\n                page,\n                limit,\n                total: count || 0,\n                totalPages\n            }\n        });\n    } catch (error) {\n        console.error(\"Error in GET /api/inquiries:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n// POST /api/inquiries - Create a new inquiry\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        // Basic validation\n        const requiredFields = [\n            \"name\",\n            \"email\",\n            \"message\"\n        ];\n        for (const field of requiredFields){\n            if (!body[field]) {\n                return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                    error: `Missing required field: ${field}`\n                }, {\n                    status: 400\n                });\n            }\n        }\n        // Validate email format\n        const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n        if (!emailRegex.test(body.email)) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Invalid email format\"\n            }, {\n                status: 400\n            });\n        }\n        const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createServerSupabase)();\n        // Save inquiry to database\n        const { data: inquiry, error: dbError } = await supabase.from(\"inquiries\").insert({\n            name: body.name,\n            email: body.email,\n            phone: body.phone || null,\n            subject: body.subject || null,\n            message: body.message,\n            inquiry_type: body.inquiry_type || \"General Inquiry\",\n            status: \"new\"\n        }).select().single();\n        if (dbError) {\n            console.error(\"Error saving inquiry to database:\", dbError);\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Failed to save inquiry\"\n            }, {\n                status: 500\n            });\n        }\n        // Send email notification (if email settings are configured)\n        try {\n            await sendInquiryEmail(inquiry);\n        } catch (emailError) {\n            console.error(\"Error sending email:\", emailError);\n        // Don't fail the request if email fails, just log it\n        }\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            message: \"Inquiry submitted successfully. We will get back to you soon!\",\n            data: inquiry\n        }, {\n            status: 201\n        });\n    } catch (error) {\n        console.error(\"Error in POST /api/inquiries:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n// Helper function to send inquiry email\nasync function sendInquiryEmail(inquiry) {\n    const adminEmail = process.env.ADMIN_EMAIL;\n    if (!adminEmail) {\n        console.log(\"ADMIN_EMAIL not configured, skipping email notification\");\n        return;\n    }\n    // Email content\n    const emailContent = `\n    New Inquiry Received - Positive7 Educational Tours\n\n    Name: ${inquiry.name}\n    Email: ${inquiry.email}\n    Phone: ${inquiry.phone || \"Not provided\"}\n    Subject: ${inquiry.subject || \"No subject\"}\n    Inquiry Type: ${inquiry.inquiry_type}\n\n    Message:\n    ${inquiry.message}\n\n    Submitted at: ${new Date(inquiry.created_at).toLocaleString()}\n    Inquiry ID: ${inquiry.id}\n  `;\n    // Here you would integrate with your email service (SendGrid, Nodemailer, etc.)\n    // For now, we'll just log the email content\n    console.log(\"Email notification would be sent to:\", adminEmail);\n    console.log(\"Email content:\", emailContent);\n// TODO: Implement actual email sending\n// Example with Nodemailer or SendGrid would go here\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvYXBpL2lucXVpcmllcy9yb3V0ZS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQXdEO0FBQ0s7QUFHN0Qsa0RBQWtEO0FBQzNDLGVBQWVFLElBQUlDLE9BQW9CO0lBQzVDLElBQUk7UUFDRixNQUFNQyxXQUFXSCwwRUFBb0JBO1FBQ3JDLE1BQU0sRUFBRUksWUFBWSxFQUFFLEdBQUcsSUFBSUMsSUFBSUgsUUFBUUksR0FBRztRQUU1Qyx5QkFBeUI7UUFDekIsTUFBTUMsT0FBT0MsU0FBU0osYUFBYUssR0FBRyxDQUFDLFdBQVc7UUFDbEQsTUFBTUMsUUFBUUYsU0FBU0osYUFBYUssR0FBRyxDQUFDLFlBQVk7UUFDcEQsTUFBTUUsU0FBU1AsYUFBYUssR0FBRyxDQUFDO1FBQ2hDLE1BQU1HLGNBQWNSLGFBQWFLLEdBQUcsQ0FBQztRQUVyQyxjQUFjO1FBQ2QsSUFBSUksUUFBUVYsU0FDVFcsSUFBSSxDQUFDLGFBQ0xDLE1BQU0sQ0FBQyxDQUFDOzs7Ozs7Ozs7O01BVVQsQ0FBQyxFQUFFO1lBQUVDLE9BQU87UUFBUTtRQUV0QixnQkFBZ0I7UUFDaEIsSUFBSUwsVUFBVUEsV0FBVyxPQUFPO1lBQzlCRSxRQUFRQSxNQUFNSSxFQUFFLENBQUMsVUFBVU47UUFDN0I7UUFDQSxJQUFJQyxlQUFlQSxnQkFBZ0IsT0FBTztZQUN4Q0MsUUFBUUEsTUFBTUksRUFBRSxDQUFDLGdCQUFnQkw7UUFDbkM7UUFFQSxtQkFBbUI7UUFDbkIsTUFBTUUsT0FBTyxDQUFDUCxPQUFPLEtBQUtHO1FBQzFCLE1BQU1RLEtBQUtKLE9BQU9KLFFBQVE7UUFFMUJHLFFBQVFBLE1BQ0xNLEtBQUssQ0FBQyxjQUFjO1lBQUVDLFdBQVc7UUFBTSxHQUN2Q0MsS0FBSyxDQUFDUCxNQUFNSTtRQUVmLE1BQU0sRUFBRUksTUFBTUMsU0FBUyxFQUFFQyxLQUFLLEVBQUVSLEtBQUssRUFBRSxHQUFHLE1BQU1IO1FBRWhELElBQUlXLE9BQU87WUFDVEMsUUFBUUQsS0FBSyxDQUFDLDZCQUE2QkE7WUFDM0MsT0FBT3pCLGtGQUFZQSxDQUFDMkIsSUFBSSxDQUN0QjtnQkFBRUYsT0FBTztZQUE0QixHQUNyQztnQkFBRWIsUUFBUTtZQUFJO1FBRWxCO1FBRUEsTUFBTWdCLGFBQWFDLEtBQUtDLElBQUksQ0FBQyxDQUFDYixTQUFTLEtBQUtOO1FBRTVDLE9BQU9YLGtGQUFZQSxDQUFDMkIsSUFBSSxDQUFDO1lBQ3ZCSixNQUFNQyxhQUFhLEVBQUU7WUFDckJPLFlBQVk7Z0JBQ1Z2QjtnQkFDQUc7Z0JBQ0FxQixPQUFPZixTQUFTO2dCQUNoQlc7WUFDRjtRQUNGO0lBQ0YsRUFBRSxPQUFPSCxPQUFPO1FBQ2RDLFFBQVFELEtBQUssQ0FBQyxnQ0FBZ0NBO1FBQzlDLE9BQU96QixrRkFBWUEsQ0FBQzJCLElBQUksQ0FDdEI7WUFBRUYsT0FBTztRQUF3QixHQUNqQztZQUFFYixRQUFRO1FBQUk7SUFFbEI7QUFDRjtBQUVBLDZDQUE2QztBQUN0QyxlQUFlcUIsS0FBSzlCLE9BQW9CO0lBQzdDLElBQUk7UUFDRixNQUFNK0IsT0FBMEIsTUFBTS9CLFFBQVF3QixJQUFJO1FBRWxELG1CQUFtQjtRQUNuQixNQUFNUSxpQkFBaUI7WUFBQztZQUFRO1lBQVM7U0FBVTtRQUNuRCxLQUFLLE1BQU1DLFNBQVNELGVBQWdCO1lBQ2xDLElBQUksQ0FBQ0QsSUFBSSxDQUFDRSxNQUFpQyxFQUFFO2dCQUMzQyxPQUFPcEMsa0ZBQVlBLENBQUMyQixJQUFJLENBQ3RCO29CQUFFRixPQUFPLENBQUMsd0JBQXdCLEVBQUVXLE1BQU0sQ0FBQztnQkFBQyxHQUM1QztvQkFBRXhCLFFBQVE7Z0JBQUk7WUFFbEI7UUFDRjtRQUVBLHdCQUF3QjtRQUN4QixNQUFNeUIsYUFBYTtRQUNuQixJQUFJLENBQUNBLFdBQVdDLElBQUksQ0FBQ0osS0FBS0ssS0FBSyxHQUFHO1lBQ2hDLE9BQU92QyxrRkFBWUEsQ0FBQzJCLElBQUksQ0FDdEI7Z0JBQUVGLE9BQU87WUFBdUIsR0FDaEM7Z0JBQUViLFFBQVE7WUFBSTtRQUVsQjtRQUVBLE1BQU1SLFdBQVdILDBFQUFvQkE7UUFFckMsMkJBQTJCO1FBQzNCLE1BQU0sRUFBRXNCLE1BQU1pQixPQUFPLEVBQUVmLE9BQU9nQixPQUFPLEVBQUUsR0FBRyxNQUFNckMsU0FDN0NXLElBQUksQ0FBQyxhQUNMMkIsTUFBTSxDQUFDO1lBQ05DLE1BQU1ULEtBQUtTLElBQUk7WUFDZkosT0FBT0wsS0FBS0ssS0FBSztZQUNqQkssT0FBT1YsS0FBS1UsS0FBSyxJQUFJO1lBQ3JCQyxTQUFTWCxLQUFLVyxPQUFPLElBQUk7WUFDekJDLFNBQVNaLEtBQUtZLE9BQU87WUFDckJDLGNBQWNiLEtBQUthLFlBQVksSUFBSTtZQUNuQ25DLFFBQVE7UUFDVixHQUNDSSxNQUFNLEdBQ05nQyxNQUFNO1FBRVQsSUFBSVAsU0FBUztZQUNYZixRQUFRRCxLQUFLLENBQUMscUNBQXFDZ0I7WUFDbkQsT0FBT3pDLGtGQUFZQSxDQUFDMkIsSUFBSSxDQUN0QjtnQkFBRUYsT0FBTztZQUF5QixHQUNsQztnQkFBRWIsUUFBUTtZQUFJO1FBRWxCO1FBRUEsNkRBQTZEO1FBQzdELElBQUk7WUFDRixNQUFNcUMsaUJBQWlCVDtRQUN6QixFQUFFLE9BQU9VLFlBQVk7WUFDbkJ4QixRQUFRRCxLQUFLLENBQUMsd0JBQXdCeUI7UUFDdEMscURBQXFEO1FBQ3ZEO1FBRUEsT0FBT2xELGtGQUFZQSxDQUFDMkIsSUFBSSxDQUFDO1lBQ3ZCbUIsU0FBUztZQUNUdkIsTUFBTWlCO1FBQ1IsR0FBRztZQUFFNUIsUUFBUTtRQUFJO0lBQ25CLEVBQUUsT0FBT2EsT0FBTztRQUNkQyxRQUFRRCxLQUFLLENBQUMsaUNBQWlDQTtRQUMvQyxPQUFPekIsa0ZBQVlBLENBQUMyQixJQUFJLENBQ3RCO1lBQUVGLE9BQU87UUFBd0IsR0FDakM7WUFBRWIsUUFBUTtRQUFJO0lBRWxCO0FBQ0Y7QUFFQSx3Q0FBd0M7QUFDeEMsZUFBZXFDLGlCQUFpQlQsT0FBWTtJQUMxQyxNQUFNVyxhQUFhQyxRQUFRQyxHQUFHLENBQUNDLFdBQVc7SUFFMUMsSUFBSSxDQUFDSCxZQUFZO1FBQ2Z6QixRQUFRNkIsR0FBRyxDQUFDO1FBQ1o7SUFDRjtJQUVBLGdCQUFnQjtJQUNoQixNQUFNQyxlQUFlLENBQUM7OztVQUdkLEVBQUVoQixRQUFRRyxJQUFJLENBQUM7V0FDZCxFQUFFSCxRQUFRRCxLQUFLLENBQUM7V0FDaEIsRUFBRUMsUUFBUUksS0FBSyxJQUFJLGVBQWU7YUFDaEMsRUFBRUosUUFBUUssT0FBTyxJQUFJLGFBQWE7a0JBQzdCLEVBQUVMLFFBQVFPLFlBQVksQ0FBQzs7O0lBR3JDLEVBQUVQLFFBQVFNLE9BQU8sQ0FBQzs7a0JBRUosRUFBRSxJQUFJVyxLQUFLakIsUUFBUWtCLFVBQVUsRUFBRUMsY0FBYyxHQUFHO2dCQUNsRCxFQUFFbkIsUUFBUW9CLEVBQUUsQ0FBQztFQUMzQixDQUFDO0lBRUQsZ0ZBQWdGO0lBQ2hGLDRDQUE0QztJQUM1Q2xDLFFBQVE2QixHQUFHLENBQUMsd0NBQXdDSjtJQUNwRHpCLFFBQVE2QixHQUFHLENBQUMsa0JBQWtCQztBQUU5Qix1Q0FBdUM7QUFDdkMsb0RBQW9EO0FBQ3REIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcG9zaXRpdmU3LXRvdXJpc20td2Vic2l0ZS8uL2FwcC9hcGkvaW5xdWlyaWVzL3JvdXRlLnRzP2E1NDciXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgTmV4dFJlcXVlc3QsIE5leHRSZXNwb25zZSB9IGZyb20gJ25leHQvc2VydmVyJztcbmltcG9ydCB7IGNyZWF0ZVNlcnZlclN1cGFiYXNlIH0gZnJvbSAnQC9saWIvc3VwYWJhc2Utc2VydmVyJztcbmltcG9ydCB0eXBlIHsgQ3JlYXRlSW5xdWlyeURhdGEgfSBmcm9tICdAL3R5cGVzL2RhdGFiYXNlJztcblxuLy8gR0VUIC9hcGkvaW5xdWlyaWVzIC0gR2V0IGlucXVpcmllcyAoQWRtaW4gb25seSlcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBHRVQocmVxdWVzdDogTmV4dFJlcXVlc3QpIHtcbiAgdHJ5IHtcbiAgICBjb25zdCBzdXBhYmFzZSA9IGNyZWF0ZVNlcnZlclN1cGFiYXNlKCk7XG4gICAgY29uc3QgeyBzZWFyY2hQYXJhbXMgfSA9IG5ldyBVUkwocmVxdWVzdC51cmwpO1xuXG4gICAgLy8gUGFyc2UgcXVlcnkgcGFyYW1ldGVyc1xuICAgIGNvbnN0IHBhZ2UgPSBwYXJzZUludChzZWFyY2hQYXJhbXMuZ2V0KCdwYWdlJykgfHwgJzEnKTtcbiAgICBjb25zdCBsaW1pdCA9IHBhcnNlSW50KHNlYXJjaFBhcmFtcy5nZXQoJ2xpbWl0JykgfHwgJzEwJyk7XG4gICAgY29uc3Qgc3RhdHVzID0gc2VhcmNoUGFyYW1zLmdldCgnc3RhdHVzJyk7XG4gICAgY29uc3QgaW5xdWlyeVR5cGUgPSBzZWFyY2hQYXJhbXMuZ2V0KCdpbnF1aXJ5VHlwZScpO1xuXG4gICAgLy8gQnVpbGQgcXVlcnlcbiAgICBsZXQgcXVlcnkgPSBzdXBhYmFzZVxuICAgICAgLmZyb20oJ2lucXVpcmllcycpXG4gICAgICAuc2VsZWN0KGBcbiAgICAgICAgaWQsXG4gICAgICAgIG5hbWUsXG4gICAgICAgIGVtYWlsLFxuICAgICAgICBwaG9uZSxcbiAgICAgICAgc3ViamVjdCxcbiAgICAgICAgbWVzc2FnZSxcbiAgICAgICAgaW5xdWlyeV90eXBlLFxuICAgICAgICBzdGF0dXMsXG4gICAgICAgIGNyZWF0ZWRfYXRcbiAgICAgIGAsIHsgY291bnQ6ICdleGFjdCcgfSk7XG5cbiAgICAvLyBBcHBseSBmaWx0ZXJzXG4gICAgaWYgKHN0YXR1cyAmJiBzdGF0dXMgIT09ICdhbGwnKSB7XG4gICAgICBxdWVyeSA9IHF1ZXJ5LmVxKCdzdGF0dXMnLCBzdGF0dXMpO1xuICAgIH1cbiAgICBpZiAoaW5xdWlyeVR5cGUgJiYgaW5xdWlyeVR5cGUgIT09ICdhbGwnKSB7XG4gICAgICBxdWVyeSA9IHF1ZXJ5LmVxKCdpbnF1aXJ5X3R5cGUnLCBpbnF1aXJ5VHlwZSk7XG4gICAgfVxuXG4gICAgLy8gQXBwbHkgcGFnaW5hdGlvblxuICAgIGNvbnN0IGZyb20gPSAocGFnZSAtIDEpICogbGltaXQ7XG4gICAgY29uc3QgdG8gPSBmcm9tICsgbGltaXQgLSAxO1xuXG4gICAgcXVlcnkgPSBxdWVyeVxuICAgICAgLm9yZGVyKCdjcmVhdGVkX2F0JywgeyBhc2NlbmRpbmc6IGZhbHNlIH0pXG4gICAgICAucmFuZ2UoZnJvbSwgdG8pO1xuXG4gICAgY29uc3QgeyBkYXRhOiBpbnF1aXJpZXMsIGVycm9yLCBjb3VudCB9ID0gYXdhaXQgcXVlcnk7XG5cbiAgICBpZiAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGZldGNoaW5nIGlucXVpcmllczonLCBlcnJvcik7XG4gICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oXG4gICAgICAgIHsgZXJyb3I6ICdGYWlsZWQgdG8gZmV0Y2ggaW5xdWlyaWVzJyB9LFxuICAgICAgICB7IHN0YXR1czogNTAwIH1cbiAgICAgICk7XG4gICAgfVxuXG4gICAgY29uc3QgdG90YWxQYWdlcyA9IE1hdGguY2VpbCgoY291bnQgfHwgMCkgLyBsaW1pdCk7XG5cbiAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oe1xuICAgICAgZGF0YTogaW5xdWlyaWVzIHx8IFtdLFxuICAgICAgcGFnaW5hdGlvbjoge1xuICAgICAgICBwYWdlLFxuICAgICAgICBsaW1pdCxcbiAgICAgICAgdG90YWw6IGNvdW50IHx8IDAsXG4gICAgICAgIHRvdGFsUGFnZXMsXG4gICAgICB9LFxuICAgIH0pO1xuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGluIEdFVCAvYXBpL2lucXVpcmllczonLCBlcnJvcik7XG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFxuICAgICAgeyBlcnJvcjogJ0ludGVybmFsIHNlcnZlciBlcnJvcicgfSxcbiAgICAgIHsgc3RhdHVzOiA1MDAgfVxuICAgICk7XG4gIH1cbn1cblxuLy8gUE9TVCAvYXBpL2lucXVpcmllcyAtIENyZWF0ZSBhIG5ldyBpbnF1aXJ5XG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gUE9TVChyZXF1ZXN0OiBOZXh0UmVxdWVzdCkge1xuICB0cnkge1xuICAgIGNvbnN0IGJvZHk6IENyZWF0ZUlucXVpcnlEYXRhID0gYXdhaXQgcmVxdWVzdC5qc29uKCk7XG5cbiAgICAvLyBCYXNpYyB2YWxpZGF0aW9uXG4gICAgY29uc3QgcmVxdWlyZWRGaWVsZHMgPSBbJ25hbWUnLCAnZW1haWwnLCAnbWVzc2FnZSddO1xuICAgIGZvciAoY29uc3QgZmllbGQgb2YgcmVxdWlyZWRGaWVsZHMpIHtcbiAgICAgIGlmICghYm9keVtmaWVsZCBhcyBrZXlvZiBDcmVhdGVJbnF1aXJ5RGF0YV0pIHtcbiAgICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFxuICAgICAgICAgIHsgZXJyb3I6IGBNaXNzaW5nIHJlcXVpcmVkIGZpZWxkOiAke2ZpZWxkfWAgfSxcbiAgICAgICAgICB7IHN0YXR1czogNDAwIH1cbiAgICAgICAgKTtcbiAgICAgIH1cbiAgICB9XG5cbiAgICAvLyBWYWxpZGF0ZSBlbWFpbCBmb3JtYXRcbiAgICBjb25zdCBlbWFpbFJlZ2V4ID0gL15bXlxcc0BdK0BbXlxcc0BdK1xcLlteXFxzQF0rJC87XG4gICAgaWYgKCFlbWFpbFJlZ2V4LnRlc3QoYm9keS5lbWFpbCkpIHtcbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgICAgeyBlcnJvcjogJ0ludmFsaWQgZW1haWwgZm9ybWF0JyB9LFxuICAgICAgICB7IHN0YXR1czogNDAwIH1cbiAgICAgICk7XG4gICAgfVxuXG4gICAgY29uc3Qgc3VwYWJhc2UgPSBjcmVhdGVTZXJ2ZXJTdXBhYmFzZSgpO1xuXG4gICAgLy8gU2F2ZSBpbnF1aXJ5IHRvIGRhdGFiYXNlXG4gICAgY29uc3QgeyBkYXRhOiBpbnF1aXJ5LCBlcnJvcjogZGJFcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAgIC5mcm9tKCdpbnF1aXJpZXMnKVxuICAgICAgLmluc2VydCh7XG4gICAgICAgIG5hbWU6IGJvZHkubmFtZSxcbiAgICAgICAgZW1haWw6IGJvZHkuZW1haWwsXG4gICAgICAgIHBob25lOiBib2R5LnBob25lIHx8IG51bGwsXG4gICAgICAgIHN1YmplY3Q6IGJvZHkuc3ViamVjdCB8fCBudWxsLFxuICAgICAgICBtZXNzYWdlOiBib2R5Lm1lc3NhZ2UsXG4gICAgICAgIGlucXVpcnlfdHlwZTogYm9keS5pbnF1aXJ5X3R5cGUgfHwgJ0dlbmVyYWwgSW5xdWlyeScsXG4gICAgICAgIHN0YXR1czogJ25ldydcbiAgICAgIH0pXG4gICAgICAuc2VsZWN0KClcbiAgICAgIC5zaW5nbGUoKTtcblxuICAgIGlmIChkYkVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBzYXZpbmcgaW5xdWlyeSB0byBkYXRhYmFzZTonLCBkYkVycm9yKTtcbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgICAgeyBlcnJvcjogJ0ZhaWxlZCB0byBzYXZlIGlucXVpcnknIH0sXG4gICAgICAgIHsgc3RhdHVzOiA1MDAgfVxuICAgICAgKTtcbiAgICB9XG5cbiAgICAvLyBTZW5kIGVtYWlsIG5vdGlmaWNhdGlvbiAoaWYgZW1haWwgc2V0dGluZ3MgYXJlIGNvbmZpZ3VyZWQpXG4gICAgdHJ5IHtcbiAgICAgIGF3YWl0IHNlbmRJbnF1aXJ5RW1haWwoaW5xdWlyeSk7XG4gICAgfSBjYXRjaCAoZW1haWxFcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3Igc2VuZGluZyBlbWFpbDonLCBlbWFpbEVycm9yKTtcbiAgICAgIC8vIERvbid0IGZhaWwgdGhlIHJlcXVlc3QgaWYgZW1haWwgZmFpbHMsIGp1c3QgbG9nIGl0XG4gICAgfVxuXG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHtcbiAgICAgIG1lc3NhZ2U6ICdJbnF1aXJ5IHN1Ym1pdHRlZCBzdWNjZXNzZnVsbHkuIFdlIHdpbGwgZ2V0IGJhY2sgdG8geW91IHNvb24hJyxcbiAgICAgIGRhdGE6IGlucXVpcnlcbiAgICB9LCB7IHN0YXR1czogMjAxIH0pO1xuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGluIFBPU1QgL2FwaS9pbnF1aXJpZXM6JywgZXJyb3IpO1xuICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgIHsgZXJyb3I6ICdJbnRlcm5hbCBzZXJ2ZXIgZXJyb3InIH0sXG4gICAgICB7IHN0YXR1czogNTAwIH1cbiAgICApO1xuICB9XG59XG5cbi8vIEhlbHBlciBmdW5jdGlvbiB0byBzZW5kIGlucXVpcnkgZW1haWxcbmFzeW5jIGZ1bmN0aW9uIHNlbmRJbnF1aXJ5RW1haWwoaW5xdWlyeTogYW55KSB7XG4gIGNvbnN0IGFkbWluRW1haWwgPSBwcm9jZXNzLmVudi5BRE1JTl9FTUFJTDtcblxuICBpZiAoIWFkbWluRW1haWwpIHtcbiAgICBjb25zb2xlLmxvZygnQURNSU5fRU1BSUwgbm90IGNvbmZpZ3VyZWQsIHNraXBwaW5nIGVtYWlsIG5vdGlmaWNhdGlvbicpO1xuICAgIHJldHVybjtcbiAgfVxuXG4gIC8vIEVtYWlsIGNvbnRlbnRcbiAgY29uc3QgZW1haWxDb250ZW50ID0gYFxuICAgIE5ldyBJbnF1aXJ5IFJlY2VpdmVkIC0gUG9zaXRpdmU3IEVkdWNhdGlvbmFsIFRvdXJzXG5cbiAgICBOYW1lOiAke2lucXVpcnkubmFtZX1cbiAgICBFbWFpbDogJHtpbnF1aXJ5LmVtYWlsfVxuICAgIFBob25lOiAke2lucXVpcnkucGhvbmUgfHwgJ05vdCBwcm92aWRlZCd9XG4gICAgU3ViamVjdDogJHtpbnF1aXJ5LnN1YmplY3QgfHwgJ05vIHN1YmplY3QnfVxuICAgIElucXVpcnkgVHlwZTogJHtpbnF1aXJ5LmlucXVpcnlfdHlwZX1cblxuICAgIE1lc3NhZ2U6XG4gICAgJHtpbnF1aXJ5Lm1lc3NhZ2V9XG5cbiAgICBTdWJtaXR0ZWQgYXQ6ICR7bmV3IERhdGUoaW5xdWlyeS5jcmVhdGVkX2F0KS50b0xvY2FsZVN0cmluZygpfVxuICAgIElucXVpcnkgSUQ6ICR7aW5xdWlyeS5pZH1cbiAgYDtcblxuICAvLyBIZXJlIHlvdSB3b3VsZCBpbnRlZ3JhdGUgd2l0aCB5b3VyIGVtYWlsIHNlcnZpY2UgKFNlbmRHcmlkLCBOb2RlbWFpbGVyLCBldGMuKVxuICAvLyBGb3Igbm93LCB3ZSdsbCBqdXN0IGxvZyB0aGUgZW1haWwgY29udGVudFxuICBjb25zb2xlLmxvZygnRW1haWwgbm90aWZpY2F0aW9uIHdvdWxkIGJlIHNlbnQgdG86JywgYWRtaW5FbWFpbCk7XG4gIGNvbnNvbGUubG9nKCdFbWFpbCBjb250ZW50OicsIGVtYWlsQ29udGVudCk7XG5cbiAgLy8gVE9ETzogSW1wbGVtZW50IGFjdHVhbCBlbWFpbCBzZW5kaW5nXG4gIC8vIEV4YW1wbGUgd2l0aCBOb2RlbWFpbGVyIG9yIFNlbmRHcmlkIHdvdWxkIGdvIGhlcmVcbn1cbiJdLCJuYW1lcyI6WyJOZXh0UmVzcG9uc2UiLCJjcmVhdGVTZXJ2ZXJTdXBhYmFzZSIsIkdFVCIsInJlcXVlc3QiLCJzdXBhYmFzZSIsInNlYXJjaFBhcmFtcyIsIlVSTCIsInVybCIsInBhZ2UiLCJwYXJzZUludCIsImdldCIsImxpbWl0Iiwic3RhdHVzIiwiaW5xdWlyeVR5cGUiLCJxdWVyeSIsImZyb20iLCJzZWxlY3QiLCJjb3VudCIsImVxIiwidG8iLCJvcmRlciIsImFzY2VuZGluZyIsInJhbmdlIiwiZGF0YSIsImlucXVpcmllcyIsImVycm9yIiwiY29uc29sZSIsImpzb24iLCJ0b3RhbFBhZ2VzIiwiTWF0aCIsImNlaWwiLCJwYWdpbmF0aW9uIiwidG90YWwiLCJQT1NUIiwiYm9keSIsInJlcXVpcmVkRmllbGRzIiwiZmllbGQiLCJlbWFpbFJlZ2V4IiwidGVzdCIsImVtYWlsIiwiaW5xdWlyeSIsImRiRXJyb3IiLCJpbnNlcnQiLCJuYW1lIiwicGhvbmUiLCJzdWJqZWN0IiwibWVzc2FnZSIsImlucXVpcnlfdHlwZSIsInNpbmdsZSIsInNlbmRJbnF1aXJ5RW1haWwiLCJlbWFpbEVycm9yIiwiYWRtaW5FbWFpbCIsInByb2Nlc3MiLCJlbnYiLCJBRE1JTl9FTUFJTCIsImxvZyIsImVtYWlsQ29udGVudCIsIkRhdGUiLCJjcmVhdGVkX2F0IiwidG9Mb2NhbGVTdHJpbmciLCJpZCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/api/inquiries/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/supabase-server.ts":
/*!********************************!*\
  !*** ./lib/supabase-server.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createClientSupabase: () => (/* binding */ createClientSupabase),\n/* harmony export */   createServerSupabase: () => (/* binding */ createServerSupabase)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\n// Create a server-side Supabase client\nfunction createServerSupabase() {\n    const supabaseUrl = \"https://soaoagcuubtzojytoati.supabase.co\";\n    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\n    if (!supabaseUrl || !supabaseServiceKey) {\n        throw new Error(\"Missing Supabase environment variables\");\n    }\n    return (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseServiceKey, {\n        auth: {\n            autoRefreshToken: false,\n            persistSession: false\n        }\n    });\n}\n// Create a client-side Supabase client\nfunction createClientSupabase() {\n    const supabaseUrl = \"https://soaoagcuubtzojytoati.supabase.co\";\n    const supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNvYW9hZ2N1dWJ0em9qeXRvYXRpIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg0ODQyOTksImV4cCI6MjA2NDA2MDI5OX0.h0NpruyXbMY9bN-RB_Ng_s_uscP6G3VW_R0rM91DtW0\";\n    if (!supabaseUrl || !supabaseAnonKey) {\n        throw new Error(\"Missing Supabase environment variables\");\n    }\n    return (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/supabase-server.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Finquiries%2Froute&page=%2Fapi%2Finquiries%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Finquiries%2Froute.ts&appDir=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();