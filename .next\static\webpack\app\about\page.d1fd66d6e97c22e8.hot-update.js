"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/about/page",{

/***/ "(app-pages-browser)/./components/about/TeamSection.tsx":
/*!******************************************!*\
  !*** ./components/about/TeamSection.tsx ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TeamSection: function() { return /* binding */ TeamSection; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_Linkedin_Mail_MapPin_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,Linkedin,Mail,MapPin,Phone,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_Linkedin_Mail_MapPin_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,Linkedin,Mail,MapPin,Phone,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_Linkedin_Mail_MapPin_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,Linkedin,Mail,MapPin,Phone,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_Linkedin_Mail_MapPin_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,Linkedin,Mail,MapPin,Phone,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_Linkedin_Mail_MapPin_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,Linkedin,Mail,MapPin,Phone,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/linkedin.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_Linkedin_Mail_MapPin_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,Linkedin,Mail,MapPin,Phone,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_Linkedin_Mail_MapPin_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,Linkedin,Mail,MapPin,Phone,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* __next_internal_client_entry_do_not_use__ TeamSection auto */ \n\n\nconst TEAM_MEMBERS = [\n    {\n        name: \"Sunil Joseph\",\n        role: \"Director Operations\",\n        bio: \"Being born and raised in Rajasthan, his love and passion for the outdoor was built at the early age of 7. He was a good athlete and represented Nationals and Western Railways. Later he moved to Ahmedabad and worked for Dalmia Group and HFCL group. But his passion for traveling was intact and he quit his job to start with the job of his dreams. Today he travels for almost 20 days a month either on student trips or exploring new destinations.\",\n        image: \"/images/team/sunil-joseph.jpg\"\n    },\n    {\n        name: \"Sharif Mansuri\",\n        role: \"Director Administration\",\n        bio: \"Sharif is born and brought up in Ahmedabad, he did his master's in Environmental science from Gujarat University and made his career in the telecom industry, and worked for 10+ years. But his flair for nature and wildlife instigated him to leave his career in telecom and persuade his dream to explore his unfulfilled desire for nature. His crisis management skill is commendable and his helpful nature has been expressed in the form of social service.\",\n        image: \"/images/team/sharif-mansuri.jpg\"\n    },\n    {\n        name: \"Steve Everett\",\n        role: \"Director Client Relations\",\n        bio: \"Hailing from the city of Ajmer in Rajasthan, his passion for traveling and love for nature started in his early teens. He was a star athlete and won the best athlete award in school and college. His love for travel drove him to join the Airlines and started his career with Damania Airways then later with Jet Airways and as station head with SpiceJet. With a master's degree in English Literature, his interpersonal and communication skills have given him an added advantage.\",\n        image: \"/images/team/steve-everett.jpg\"\n    },\n    {\n        name: \"Arth Thakur\",\n        role: \"Team Management & Marketing Executive\",\n        bio: \"Arth was born and raised in Ahmedabad. He was mischievous during his younger days but creative and never left a chance to show his creativity. He was in different sports and he had immense love for dance. He has done his graduation with BCA in Design (Animations) and has expertise in Digital Marketing. His love for traveling made him join many trips with Positive7 as a volunteer and after many successful years, he joined Positive7 as Tour Manager.\",\n        image: \"/images/team/arth-thakur.jpg\"\n    }\n];\nfunction TeamSection() {\n    const containerVariants = {\n        hidden: {\n            opacity: 0\n        },\n        visible: {\n            opacity: 1,\n            transition: {\n                staggerChildren: 0.1\n            }\n        }\n    };\n    const itemVariants = {\n        hidden: {\n            opacity: 0,\n            y: 20\n        },\n        visible: {\n            opacity: 1,\n            y: 0\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"py-20\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-4xl font-bold text-gray-900 mb-4\",\n                            children: \"Meet Our Team\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                            lineNumber: 62,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-gray-600 max-w-3xl mx-auto\",\n                            children: \"Our passionate team of educators, adventure specialists, and travel experts work together to create unforgettable learning experiences for every student\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                            lineNumber: 63,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                    lineNumber: 61,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                    variants: containerVariants,\n                    initial: \"hidden\",\n                    whileInView: \"visible\",\n                    viewport: {\n                        once: true\n                    },\n                    className: \"grid md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                    children: TEAM_MEMBERS.map((member, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                            variants: itemVariants,\n                            className: \"group\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 h-full\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative w-32 h-32 mx-auto mb-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full h-full bg-gradient-to-r from-blue-100 to-green-100 rounded-full flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_Linkedin_Mail_MapPin_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                className: \"w-16 h-16 text-gray-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                                lineNumber: 86,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                            lineNumber: 85,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                        lineNumber: 84,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-bold text-gray-900 mb-2\",\n                                                children: member.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                                lineNumber: 99,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-blue-600 font-medium mb-3\",\n                                                children: member.role\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                                lineNumber: 100,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 text-sm leading-relaxed\",\n                                                children: member.bio\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                                lineNumber: 101,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-sm font-semibold text-gray-900 mb-3\",\n                                                children: \"Expertise:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                                lineNumber: 106,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-wrap gap-2\",\n                                                children: member.expertise.map((skill, skillIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"px-3 py-1 bg-blue-100 text-blue-700 text-xs rounded-full\",\n                                                        children: skill\n                                                    }, skillIndex, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                                        lineNumber: 109,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                                lineNumber: 107,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                        lineNumber: 105,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-sm font-semibold text-gray-900 mb-3 flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_Linkedin_Mail_MapPin_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                                        lineNumber: 122,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"Key Achievements:\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                                lineNumber: 121,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"space-y-1\",\n                                                children: member.achievements.map((achievement, achievementIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        className: \"text-xs text-gray-600 flex items-start gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-1 h-1 bg-green-500 rounded-full mt-2 flex-shrink-0\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                                                lineNumber: 128,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: achievement\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                                                lineNumber: 129,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, achievementIndex, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                                        lineNumber: 127,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                                lineNumber: 125,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                        lineNumber: 120,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"pt-4 border-t border-gray-200\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-center gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"mailto:\".concat(member.contact.email),\n                                                    className: \"p-2 bg-gray-100 rounded-full hover:bg-blue-100 hover:text-blue-600 transition-colors\",\n                                                    title: \"Email\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_Linkedin_Mail_MapPin_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                                        lineNumber: 143,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                                    lineNumber: 138,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"tel:\".concat(member.contact.phone),\n                                                    className: \"p-2 bg-gray-100 rounded-full hover:bg-green-100 hover:text-green-600 transition-colors\",\n                                                    title: \"Phone\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_Linkedin_Mail_MapPin_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                                        lineNumber: 150,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                                    lineNumber: 145,\n                                                    columnNumber: 21\n                                                }, this),\n                                                member.contact.linkedin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: member.contact.linkedin,\n                                                    target: \"_blank\",\n                                                    rel: \"noopener noreferrer\",\n                                                    className: \"p-2 bg-gray-100 rounded-full hover:bg-blue-100 hover:text-blue-600 transition-colors\",\n                                                    title: \"LinkedIn\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_Linkedin_Mail_MapPin_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                                        lineNumber: 160,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                                    lineNumber: 153,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                            lineNumber: 137,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                lineNumber: 82,\n                                columnNumber: 15\n                            }, this)\n                        }, index, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                            lineNumber: 77,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                    lineNumber: 69,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-16 bg-gradient-to-r from-blue-50 to-green-50 rounded-2xl p-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-2xl font-bold text-gray-900 mb-4\",\n                                    children: \"Our Team Culture\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                    lineNumber: 173,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-700 max-w-3xl mx-auto\",\n                                    children: \"We believe that a passionate, diverse, and dedicated team is the foundation of exceptional educational experiences.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                    lineNumber: 174,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                            lineNumber: 172,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid md:grid-cols-4 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_Linkedin_Mail_MapPin_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"w-6 h-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                                lineNumber: 182,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                            lineNumber: 181,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-semibold text-gray-900 mb-2\",\n                                            children: \"Continuous Learning\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                            lineNumber: 184,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"We invest in our team's growth and development\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                            lineNumber: 185,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                    lineNumber: 180,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-green-600 rounded-full flex items-center justify-center mx-auto mb-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_Linkedin_Mail_MapPin_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                className: \"w-6 h-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                                lineNumber: 190,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                            lineNumber: 189,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-semibold text-gray-900 mb-2\",\n                                            children: \"Collaboration\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                            lineNumber: 192,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"We work together to achieve common goals\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                            lineNumber: 193,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-purple-600 rounded-full flex items-center justify-center mx-auto mb-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_Linkedin_Mail_MapPin_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                className: \"w-6 h-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                                lineNumber: 198,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                            lineNumber: 197,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-semibold text-gray-900 mb-2\",\n                                            children: \"Excellence\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                            lineNumber: 200,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"We strive for the highest standards in everything\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                            lineNumber: 201,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                    lineNumber: 196,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-orange-600 rounded-full flex items-center justify-center mx-auto mb-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_Linkedin_Mail_MapPin_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-6 h-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                                lineNumber: 206,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                            lineNumber: 205,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-semibold text-gray-900 mb-2\",\n                                            children: \"Adventure Spirit\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                            lineNumber: 208,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"We embrace challenges and new experiences\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                            lineNumber: 209,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                    lineNumber: 204,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                            lineNumber: 179,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                    lineNumber: 171,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n            lineNumber: 60,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n        lineNumber: 59,\n        columnNumber: 5\n    }, this);\n}\n_c = TeamSection;\nvar _c;\n$RefreshReg$(_c, \"TeamSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/about/TeamSection.tsx\n"));

/***/ })

});