<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Client Auth</title>
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
</head>
<body>
    <h1>Test Supabase Authentication</h1>
    
    <div id="auth-section">
        <h2>Login</h2>
        <input type="email" id="email" placeholder="Email" value="<EMAIL>">
        <input type="password" id="password" placeholder="Password" value="customer123456">
        <button onclick="testLogin()">Login</button>
        <button onclick="testLogout()">Logout</button>
        <button onclick="checkSession()">Check Session</button>
    </div>
    
    <div id="result"></div>

    <script>
        // Initialize Supabase client
        const supabaseUrl = 'https://soaoagcuubtzojytoati.supabase.co';
        const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNvYW9hZ2N1dWJ0em9qeXRvYXRpIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg0ODQyOTksImV4cCI6MjA2NDA2MDI5OX0.h0NpruyXbMY9bN-RB_Ng_s_uscP6G3VW_R0rM91DtW0';
        const supabase = window.supabase.createClient(supabaseUrl, supabaseKey);

        function log(message) {
            const result = document.getElementById('result');
            result.innerHTML += '<p>' + new Date().toLocaleTimeString() + ': ' + message + '</p>';
            console.log(message);
        }

        async function testLogin() {
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            log('🔄 Attempting login...');
            
            try {
                const { data, error } = await supabase.auth.signInWithPassword({
                    email: email,
                    password: password
                });

                if (error) {
                    log('❌ Login failed: ' + error.message);
                } else {
                    log('✅ Login successful! User ID: ' + data.user.id);
                    
                    // Try to fetch user profile
                    const { data: profile, error: profileError } = await supabase
                        .from('users')
                        .select('*')
                        .eq('id', data.user.id)
                        .single();
                    
                    if (profileError) {
                        log('❌ Profile fetch failed: ' + profileError.message);
                    } else {
                        log('✅ Profile fetched: ' + profile.full_name + ' (' + profile.role + ')');
                    }
                }
            } catch (err) {
                log('❌ Login error: ' + err.message);
            }
        }

        async function testLogout() {
            log('🔄 Attempting logout...');
            
            try {
                const { error } = await supabase.auth.signOut();
                
                if (error) {
                    log('❌ Logout failed: ' + error.message);
                } else {
                    log('✅ Logout successful!');
                }
            } catch (err) {
                log('❌ Logout error: ' + err.message);
            }
        }

        async function checkSession() {
            log('🔄 Checking session...');
            
            try {
                const { data: { session }, error } = await supabase.auth.getSession();
                
                if (error) {
                    log('❌ Session check failed: ' + error.message);
                } else if (session) {
                    log('✅ Active session found for user: ' + session.user.email);
                } else {
                    log('ℹ️ No active session');
                }
            } catch (err) {
                log('❌ Session check error: ' + err.message);
            }
        }

        // Listen for auth state changes
        supabase.auth.onAuthStateChange((event, session) => {
            log('🔔 Auth state changed: ' + event);
            if (session) {
                log('   User: ' + session.user.email);
            }
        });

        // Check initial session
        checkSession();
    </script>
</body>
</html>
