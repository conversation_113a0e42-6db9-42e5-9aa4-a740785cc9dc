'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import { createClientSupabase } from '@/lib/supabase-client';
import type { User } from '@/types/database';
import type { Session } from '@supabase/supabase-js';

interface AuthContextType {
  user: User | null;
  session: Session | null;
  loading: boolean;
  signUp: (email: string, password: string, userData: Partial<User>) => Promise<{ data: any; error: string | null }>;
  signIn: (email: string, password: string) => Promise<{ data: any; error: string | null }>;
  signInWithGoogle: () => Promise<{ data: any; error: string | null }>;
  signOut: () => Promise<{ error: string | null }>;
  resetPassword: (email: string) => Promise<{ error: string | null }>;
  updateProfile: (updates: Partial<User>) => Promise<{ data: User | null; error: string | null }>;
  refreshUser: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

interface AuthProviderProps {
  children: React.ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [loading, setLoading] = useState(true);
  const supabase = createClientSupabase();

  // Fetch user profile from database
  const fetchUserProfile = async (userId: string): Promise<User | null> => {
    try {
      const { data, error } = await supabase
        .from('users')
        .select('*')
        .eq('id', userId)
        .single();

      if (error) {
        console.error('AuthContext: Error fetching user profile:', error);
        return null;
      }

      return data as User;
    } catch (error) {
      console.error('AuthContext: Error fetching user profile:', error);
      return null;
    }
  };

  // Initialize auth state
  useEffect(() => {
    const initializeAuth = async () => {
      console.log('AuthContext: Initializing auth');

      // Get initial session
      const { data: { session: initialSession } } = await supabase.auth.getSession();

      if (initialSession) {
        setSession(initialSession);
        const userProfile = await fetchUserProfile(initialSession.user.id);
        setUser(userProfile);
      }

      setLoading(false);
    };

    initializeAuth();

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(async (event, session) => {
      console.log('AuthContext: Auth state changed:', event);

      setSession(session);

      if (session) {
        const userProfile = await fetchUserProfile(session.user.id);
        setUser(userProfile);
      } else {
        setUser(null);
      }
    });

    return () => subscription.unsubscribe();
  }, []);

  // Sign up function
  const signUp = async (email: string, password: string, userData: Partial<User>) => {
    try {
      console.log('AuthContext: Attempting signup');

      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            full_name: userData.full_name,
            phone: userData.phone,
          },
        },
      });

      if (error) {
        console.error('AuthContext: Signup error:', error);
        return { data: null, error: error.message };
      }

      if (!data.user) {
        return { data: null, error: 'Failed to create user account' };
      }

      // Create user profile in the database
      const { error: profileError } = await supabase
        .from('users')
        .insert({
          id: data.user.id,
          email: data.user.email!,
          full_name: userData.full_name || '',
          phone: userData.phone || '',
          role: 'customer',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        });

      if (profileError) {
        console.error('AuthContext: Profile creation error:', profileError);
        return { data: null, error: 'Failed to create user profile' };
      }

      console.log('AuthContext: Signup successful');
      return { data: data.user, error: null };
    } catch (error: any) {
      console.error('AuthContext: Signup network error:', error);
      return { data: null, error: 'Network error. Please try again.' };
    }
  };

  // Sign in function
  const signIn = async (email: string, password: string) => {
    try {
      console.log('AuthContext: Attempting signin');

      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        console.error('AuthContext: Signin error:', error);
        return { data: null, error: error.message };
      }

      console.log('AuthContext: Signin successful');

      // The auth state change listener will handle updating user and session
      return { data: data.user, error: null };
    } catch (error: any) {
      console.error('AuthContext: Signin network error:', error);
      return { data: null, error: 'Network error. Please try again.' };
    }
  };

  // Google sign in function
  const signInWithGoogle = async () => {
    try {
      console.log('AuthContext: Attempting Google signin');
      const response = await fetch('/api/auth/google', {
        method: 'POST',
        credentials: 'include',
      });

      const data = await response.json();

      if (!response.ok) {
        console.error('AuthContext: Google signin error:', data.error);
        return { data: null, error: data.error };
      }

      console.log('AuthContext: Google OAuth URL generated');

      // Redirect to Google OAuth
      window.location.href = data.url;

      return { data: data, error: null };
    } catch (error: any) {
      console.error('AuthContext: Google signin network error:', error);
      return { data: null, error: 'Network error. Please try again.' };
    }
  };

  // Sign out function
  const signOut = async () => {
    try {
      console.log('AuthContext: Attempting signout');

      const { error } = await supabase.auth.signOut();

      if (error) {
        console.error('AuthContext: Signout error:', error);
        return { error: error.message };
      }

      console.log('AuthContext: Signout successful');

      // The auth state change listener will handle clearing user and session
      return { error: null };
    } catch (error: any) {
      console.error('AuthContext: Signout network error:', error);
      return { error: 'Network error. Please try again.' };
    }
  };

  // Reset password function
  const resetPassword = async (email: string) => {
    try {
      console.log('AuthContext: Reset password not implemented via API yet');
      return { error: 'Password reset functionality will be available soon' };
    } catch (error: any) {
      return { error: error.message };
    }
  };

  // Update profile function
  const updateProfile = async (updates: Partial<User>) => {
    if (!user) {
      return { data: null, error: 'No user logged in' };
    }

    try {
      console.log('AuthContext: Update profile not implemented via API yet');
      return { data: null, error: 'Profile update functionality will be available soon' };
    } catch (error: any) {
      return { data: null, error: error.message };
    }
  };

  // Refresh user data
  const refreshUser = async () => {
    console.log('AuthContext: Refreshing user data');
    if (session) {
      const userProfile = await fetchUserProfile(session.user.id);
      setUser(userProfile);
    }
  };

  const value: AuthContextType = {
    user,
    session,
    loading,
    signUp,
    signIn,
    signInWithGoogle,
    signOut,
    resetPassword,
    updateProfile,
    refreshUser,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}
