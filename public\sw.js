const CACHE_NAME = 'positive7-v2.0.0'

// Simple service worker without complex caching logic
// Just handles offline fallback

// Install event - simple installation
self.addEventListener('install', (event) => {
  console.log('Service Worker: Installing...')
  self.skipWaiting()
})

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  console.log('Service Worker: Activating...')
  event.waitUntil(
    caches.keys().then((cacheNames) => {
      return Promise.all(
        cacheNames.map((cacheName) => {
          if (cacheName !== CACHE_NAME) {
            console.log('Service Worker: Deleting old cache', cacheName)
            return caches.delete(cacheName)
          }
        })
      )
    }).then(() => {
      console.log('Service Worker: Activated')
      return self.clients.claim()
    })
  )
})

// Fetch event - simple pass-through, no caching
self.addEventListener('fetch', (event) => {
  // Just pass through all requests without caching
  // This ensures no caching issues during development
  event.respondWith(fetch(event.request))
})


