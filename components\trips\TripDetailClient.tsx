'use client';

import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { motion } from 'framer-motion';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import {
  Calendar,
  Clock,
  MapPin,
  Users,
  Star,
  ArrowLeft,
  Heart,
  Share2,
  Mountain,
  CheckCircle,
  XCircle,
  Info,
  Shield,
  Package,
  Activity,
  Train,
  Home,
  Phone,
  CreditCard,
  AlertTriangle,
  Backpack
} from 'lucide-react';
import Button from '@/components/ui/Button';
import { cn } from '@/lib/utils';

interface TripDetailClientProps {
  trip: any; // We'll type this properly later
  relatedTrips: any[];
}

export default function TripDetailClient({ trip, relatedTrips }: TripDetailClientProps) {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  };

  return (
    <>
      <Header />
      <main className="flex-1">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50"
        >
          {/* Hero Section */}
          <motion.section variants={itemVariants} className="relative h-[70vh] overflow-hidden">
            <Image
              src={trip.featured_image_url || '/images/fallback-trip.jpg'}
              alt={trip.title}
              fill
              className="object-cover"
              priority
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/30 to-transparent" />

            {/* Navigation */}
            <div className="absolute top-6 left-6 z-10">
              <Link href="/trips">
                <Button variant="secondary" size="sm" className="bg-white/20 backdrop-blur-sm hover:bg-white/30">
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Back to Trips
                </Button>
              </Link>
            </div>

            {/* Action Buttons */}
            <div className="absolute top-6 right-6 z-10 flex gap-2">
              <Button variant="secondary" size="sm" className="bg-white/20 backdrop-blur-sm hover:bg-white/30">
                <Heart className="w-4 h-4" />
              </Button>
              <Button variant="secondary" size="sm" className="bg-white/20 backdrop-blur-sm hover:bg-white/30">
                <Share2 className="w-4 h-4" />
              </Button>
            </div>

            {/* Hero Content */}
            <div className="absolute bottom-0 left-0 right-0 p-8 text-white">
              <div className="max-w-7xl mx-auto">
                <motion.div
                  variants={itemVariants}
                  className="flex flex-wrap items-end justify-between gap-6"
                >
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-4 mb-4">
                      <span className="px-3 py-1 bg-blue-600 rounded-full text-sm font-medium">
                        {trip.destination}
                      </span>
                      <div className="flex items-center gap-1">
                        <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                        <span className="font-medium">4.8</span>
                        <span className="text-white/80">(Reviews)</span>
                      </div>
                    </div>
                    <h1 className="text-4xl md:text-6xl font-bold mb-2">{trip.title}</h1>
                    <p className="text-xl text-white/90 mb-4">{trip.destination}</p>
                    <div className="flex flex-wrap items-center gap-6 text-white/80">
                      <div className="flex items-center gap-2">
                        <Clock className="w-5 h-5" />
                        <span>{trip.duration_days} Days</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Users className="w-5 h-5" />
                        <span>Educational Tour</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Mountain className="w-5 h-5" />
                        <span className="capitalize">{trip.difficulty}</span>
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-3xl font-bold mb-2">₹{trip.price_per_person.toLocaleString()}</div>
                    <div className="text-white/80 mb-4">per person</div>
                    <Button size="lg" className="bg-gradient-to-r from-blue-600 to-green-600 hover:from-blue-700 hover:to-green-700">
                      Book Now
                    </Button>
                  </div>
                </motion.div>
              </div>
            </div>
          </motion.section>

          {/* Main Content */}
          <div className="max-w-7xl mx-auto px-4 py-12">
            <div className="grid lg:grid-cols-3 gap-12">
              {/* Left Column - Main Content */}
              <div className="lg:col-span-2 space-y-12">
                {/* Trip Overview */}
                <motion.section variants={itemVariants} className="bg-white rounded-2xl p-8 shadow-lg">
                  <h2 className="text-3xl font-bold mb-6 text-gray-900">Trip Overview</h2>
                  <p className="text-gray-700 text-lg leading-relaxed mb-8">
                    {trip.description || 'Discover amazing educational experiences and create unforgettable memories with Positive7.'}
                  </p>

                  {/* Quick Info Grid */}
                  <div className="grid md:grid-cols-2 gap-6">
                    <div className="space-y-4">
                      <div className="flex items-center gap-3">
                        <Clock className="w-5 h-5 text-blue-600" />
                        <div>
                          <div className="font-medium text-gray-900">Duration</div>
                          <div className="text-gray-600">{trip.duration_days} Days</div>
                        </div>
                      </div>
                      <div className="flex items-center gap-3">
                        <Users className="w-5 h-5 text-blue-600" />
                        <div>
                          <div className="font-medium text-gray-900">Type</div>
                          <div className="text-gray-600">Educational Tour</div>
                        </div>
                      </div>
                    </div>
                    <div className="space-y-4">
                      <div className="flex items-center gap-3">
                        <Mountain className="w-5 h-5 text-blue-600" />
                        <div>
                          <div className="font-medium text-gray-900">Difficulty</div>
                          <div className="text-gray-600 capitalize">{trip.difficulty}</div>
                        </div>
                      </div>
                      <div className="flex items-center gap-3">
                        <MapPin className="w-5 h-5 text-blue-600" />
                        <div>
                          <div className="font-medium text-gray-900">Destination</div>
                          <div className="text-gray-600">{trip.destination}</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </motion.section>

                {/* Trip Details */}
                {trip.detailed_description && (
                  <motion.section variants={itemVariants} className="bg-white rounded-2xl p-8 shadow-lg">
                    <h2 className="text-3xl font-bold mb-6 text-gray-900">About This Trip</h2>
                    <p className="text-gray-700 text-lg leading-relaxed">
                      {trip.detailed_description}
                    </p>
                  </motion.section>
                )}

                {/* Travel Information */}
                <motion.section variants={itemVariants} className="bg-white rounded-2xl p-8 shadow-lg">
                  <h2 className="text-3xl font-bold mb-6 text-gray-900">Travel Information</h2>
                  <div className="grid md:grid-cols-2 gap-6">
                    {trip.mode_of_travel && (
                      <div className="flex items-start gap-3">
                        <Train className="w-5 h-5 text-blue-600 mt-1" />
                        <div>
                          <div className="font-medium text-gray-900 mb-1">Mode of Travel</div>
                          <div className="text-gray-600">{trip.mode_of_travel}</div>
                        </div>
                      </div>
                    )}
                    {trip.pickup_location && (
                      <div className="flex items-start gap-3">
                        <MapPin className="w-5 h-5 text-green-600 mt-1" />
                        <div>
                          <div className="font-medium text-gray-900 mb-1">Pickup Location</div>
                          <div className="text-gray-600">{trip.pickup_location}</div>
                        </div>
                      </div>
                    )}
                    {trip.drop_location && (
                      <div className="flex items-start gap-3">
                        <MapPin className="w-5 h-5 text-red-600 mt-1" />
                        <div>
                          <div className="font-medium text-gray-900 mb-1">Drop Location</div>
                          <div className="text-gray-600">{trip.drop_location}</div>
                        </div>
                      </div>
                    )}
                    {trip.property_used && (
                      <div className="flex items-start gap-3">
                        <Home className="w-5 h-5 text-purple-600 mt-1" />
                        <div>
                          <div className="font-medium text-gray-900 mb-1">Accommodation</div>
                          <div className="text-gray-600">{trip.property_used}</div>
                        </div>
                      </div>
                    )}
                  </div>
                </motion.section>

                {/* Activities */}
                {trip.activities && trip.activities.length > 0 && (
                  <motion.section variants={itemVariants} className="bg-white rounded-2xl p-8 shadow-lg">
                    <h2 className="text-3xl font-bold mb-6 text-gray-900">Activities Included</h2>
                    <div className="grid md:grid-cols-2 gap-4">
                      {trip.activities.map((activity, index) => (
                        <div key={index} className="flex items-center gap-3">
                          <Activity className="w-5 h-5 text-green-600" />
                          <span className="text-gray-700">{activity}</span>
                        </div>
                      ))}
                    </div>

                    {trip.optional_activities && trip.optional_activities.length > 0 && (
                      <div className="mt-8">
                        <h3 className="text-xl font-semibold mb-4 text-gray-900">Optional Activities</h3>
                        <div className="grid md:grid-cols-2 gap-4">
                          {trip.optional_activities.map((activity, index) => (
                            <div key={index} className="flex items-center gap-3">
                              <Activity className="w-5 h-5 text-orange-600" />
                              <span className="text-gray-700">{activity}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </motion.section>
                )}

                {/* Inclusions & Exclusions */}
                <motion.section variants={itemVariants} className="bg-white rounded-2xl p-8 shadow-lg">
                  <h2 className="text-3xl font-bold mb-6 text-gray-900">What's Included & Excluded</h2>
                  <div className="grid md:grid-cols-2 gap-8">
                    {/* Inclusions */}
                    <div>
                      <h3 className="text-xl font-semibold mb-4 text-green-700 flex items-center gap-2">
                        <CheckCircle className="w-5 h-5" />
                        Included
                      </h3>
                      <div className="space-y-3">
                        {trip.inclusions && trip.inclusions.map((item, index) => (
                          <div key={index} className="flex items-start gap-3">
                            <CheckCircle className="w-4 h-4 text-green-600 mt-1 flex-shrink-0" />
                            <span className="text-gray-700">{item}</span>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Exclusions */}
                    <div>
                      <h3 className="text-xl font-semibold mb-4 text-red-700 flex items-center gap-2">
                        <XCircle className="w-5 h-5" />
                        Not Included
                      </h3>
                      <div className="space-y-3">
                        {trip.exclusions && trip.exclusions.map((item, index) => (
                          <div key={index} className="flex items-start gap-3">
                            <XCircle className="w-4 h-4 text-red-600 mt-1 flex-shrink-0" />
                            <span className="text-gray-700">{item}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </motion.section>

                {/* Safety & Benefits */}
                {((trip.benefits && trip.benefits.length > 0) || (trip.safety_supervision && trip.safety_supervision.length > 0)) && (
                  <motion.section variants={itemVariants} className="bg-white rounded-2xl p-8 shadow-lg">
                    <h2 className="text-3xl font-bold mb-6 text-gray-900">Safety & Benefits</h2>
                    <div className="grid md:grid-cols-2 gap-8">
                      {/* Benefits */}
                      {trip.benefits && trip.benefits.length > 0 && (
                        <div>
                          <h3 className="text-xl font-semibold mb-4 text-blue-700 flex items-center gap-2">
                            <Package className="w-5 h-5" />
                            Trip Benefits
                          </h3>
                          <div className="space-y-3">
                            {trip.benefits.map((benefit, index) => (
                              <div key={index} className="flex items-start gap-3">
                                <CheckCircle className="w-4 h-4 text-blue-600 mt-1 flex-shrink-0" />
                                <span className="text-gray-700">{benefit}</span>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}

                      {/* Safety */}
                      {trip.safety_supervision && trip.safety_supervision.length > 0 && (
                        <div>
                          <h3 className="text-xl font-semibold mb-4 text-green-700 flex items-center gap-2">
                            <Shield className="w-5 h-5" />
                            Safety & Supervision
                          </h3>
                          <div className="space-y-3">
                            {trip.safety_supervision.map((safety, index) => (
                              <div key={index} className="flex items-start gap-3">
                                <Shield className="w-4 h-4 text-green-600 mt-1 flex-shrink-0" />
                                <span className="text-gray-700">{safety}</span>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  </motion.section>
                )}

                {/* Things to Carry */}
                {trip.things_to_carry && trip.things_to_carry.length > 0 && (
                  <motion.section variants={itemVariants} className="bg-white rounded-2xl p-8 shadow-lg">
                    <h2 className="text-3xl font-bold mb-6 text-gray-900 flex items-center gap-3">
                      <Backpack className="w-8 h-8 text-orange-600" />
                      Things to Carry
                    </h2>
                    <div className="grid md:grid-cols-2 gap-4">
                      {trip.things_to_carry.map((item, index) => (
                        <div key={index} className="flex items-start gap-3">
                          <Backpack className="w-4 h-4 text-orange-600 mt-1 flex-shrink-0" />
                          <span className="text-gray-700">{item}</span>
                        </div>
                      ))}
                    </div>
                  </motion.section>
                )}

                {/* Available Dates */}
                {trip.available_dates && trip.available_dates.length > 0 && (
                  <motion.section variants={itemVariants} className="bg-white rounded-2xl p-8 shadow-lg">
                    <h2 className="text-3xl font-bold mb-6 text-gray-900 flex items-center gap-3">
                      <Calendar className="w-8 h-8 text-purple-600" />
                      Available Dates
                    </h2>
                    <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
                      {trip.available_dates.map((date, index) => (
                        <div key={index} className="bg-purple-50 border border-purple-200 rounded-lg p-4 text-center">
                          <Calendar className="w-5 h-5 text-purple-600 mx-auto mb-2" />
                          <span className="text-gray-800 font-medium">{date}</span>
                        </div>
                      ))}
                    </div>
                  </motion.section>
                )}

                {/* Payment & Cancellation */}
                <motion.section variants={itemVariants} className="bg-white rounded-2xl p-8 shadow-lg">
                  <h2 className="text-3xl font-bold mb-6 text-gray-900">Payment & Cancellation Policy</h2>
                  <div className="grid md:grid-cols-2 gap-8">
                    {/* Payment Terms */}
                    {trip.payment_terms && (
                      <div>
                        <h3 className="text-xl font-semibold mb-4 text-green-700 flex items-center gap-2">
                          <CreditCard className="w-5 h-5" />
                          Payment Terms
                        </h3>
                        <p className="text-gray-700">{trip.payment_terms}</p>
                      </div>
                    )}

                    {/* Cancellation Policy */}
                    {trip.cancellation_policy && (
                      <div>
                        <h3 className="text-xl font-semibold mb-4 text-red-700 flex items-center gap-2">
                          <AlertTriangle className="w-5 h-5" />
                          Cancellation Policy
                        </h3>
                        <div className="space-y-2">
                          {Object.entries(trip.cancellation_policy).map(([key, value]) => (
                            <div key={key} className="flex justify-between">
                              <span className="text-gray-600 capitalize">{key.replace(/_/g, ' ')}</span>
                              <span className="font-medium text-gray-800">{value}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </motion.section>

                {/* Special Notes */}
                {trip.special_notes && trip.special_notes.length > 0 && (
                  <motion.section variants={itemVariants} className="bg-yellow-50 border border-yellow-200 rounded-2xl p-8">
                    <h2 className="text-2xl font-bold mb-6 text-yellow-800 flex items-center gap-3">
                      <Info className="w-6 h-6" />
                      Important Notes
                    </h2>
                    <div className="space-y-3">
                      {trip.special_notes.map((note, index) => (
                        <div key={index} className="flex items-start gap-3">
                          <Info className="w-4 h-4 text-yellow-600 mt-1 flex-shrink-0" />
                          <span className="text-yellow-800">{note}</span>
                        </div>
                      ))}
                    </div>
                  </motion.section>
                )}

                {/* Booking CTA for Mobile */}
                <motion.section variants={itemVariants} className="lg:hidden bg-white rounded-2xl p-6 shadow-lg">
                  <div className="text-center">
                    <div className="text-2xl font-bold mb-2">₹{trip.price_per_person.toLocaleString()}</div>
                    <div className="text-gray-600 mb-4">per person</div>
                    <Button size="lg" className="w-full bg-gradient-to-r from-blue-600 to-green-600 hover:from-blue-700 hover:to-green-700">
                      Book This Trip
                    </Button>
                  </div>
                </motion.section>
              </div>

              {/* Right Column - Booking Sidebar */}
              <div className="lg:col-span-1 hidden lg:block">
                <motion.div variants={itemVariants} className="sticky top-24 space-y-6">
                  {/* Pricing Card */}
                  <div className="bg-white rounded-2xl p-6 shadow-lg">
                    <div className="text-center mb-6">
                      <div className="text-3xl font-bold mb-2">₹{trip.price_per_person.toLocaleString()}</div>
                      <div className="text-gray-600">per person</div>
                      {trip.commercial_price && trip.commercial_price !== trip.price_per_person && (
                        <div className="text-sm text-gray-500 line-through">₹{trip.commercial_price.toLocaleString()}</div>
                      )}
                    </div>

                    {/* Quick Info */}
                    <div className="space-y-3 mb-6">
                      <div className="flex justify-between items-center">
                        <span className="text-gray-600">Duration</span>
                        <span className="font-medium">{trip.duration_days} Days</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-gray-600">Group Size</span>
                        <span className="font-medium">{trip.min_participants}-{trip.max_participants}</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-gray-600">Difficulty</span>
                        <span className="font-medium capitalize">{trip.difficulty}</span>
                      </div>
                      {trip.category && (
                        <div className="flex justify-between items-center">
                          <span className="text-gray-600">Category</span>
                          <span className="font-medium">{trip.category}</span>
                        </div>
                      )}
                    </div>

                    <Button size="lg" className="w-full mb-4 bg-gradient-to-r from-blue-600 to-green-600 hover:from-blue-700 hover:to-green-700">
                      Book This Trip
                    </Button>

                    {/* Contact Options */}
                    <div className="space-y-3">
                      <Button variant="outline" size="sm" className="w-full">
                        <Phone className="w-4 h-4 mr-2" />
                        Call: +91 78780 05500
                      </Button>
                      <Button variant="outline" size="sm" className="w-full">
                        WhatsApp Inquiry
                      </Button>
                    </div>

                    <div className="text-center text-sm text-gray-500 mt-4">
                      <p>Contact us for group bookings and custom packages</p>
                    </div>
                  </div>

                  {/* Quick Features */}
                  <div className="bg-white rounded-2xl p-6 shadow-lg">
                    <h3 className="font-semibold text-gray-900 mb-4">Why Choose This Trip?</h3>
                    <div className="space-y-3">
                      <div className="flex items-center gap-3">
                        <Shield className="w-4 h-4 text-green-600" />
                        <span className="text-sm text-gray-700">Safety First</span>
                      </div>
                      <div className="flex items-center gap-3">
                        <Users className="w-4 h-4 text-blue-600" />
                        <span className="text-sm text-gray-700">Expert Guides</span>
                      </div>
                      <div className="flex items-center gap-3">
                        <CheckCircle className="w-4 h-4 text-green-600" />
                        <span className="text-sm text-gray-700">All Inclusive</span>
                      </div>
                      <div className="flex items-center gap-3">
                        <Phone className="w-4 h-4 text-purple-600" />
                        <span className="text-sm text-gray-700">24/7 Support</span>
                      </div>
                    </div>
                  </div>
                </motion.div>
              </div>
            </div>

            {/* Related Trips */}
            {relatedTrips.length > 0 && (
              <motion.section variants={itemVariants} className="mt-16">
                <h2 className="text-3xl font-bold mb-8 text-gray-900">Related Trips</h2>
                <div className="grid md:grid-cols-3 gap-6">
                  {relatedTrips.map((relatedTrip) => (
                    <Link key={relatedTrip.id} href={`/trips/${relatedTrip.slug}`}>
                      <div className="bg-white rounded-lg overflow-hidden shadow-md hover:shadow-lg transition-shadow">
                        <div className="relative h-48">
                          <Image
                            src={relatedTrip.featured_image_url || '/images/fallback-trip.jpg'}
                            alt={relatedTrip.title}
                            fill
                            className="object-cover"
                          />
                        </div>
                        <div className="p-4">
                          <h3 className="font-semibold text-gray-900 mb-2">{relatedTrip.title}</h3>
                          <p className="text-gray-600 text-sm mb-3 line-clamp-2">
                            {relatedTrip.description || 'Discover amazing experiences'}
                          </p>
                          <div className="flex justify-between items-center">
                            <span className="text-sm text-gray-500">{relatedTrip.duration_days} Days</span>
                            <span className="font-semibold text-primary-600">₹{relatedTrip.price_per_person.toLocaleString()}</span>
                          </div>
                        </div>
                      </div>
                    </Link>
                  ))}
                </div>
              </motion.section>
            )}
          </div>
        </motion.div>
      </main>
      <Footer />
    </>
  );
}
