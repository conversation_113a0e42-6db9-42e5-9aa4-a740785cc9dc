/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/prop-types";
exports.ids = ["vendor-chunks/prop-types"];
exports.modules = {

/***/ "(ssr)/./node_modules/prop-types/checkPropTypes.js":
/*!***************************************************!*\
  !*** ./node_modules/prop-types/checkPropTypes.js ***!
  \***************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */ \nvar printWarning = function() {};\nif (true) {\n    var ReactPropTypesSecret = __webpack_require__(/*! ./lib/ReactPropTypesSecret */ \"(ssr)/./node_modules/prop-types/lib/ReactPropTypesSecret.js\");\n    var loggedTypeFailures = {};\n    var has = __webpack_require__(/*! ./lib/has */ \"(ssr)/./node_modules/prop-types/lib/has.js\");\n    printWarning = function(text) {\n        var message = \"Warning: \" + text;\n        if (typeof console !== \"undefined\") {\n            console.error(message);\n        }\n        try {\n            // --- Welcome to debugging React ---\n            // This error was thrown as a convenience so that you can use this stack\n            // to find the callsite that caused this warning to fire.\n            throw new Error(message);\n        } catch (x) {}\n    };\n}\n/**\n * Assert that the values match with the type specs.\n * Error messages are memorized and will only be shown once.\n *\n * @param {object} typeSpecs Map of name to a ReactPropType\n * @param {object} values Runtime values that need to be type-checked\n * @param {string} location e.g. \"prop\", \"context\", \"child context\"\n * @param {string} componentName Name of the component for error messages.\n * @param {?Function} getStack Returns the component stack.\n * @private\n */ function checkPropTypes(typeSpecs, values, location, componentName, getStack) {\n    if (true) {\n        for(var typeSpecName in typeSpecs){\n            if (has(typeSpecs, typeSpecName)) {\n                var error;\n                // Prop type validation may throw. In case they do, we don't want to\n                // fail the render phase where it didn't fail before. So we log it.\n                // After these have been cleaned up, we'll let them throw.\n                try {\n                    // This is intentionally an invariant that gets caught. It's the same\n                    // behavior as without this statement except with a better message.\n                    if (typeof typeSpecs[typeSpecName] !== \"function\") {\n                        var err = Error((componentName || \"React class\") + \": \" + location + \" type `\" + typeSpecName + \"` is invalid; \" + \"it must be a function, usually from the `prop-types` package, but received `\" + typeof typeSpecs[typeSpecName] + \"`.\" + \"This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.\");\n                        err.name = \"Invariant Violation\";\n                        throw err;\n                    }\n                    error = typeSpecs[typeSpecName](values, typeSpecName, componentName, location, null, ReactPropTypesSecret);\n                } catch (ex) {\n                    error = ex;\n                }\n                if (error && !(error instanceof Error)) {\n                    printWarning((componentName || \"React class\") + \": type specification of \" + location + \" `\" + typeSpecName + \"` is invalid; the type checker \" + \"function must return `null` or an `Error` but returned a \" + typeof error + \". \" + \"You may have forgotten to pass an argument to the type checker \" + \"creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and \" + \"shape all require an argument).\");\n                }\n                if (error instanceof Error && !(error.message in loggedTypeFailures)) {\n                    // Only monitor this failure once because there tends to be a lot of the\n                    // same error.\n                    loggedTypeFailures[error.message] = true;\n                    var stack = getStack ? getStack() : \"\";\n                    printWarning(\"Failed \" + location + \" type: \" + error.message + (stack != null ? stack : \"\"));\n                }\n            }\n        }\n    }\n}\n/**\n * Resets warning cache when testing.\n *\n * @private\n */ checkPropTypes.resetWarningCache = function() {\n    if (true) {\n        loggedTypeFailures = {};\n    }\n};\nmodule.exports = checkPropTypes;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/prop-types/checkPropTypes.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/prop-types/factoryWithTypeCheckers.js":
/*!************************************************************!*\
  !*** ./node_modules/prop-types/factoryWithTypeCheckers.js ***!
  \************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */ \nvar ReactIs = __webpack_require__(/*! react-is */ \"(ssr)/./node_modules/react-is/index.js\");\nvar assign = __webpack_require__(/*! object-assign */ \"(ssr)/./node_modules/object-assign/index.js\");\nvar ReactPropTypesSecret = __webpack_require__(/*! ./lib/ReactPropTypesSecret */ \"(ssr)/./node_modules/prop-types/lib/ReactPropTypesSecret.js\");\nvar has = __webpack_require__(/*! ./lib/has */ \"(ssr)/./node_modules/prop-types/lib/has.js\");\nvar checkPropTypes = __webpack_require__(/*! ./checkPropTypes */ \"(ssr)/./node_modules/prop-types/checkPropTypes.js\");\nvar printWarning = function() {};\nif (true) {\n    printWarning = function(text) {\n        var message = \"Warning: \" + text;\n        if (typeof console !== \"undefined\") {\n            console.error(message);\n        }\n        try {\n            // --- Welcome to debugging React ---\n            // This error was thrown as a convenience so that you can use this stack\n            // to find the callsite that caused this warning to fire.\n            throw new Error(message);\n        } catch (x) {}\n    };\n}\nfunction emptyFunctionThatReturnsNull() {\n    return null;\n}\nmodule.exports = function(isValidElement, throwOnDirectAccess) {\n    /* global Symbol */ var ITERATOR_SYMBOL = typeof Symbol === \"function\" && Symbol.iterator;\n    var FAUX_ITERATOR_SYMBOL = \"@@iterator\"; // Before Symbol spec.\n    /**\n   * Returns the iterator method function contained on the iterable object.\n   *\n   * Be sure to invoke the function with the iterable as context:\n   *\n   *     var iteratorFn = getIteratorFn(myIterable);\n   *     if (iteratorFn) {\n   *       var iterator = iteratorFn.call(myIterable);\n   *       ...\n   *     }\n   *\n   * @param {?object} maybeIterable\n   * @return {?function}\n   */ function getIteratorFn(maybeIterable) {\n        var iteratorFn = maybeIterable && (ITERATOR_SYMBOL && maybeIterable[ITERATOR_SYMBOL] || maybeIterable[FAUX_ITERATOR_SYMBOL]);\n        if (typeof iteratorFn === \"function\") {\n            return iteratorFn;\n        }\n    }\n    /**\n   * Collection of methods that allow declaration and validation of props that are\n   * supplied to React components. Example usage:\n   *\n   *   var Props = require('ReactPropTypes');\n   *   var MyArticle = React.createClass({\n   *     propTypes: {\n   *       // An optional string prop named \"description\".\n   *       description: Props.string,\n   *\n   *       // A required enum prop named \"category\".\n   *       category: Props.oneOf(['News','Photos']).isRequired,\n   *\n   *       // A prop named \"dialog\" that requires an instance of Dialog.\n   *       dialog: Props.instanceOf(Dialog).isRequired\n   *     },\n   *     render: function() { ... }\n   *   });\n   *\n   * A more formal specification of how these methods are used:\n   *\n   *   type := array|bool|func|object|number|string|oneOf([...])|instanceOf(...)\n   *   decl := ReactPropTypes.{type}(.isRequired)?\n   *\n   * Each and every declaration produces a function with the same signature. This\n   * allows the creation of custom validation functions. For example:\n   *\n   *  var MyLink = React.createClass({\n   *    propTypes: {\n   *      // An optional string or URI prop named \"href\".\n   *      href: function(props, propName, componentName) {\n   *        var propValue = props[propName];\n   *        if (propValue != null && typeof propValue !== 'string' &&\n   *            !(propValue instanceof URI)) {\n   *          return new Error(\n   *            'Expected a string or an URI for ' + propName + ' in ' +\n   *            componentName\n   *          );\n   *        }\n   *      }\n   *    },\n   *    render: function() {...}\n   *  });\n   *\n   * @internal\n   */ var ANONYMOUS = \"<<anonymous>>\";\n    // Important!\n    // Keep this list in sync with production version in `./factoryWithThrowingShims.js`.\n    var ReactPropTypes = {\n        array: createPrimitiveTypeChecker(\"array\"),\n        bigint: createPrimitiveTypeChecker(\"bigint\"),\n        bool: createPrimitiveTypeChecker(\"boolean\"),\n        func: createPrimitiveTypeChecker(\"function\"),\n        number: createPrimitiveTypeChecker(\"number\"),\n        object: createPrimitiveTypeChecker(\"object\"),\n        string: createPrimitiveTypeChecker(\"string\"),\n        symbol: createPrimitiveTypeChecker(\"symbol\"),\n        any: createAnyTypeChecker(),\n        arrayOf: createArrayOfTypeChecker,\n        element: createElementTypeChecker(),\n        elementType: createElementTypeTypeChecker(),\n        instanceOf: createInstanceTypeChecker,\n        node: createNodeChecker(),\n        objectOf: createObjectOfTypeChecker,\n        oneOf: createEnumTypeChecker,\n        oneOfType: createUnionTypeChecker,\n        shape: createShapeTypeChecker,\n        exact: createStrictShapeTypeChecker\n    };\n    /**\n   * inlined Object.is polyfill to avoid requiring consumers ship their own\n   * https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/is\n   */ /*eslint-disable no-self-compare*/ function is(x, y) {\n        // SameValue algorithm\n        if (x === y) {\n            // Steps 1-5, 7-10\n            // Steps 6.b-6.e: +0 != -0\n            return x !== 0 || 1 / x === 1 / y;\n        } else {\n            // Step 6.a: NaN == NaN\n            return x !== x && y !== y;\n        }\n    }\n    /*eslint-enable no-self-compare*/ /**\n   * We use an Error-like object for backward compatibility as people may call\n   * PropTypes directly and inspect their output. However, we don't use real\n   * Errors anymore. We don't inspect their stack anyway, and creating them\n   * is prohibitively expensive if they are created too often, such as what\n   * happens in oneOfType() for any type before the one that matched.\n   */ function PropTypeError(message, data) {\n        this.message = message;\n        this.data = data && typeof data === \"object\" ? data : {};\n        this.stack = \"\";\n    }\n    // Make `instanceof Error` still work for returned errors.\n    PropTypeError.prototype = Error.prototype;\n    function createChainableTypeChecker(validate) {\n        if (true) {\n            var manualPropTypeCallCache = {};\n            var manualPropTypeWarningCount = 0;\n        }\n        function checkType(isRequired, props, propName, componentName, location, propFullName, secret) {\n            componentName = componentName || ANONYMOUS;\n            propFullName = propFullName || propName;\n            if (secret !== ReactPropTypesSecret) {\n                if (throwOnDirectAccess) {\n                    // New behavior only for users of `prop-types` package\n                    var err = new Error(\"Calling PropTypes validators directly is not supported by the `prop-types` package. \" + \"Use `PropTypes.checkPropTypes()` to call them. \" + \"Read more at http://fb.me/use-check-prop-types\");\n                    err.name = \"Invariant Violation\";\n                    throw err;\n                } else if ( true && typeof console !== \"undefined\") {\n                    // Old behavior for people using React.PropTypes\n                    var cacheKey = componentName + \":\" + propName;\n                    if (!manualPropTypeCallCache[cacheKey] && // Avoid spamming the console because they are often not actionable except for lib authors\n                    manualPropTypeWarningCount < 3) {\n                        printWarning(\"You are manually calling a React.PropTypes validation \" + \"function for the `\" + propFullName + \"` prop on `\" + componentName + \"`. This is deprecated \" + \"and will throw in the standalone `prop-types` package. \" + \"You may be seeing this warning due to a third-party PropTypes \" + \"library. See https://fb.me/react-warning-dont-call-proptypes \" + \"for details.\");\n                        manualPropTypeCallCache[cacheKey] = true;\n                        manualPropTypeWarningCount++;\n                    }\n                }\n            }\n            if (props[propName] == null) {\n                if (isRequired) {\n                    if (props[propName] === null) {\n                        return new PropTypeError(\"The \" + location + \" `\" + propFullName + \"` is marked as required \" + (\"in `\" + componentName + \"`, but its value is `null`.\"));\n                    }\n                    return new PropTypeError(\"The \" + location + \" `\" + propFullName + \"` is marked as required in \" + (\"`\" + componentName + \"`, but its value is `undefined`.\"));\n                }\n                return null;\n            } else {\n                return validate(props, propName, componentName, location, propFullName);\n            }\n        }\n        var chainedCheckType = checkType.bind(null, false);\n        chainedCheckType.isRequired = checkType.bind(null, true);\n        return chainedCheckType;\n    }\n    function createPrimitiveTypeChecker(expectedType) {\n        function validate(props, propName, componentName, location, propFullName, secret) {\n            var propValue = props[propName];\n            var propType = getPropType(propValue);\n            if (propType !== expectedType) {\n                // `propValue` being instance of, say, date/regexp, pass the 'object'\n                // check, but we can offer a more precise error message here rather than\n                // 'of type `object`'.\n                var preciseType = getPreciseType(propValue);\n                return new PropTypeError(\"Invalid \" + location + \" `\" + propFullName + \"` of type \" + (\"`\" + preciseType + \"` supplied to `\" + componentName + \"`, expected \") + (\"`\" + expectedType + \"`.\"), {\n                    expectedType: expectedType\n                });\n            }\n            return null;\n        }\n        return createChainableTypeChecker(validate);\n    }\n    function createAnyTypeChecker() {\n        return createChainableTypeChecker(emptyFunctionThatReturnsNull);\n    }\n    function createArrayOfTypeChecker(typeChecker) {\n        function validate(props, propName, componentName, location, propFullName) {\n            if (typeof typeChecker !== \"function\") {\n                return new PropTypeError(\"Property `\" + propFullName + \"` of component `\" + componentName + \"` has invalid PropType notation inside arrayOf.\");\n            }\n            var propValue = props[propName];\n            if (!Array.isArray(propValue)) {\n                var propType = getPropType(propValue);\n                return new PropTypeError(\"Invalid \" + location + \" `\" + propFullName + \"` of type \" + (\"`\" + propType + \"` supplied to `\" + componentName + \"`, expected an array.\"));\n            }\n            for(var i = 0; i < propValue.length; i++){\n                var error = typeChecker(propValue, i, componentName, location, propFullName + \"[\" + i + \"]\", ReactPropTypesSecret);\n                if (error instanceof Error) {\n                    return error;\n                }\n            }\n            return null;\n        }\n        return createChainableTypeChecker(validate);\n    }\n    function createElementTypeChecker() {\n        function validate(props, propName, componentName, location, propFullName) {\n            var propValue = props[propName];\n            if (!isValidElement(propValue)) {\n                var propType = getPropType(propValue);\n                return new PropTypeError(\"Invalid \" + location + \" `\" + propFullName + \"` of type \" + (\"`\" + propType + \"` supplied to `\" + componentName + \"`, expected a single ReactElement.\"));\n            }\n            return null;\n        }\n        return createChainableTypeChecker(validate);\n    }\n    function createElementTypeTypeChecker() {\n        function validate(props, propName, componentName, location, propFullName) {\n            var propValue = props[propName];\n            if (!ReactIs.isValidElementType(propValue)) {\n                var propType = getPropType(propValue);\n                return new PropTypeError(\"Invalid \" + location + \" `\" + propFullName + \"` of type \" + (\"`\" + propType + \"` supplied to `\" + componentName + \"`, expected a single ReactElement type.\"));\n            }\n            return null;\n        }\n        return createChainableTypeChecker(validate);\n    }\n    function createInstanceTypeChecker(expectedClass) {\n        function validate(props, propName, componentName, location, propFullName) {\n            if (!(props[propName] instanceof expectedClass)) {\n                var expectedClassName = expectedClass.name || ANONYMOUS;\n                var actualClassName = getClassName(props[propName]);\n                return new PropTypeError(\"Invalid \" + location + \" `\" + propFullName + \"` of type \" + (\"`\" + actualClassName + \"` supplied to `\" + componentName + \"`, expected \") + (\"instance of `\" + expectedClassName + \"`.\"));\n            }\n            return null;\n        }\n        return createChainableTypeChecker(validate);\n    }\n    function createEnumTypeChecker(expectedValues) {\n        if (!Array.isArray(expectedValues)) {\n            if (true) {\n                if (arguments.length > 1) {\n                    printWarning(\"Invalid arguments supplied to oneOf, expected an array, got \" + arguments.length + \" arguments. \" + \"A common mistake is to write oneOf(x, y, z) instead of oneOf([x, y, z]).\");\n                } else {\n                    printWarning(\"Invalid argument supplied to oneOf, expected an array.\");\n                }\n            }\n            return emptyFunctionThatReturnsNull;\n        }\n        function validate(props, propName, componentName, location, propFullName) {\n            var propValue = props[propName];\n            for(var i = 0; i < expectedValues.length; i++){\n                if (is(propValue, expectedValues[i])) {\n                    return null;\n                }\n            }\n            var valuesString = JSON.stringify(expectedValues, function replacer(key, value) {\n                var type = getPreciseType(value);\n                if (type === \"symbol\") {\n                    return String(value);\n                }\n                return value;\n            });\n            return new PropTypeError(\"Invalid \" + location + \" `\" + propFullName + \"` of value `\" + String(propValue) + \"` \" + (\"supplied to `\" + componentName + \"`, expected one of \" + valuesString + \".\"));\n        }\n        return createChainableTypeChecker(validate);\n    }\n    function createObjectOfTypeChecker(typeChecker) {\n        function validate(props, propName, componentName, location, propFullName) {\n            if (typeof typeChecker !== \"function\") {\n                return new PropTypeError(\"Property `\" + propFullName + \"` of component `\" + componentName + \"` has invalid PropType notation inside objectOf.\");\n            }\n            var propValue = props[propName];\n            var propType = getPropType(propValue);\n            if (propType !== \"object\") {\n                return new PropTypeError(\"Invalid \" + location + \" `\" + propFullName + \"` of type \" + (\"`\" + propType + \"` supplied to `\" + componentName + \"`, expected an object.\"));\n            }\n            for(var key in propValue){\n                if (has(propValue, key)) {\n                    var error = typeChecker(propValue, key, componentName, location, propFullName + \".\" + key, ReactPropTypesSecret);\n                    if (error instanceof Error) {\n                        return error;\n                    }\n                }\n            }\n            return null;\n        }\n        return createChainableTypeChecker(validate);\n    }\n    function createUnionTypeChecker(arrayOfTypeCheckers) {\n        if (!Array.isArray(arrayOfTypeCheckers)) {\n             true ? printWarning(\"Invalid argument supplied to oneOfType, expected an instance of array.\") : 0;\n            return emptyFunctionThatReturnsNull;\n        }\n        for(var i = 0; i < arrayOfTypeCheckers.length; i++){\n            var checker = arrayOfTypeCheckers[i];\n            if (typeof checker !== \"function\") {\n                printWarning(\"Invalid argument supplied to oneOfType. Expected an array of check functions, but \" + \"received \" + getPostfixForTypeWarning(checker) + \" at index \" + i + \".\");\n                return emptyFunctionThatReturnsNull;\n            }\n        }\n        function validate(props, propName, componentName, location, propFullName) {\n            var expectedTypes = [];\n            for(var i = 0; i < arrayOfTypeCheckers.length; i++){\n                var checker = arrayOfTypeCheckers[i];\n                var checkerResult = checker(props, propName, componentName, location, propFullName, ReactPropTypesSecret);\n                if (checkerResult == null) {\n                    return null;\n                }\n                if (checkerResult.data && has(checkerResult.data, \"expectedType\")) {\n                    expectedTypes.push(checkerResult.data.expectedType);\n                }\n            }\n            var expectedTypesMessage = expectedTypes.length > 0 ? \", expected one of type [\" + expectedTypes.join(\", \") + \"]\" : \"\";\n            return new PropTypeError(\"Invalid \" + location + \" `\" + propFullName + \"` supplied to \" + (\"`\" + componentName + \"`\" + expectedTypesMessage + \".\"));\n        }\n        return createChainableTypeChecker(validate);\n    }\n    function createNodeChecker() {\n        function validate(props, propName, componentName, location, propFullName) {\n            if (!isNode(props[propName])) {\n                return new PropTypeError(\"Invalid \" + location + \" `\" + propFullName + \"` supplied to \" + (\"`\" + componentName + \"`, expected a ReactNode.\"));\n            }\n            return null;\n        }\n        return createChainableTypeChecker(validate);\n    }\n    function invalidValidatorError(componentName, location, propFullName, key, type) {\n        return new PropTypeError((componentName || \"React class\") + \": \" + location + \" type `\" + propFullName + \".\" + key + \"` is invalid; \" + \"it must be a function, usually from the `prop-types` package, but received `\" + type + \"`.\");\n    }\n    function createShapeTypeChecker(shapeTypes) {\n        function validate(props, propName, componentName, location, propFullName) {\n            var propValue = props[propName];\n            var propType = getPropType(propValue);\n            if (propType !== \"object\") {\n                return new PropTypeError(\"Invalid \" + location + \" `\" + propFullName + \"` of type `\" + propType + \"` \" + (\"supplied to `\" + componentName + \"`, expected `object`.\"));\n            }\n            for(var key in shapeTypes){\n                var checker = shapeTypes[key];\n                if (typeof checker !== \"function\") {\n                    return invalidValidatorError(componentName, location, propFullName, key, getPreciseType(checker));\n                }\n                var error = checker(propValue, key, componentName, location, propFullName + \".\" + key, ReactPropTypesSecret);\n                if (error) {\n                    return error;\n                }\n            }\n            return null;\n        }\n        return createChainableTypeChecker(validate);\n    }\n    function createStrictShapeTypeChecker(shapeTypes) {\n        function validate(props, propName, componentName, location, propFullName) {\n            var propValue = props[propName];\n            var propType = getPropType(propValue);\n            if (propType !== \"object\") {\n                return new PropTypeError(\"Invalid \" + location + \" `\" + propFullName + \"` of type `\" + propType + \"` \" + (\"supplied to `\" + componentName + \"`, expected `object`.\"));\n            }\n            // We need to check all keys in case some are required but missing from props.\n            var allKeys = assign({}, props[propName], shapeTypes);\n            for(var key in allKeys){\n                var checker = shapeTypes[key];\n                if (has(shapeTypes, key) && typeof checker !== \"function\") {\n                    return invalidValidatorError(componentName, location, propFullName, key, getPreciseType(checker));\n                }\n                if (!checker) {\n                    return new PropTypeError(\"Invalid \" + location + \" `\" + propFullName + \"` key `\" + key + \"` supplied to `\" + componentName + \"`.\" + \"\\nBad object: \" + JSON.stringify(props[propName], null, \"  \") + \"\\nValid keys: \" + JSON.stringify(Object.keys(shapeTypes), null, \"  \"));\n                }\n                var error = checker(propValue, key, componentName, location, propFullName + \".\" + key, ReactPropTypesSecret);\n                if (error) {\n                    return error;\n                }\n            }\n            return null;\n        }\n        return createChainableTypeChecker(validate);\n    }\n    function isNode(propValue) {\n        switch(typeof propValue){\n            case \"number\":\n            case \"string\":\n            case \"undefined\":\n                return true;\n            case \"boolean\":\n                return !propValue;\n            case \"object\":\n                if (Array.isArray(propValue)) {\n                    return propValue.every(isNode);\n                }\n                if (propValue === null || isValidElement(propValue)) {\n                    return true;\n                }\n                var iteratorFn = getIteratorFn(propValue);\n                if (iteratorFn) {\n                    var iterator = iteratorFn.call(propValue);\n                    var step;\n                    if (iteratorFn !== propValue.entries) {\n                        while(!(step = iterator.next()).done){\n                            if (!isNode(step.value)) {\n                                return false;\n                            }\n                        }\n                    } else {\n                        // Iterator will provide entry [k,v] tuples rather than values.\n                        while(!(step = iterator.next()).done){\n                            var entry = step.value;\n                            if (entry) {\n                                if (!isNode(entry[1])) {\n                                    return false;\n                                }\n                            }\n                        }\n                    }\n                } else {\n                    return false;\n                }\n                return true;\n            default:\n                return false;\n        }\n    }\n    function isSymbol(propType, propValue) {\n        // Native Symbol.\n        if (propType === \"symbol\") {\n            return true;\n        }\n        // falsy value can't be a Symbol\n        if (!propValue) {\n            return false;\n        }\n        // 19.4.3.5 Symbol.prototype[@@toStringTag] === 'Symbol'\n        if (propValue[\"@@toStringTag\"] === \"Symbol\") {\n            return true;\n        }\n        // Fallback for non-spec compliant Symbols which are polyfilled.\n        if (typeof Symbol === \"function\" && propValue instanceof Symbol) {\n            return true;\n        }\n        return false;\n    }\n    // Equivalent of `typeof` but with special handling for array and regexp.\n    function getPropType(propValue) {\n        var propType = typeof propValue;\n        if (Array.isArray(propValue)) {\n            return \"array\";\n        }\n        if (propValue instanceof RegExp) {\n            // Old webkits (at least until Android 4.0) return 'function' rather than\n            // 'object' for typeof a RegExp. We'll normalize this here so that /bla/\n            // passes PropTypes.object.\n            return \"object\";\n        }\n        if (isSymbol(propType, propValue)) {\n            return \"symbol\";\n        }\n        return propType;\n    }\n    // This handles more types than `getPropType`. Only used for error messages.\n    // See `createPrimitiveTypeChecker`.\n    function getPreciseType(propValue) {\n        if (typeof propValue === \"undefined\" || propValue === null) {\n            return \"\" + propValue;\n        }\n        var propType = getPropType(propValue);\n        if (propType === \"object\") {\n            if (propValue instanceof Date) {\n                return \"date\";\n            } else if (propValue instanceof RegExp) {\n                return \"regexp\";\n            }\n        }\n        return propType;\n    }\n    // Returns a string that is postfixed to a warning about an invalid type.\n    // For example, \"undefined\" or \"of type array\"\n    function getPostfixForTypeWarning(value) {\n        var type = getPreciseType(value);\n        switch(type){\n            case \"array\":\n            case \"object\":\n                return \"an \" + type;\n            case \"boolean\":\n            case \"date\":\n            case \"regexp\":\n                return \"a \" + type;\n            default:\n                return type;\n        }\n    }\n    // Returns class name of the object, if any.\n    function getClassName(propValue) {\n        if (!propValue.constructor || !propValue.constructor.name) {\n            return ANONYMOUS;\n        }\n        return propValue.constructor.name;\n    }\n    ReactPropTypes.checkPropTypes = checkPropTypes;\n    ReactPropTypes.resetWarningCache = checkPropTypes.resetWarningCache;\n    ReactPropTypes.PropTypes = ReactPropTypes;\n    return ReactPropTypes;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/prop-types/factoryWithTypeCheckers.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/prop-types/index.js":
/*!******************************************!*\
  !*** ./node_modules/prop-types/index.js ***!
  \******************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */ if (true) {\n    var ReactIs = __webpack_require__(/*! react-is */ \"(ssr)/./node_modules/react-is/index.js\");\n    // By explicitly using `prop-types` you are opting into new development behavior.\n    // http://fb.me/prop-types-in-prod\n    var throwOnDirectAccess = true;\n    module.exports = __webpack_require__(/*! ./factoryWithTypeCheckers */ \"(ssr)/./node_modules/prop-types/factoryWithTypeCheckers.js\")(ReactIs.isElement, throwOnDirectAccess);\n} else {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcHJvcC10eXBlcy9pbmRleC5qcyIsIm1hcHBpbmdzIjoiQUFBQTs7Ozs7Q0FLQyxHQUVELElBQUlBLElBQXlCLEVBQWM7SUFDekMsSUFBSUMsVUFBVUMsbUJBQU9BLENBQUM7SUFFdEIsaUZBQWlGO0lBQ2pGLGtDQUFrQztJQUNsQyxJQUFJQyxzQkFBc0I7SUFDMUJDLE9BQU9DLE9BQU8sR0FBR0gsbUJBQU9BLENBQUMsK0ZBQTZCRCxRQUFRSyxTQUFTLEVBQUVIO0FBQzNFLE9BQU8sRUFJTiIsInNvdXJjZXMiOlsid2VicGFjazovL3Bvc2l0aXZlNy10b3VyaXNtLXdlYnNpdGUvLi9ub2RlX21vZHVsZXMvcHJvcC10eXBlcy9pbmRleC5qcz9iZGUxIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQ29weXJpZ2h0IChjKSAyMDEzLXByZXNlbnQsIEZhY2Vib29rLCBJbmMuXG4gKlxuICogVGhpcyBzb3VyY2UgY29kZSBpcyBsaWNlbnNlZCB1bmRlciB0aGUgTUlUIGxpY2Vuc2UgZm91bmQgaW4gdGhlXG4gKiBMSUNFTlNFIGZpbGUgaW4gdGhlIHJvb3QgZGlyZWN0b3J5IG9mIHRoaXMgc291cmNlIHRyZWUuXG4gKi9cblxuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WICE9PSAncHJvZHVjdGlvbicpIHtcbiAgdmFyIFJlYWN0SXMgPSByZXF1aXJlKCdyZWFjdC1pcycpO1xuXG4gIC8vIEJ5IGV4cGxpY2l0bHkgdXNpbmcgYHByb3AtdHlwZXNgIHlvdSBhcmUgb3B0aW5nIGludG8gbmV3IGRldmVsb3BtZW50IGJlaGF2aW9yLlxuICAvLyBodHRwOi8vZmIubWUvcHJvcC10eXBlcy1pbi1wcm9kXG4gIHZhciB0aHJvd09uRGlyZWN0QWNjZXNzID0gdHJ1ZTtcbiAgbW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2ZhY3RvcnlXaXRoVHlwZUNoZWNrZXJzJykoUmVhY3RJcy5pc0VsZW1lbnQsIHRocm93T25EaXJlY3RBY2Nlc3MpO1xufSBlbHNlIHtcbiAgLy8gQnkgZXhwbGljaXRseSB1c2luZyBgcHJvcC10eXBlc2AgeW91IGFyZSBvcHRpbmcgaW50byBuZXcgcHJvZHVjdGlvbiBiZWhhdmlvci5cbiAgLy8gaHR0cDovL2ZiLm1lL3Byb3AtdHlwZXMtaW4tcHJvZFxuICBtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vZmFjdG9yeVdpdGhUaHJvd2luZ1NoaW1zJykoKTtcbn1cbiJdLCJuYW1lcyI6WyJwcm9jZXNzIiwiUmVhY3RJcyIsInJlcXVpcmUiLCJ0aHJvd09uRGlyZWN0QWNjZXNzIiwibW9kdWxlIiwiZXhwb3J0cyIsImlzRWxlbWVudCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/prop-types/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/prop-types/lib/ReactPropTypesSecret.js":
/*!*************************************************************!*\
  !*** ./node_modules/prop-types/lib/ReactPropTypesSecret.js ***!
  \*************************************************************/
/***/ ((module) => {

"use strict";
eval("/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */ \nvar ReactPropTypesSecret = \"SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED\";\nmodule.exports = ReactPropTypesSecret;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcHJvcC10eXBlcy9saWIvUmVhY3RQcm9wVHlwZXNTZWNyZXQuanMiLCJtYXBwaW5ncyI6IkFBQUE7Ozs7O0NBS0MsR0FFRDtBQUVBLElBQUlBLHVCQUF1QjtBQUUzQkMsT0FBT0MsT0FBTyxHQUFHRiIsInNvdXJjZXMiOlsid2VicGFjazovL3Bvc2l0aXZlNy10b3VyaXNtLXdlYnNpdGUvLi9ub2RlX21vZHVsZXMvcHJvcC10eXBlcy9saWIvUmVhY3RQcm9wVHlwZXNTZWNyZXQuanM/ZTA5OCJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIENvcHlyaWdodCAoYykgMjAxMy1wcmVzZW50LCBGYWNlYm9vaywgSW5jLlxuICpcbiAqIFRoaXMgc291cmNlIGNvZGUgaXMgbGljZW5zZWQgdW5kZXIgdGhlIE1JVCBsaWNlbnNlIGZvdW5kIGluIHRoZVxuICogTElDRU5TRSBmaWxlIGluIHRoZSByb290IGRpcmVjdG9yeSBvZiB0aGlzIHNvdXJjZSB0cmVlLlxuICovXG5cbid1c2Ugc3RyaWN0JztcblxudmFyIFJlYWN0UHJvcFR5cGVzU2VjcmV0ID0gJ1NFQ1JFVF9ET19OT1RfUEFTU19USElTX09SX1lPVV9XSUxMX0JFX0ZJUkVEJztcblxubW9kdWxlLmV4cG9ydHMgPSBSZWFjdFByb3BUeXBlc1NlY3JldDtcbiJdLCJuYW1lcyI6WyJSZWFjdFByb3BUeXBlc1NlY3JldCIsIm1vZHVsZSIsImV4cG9ydHMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/prop-types/lib/ReactPropTypesSecret.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/prop-types/lib/has.js":
/*!********************************************!*\
  !*** ./node_modules/prop-types/lib/has.js ***!
  \********************************************/
/***/ ((module) => {

eval("module.exports = Function.call.bind(Object.prototype.hasOwnProperty);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9wb3NpdGl2ZTctdG91cmlzbS13ZWJzaXRlLy4vbm9kZV9tb2R1bGVzL3Byb3AtdHlwZXMvbGliL2hhcy5qcz9lNDcwIl0sInNvdXJjZXNDb250ZW50IjpbIm1vZHVsZS5leHBvcnRzID0gRnVuY3Rpb24uY2FsbC5iaW5kKE9iamVjdC5wcm90b3R5cGUuaGFzT3duUHJvcGVydHkpO1xuIl0sIm5hbWVzIjpbIm1vZHVsZSIsImV4cG9ydHMiLCJGdW5jdGlvbiIsImNhbGwiLCJiaW5kIiwiT2JqZWN0IiwicHJvdG90eXBlIiwiaGFzT3duUHJvcGVydHkiXSwibWFwcGluZ3MiOiJBQUFBQSxPQUFPQyxPQUFPLEdBQUdDLFNBQVNDLElBQUksQ0FBQ0MsSUFBSSxDQUFDQyxPQUFPQyxTQUFTLENBQUNDLGNBQWMiLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcHJvcC10eXBlcy9saWIvaGFzLmpzIiwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/prop-types/lib/has.js\n");

/***/ })

};
;