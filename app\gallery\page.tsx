import { Metadata } from 'next'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'
import GalleryClient from '@/components/gallery/GalleryClient'

export const metadata: Metadata = {
  title: 'Gallery - Positive7 Educational Tours',
  description: 'Explore our photo gallery showcasing memorable moments from educational tours, adventure camps, and student trips across India.',
  keywords: 'photo gallery, educational tours, student trips, adventure camps, travel photos, Positive7'
}

// Sample gallery data (this would come from database in real implementation)
const galleryImages = [
  {
    id: '1',
    title: 'Manali Adventure Camp',
    category: 'Adventure',
    location: 'Manali, Himachal Pradesh',
    imageUrl: '/images/trips/gettyimages-1134041601-612x612-1.jpg',
    description: 'Students enjoying snow activities in the beautiful mountains of Manali'
  },
  {
    id: '2',
    title: 'Rishikesh Yoga Session',
    category: 'Spiritual',
    location: 'Rishikesh, Uttarakhand',
    imageUrl: '/images/trips/dusk-time-rishikesh-holy-town-travel-destination-india-1024x684.jpg',
    description: 'Morning yoga session by the holy Ganges river'
  },
  {
    id: '3',
    title: 'Tirthan Valley Nature Walk',
    category: 'Nature',
    location: 'Tirthan Valley, Himachal Pradesh',
    imageUrl: '/images/trips/Tirthan-Valley-Himalayan-1-scaled-qi2e45mk4qblqepqjw0jqlabp95dlyg00al0h5hit8.webp',
    description: 'Exploring the pristine nature of Tirthan Valley'
  },
  {
    id: '4',
    title: 'Dharamshala Cultural Tour',
    category: 'Cultural',
    location: 'Dharamshala, Himachal Pradesh',
    imageUrl: '/images/trips/AMRITSAR-DHARAMSHALA-MCLEODGANJ-TRIUND-DALHOUSIE3.webp',
    description: 'Students learning about Tibetan culture and traditions'
  },
  {
    id: '5',
    title: 'Rajpura Temple Visit',
    category: 'Religious',
    location: 'Rajpura, Rajasthan',
    imageUrl: '/images/trips/Jirawala-Parshwanath-Jain-Tirth-scaled.jpg',
    description: 'Spiritual journey to the famous Sundha Mata temple'
  },
  {
    id: '6',
    title: 'Brigu Lake Trek',
    category: 'Trekking',
    location: 'Manali, Himachal Pradesh',
    imageUrl: '/images/trips/bhrigu-lake3.webp',
    description: 'Challenging trek to the beautiful Brigu Lake'
  },
  {
    id: '7',
    title: 'Udbhav Rural Initiative',
    category: 'Rural',
    location: 'Gujarat Villages',
    imageUrl: '/images/udbhav/Udbhav.jpg',
    description: 'Students connecting with rural communities and traditions'
  },
  {
    id: '8',
    title: 'Udbhav Cultural Exchange',
    category: 'Rural',
    location: 'Gujarat Villages',
    imageUrl: '/images/udbhav/Udbhav-2-scaled.jpg',
    description: 'Learning traditional arts and crafts from local artisans'
  },
  {
    id: '9',
    title: 'Adventure Activities',
    category: 'Adventure',
    location: 'Various Locations',
    imageUrl: '/images/udbhav/Udbhav-1-scaled.jpg',
    description: 'Exciting outdoor activities and team building exercises'
  },
  {
    id: '10',
    title: 'Group Activities',
    category: 'Educational',
    location: 'Camp Sites',
    imageUrl: '/images/udbhav/Udbhav-3-1024x467.jpg',
    description: 'Students participating in educational group activities'
  },
  {
    id: '11',
    title: 'Ranthambore Safari',
    category: 'Wildlife',
    location: 'Ranthambore, Rajasthan',
    imageUrl: '/images/trips/Ranthambore-Rajasthan-scaled.webp',
    description: 'Wildlife safari in the famous Ranthambore National Park'
  },
  {
    id: '12',
    title: 'Kumbhalgarh Fort',
    category: 'Historical',
    location: 'Kumbhalgarh, Rajasthan',
    imageUrl: '/images/trips/kumbhalgarh-fort-india-scaled.webp',
    description: 'Exploring the magnificent Kumbhalgarh Fort'
  }
]

export default function GalleryPage() {
  return (
    <>
      <Header />
      <main className="flex-1">
        <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50">
          <div className="max-w-7xl mx-auto px-4 py-8">
            <GalleryClient images={galleryImages} />
          </div>
        </div>
      </main>
      <Footer />
    </>
  )
}
