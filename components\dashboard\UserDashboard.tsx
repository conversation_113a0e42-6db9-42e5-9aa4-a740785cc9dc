'use client'

import { useState, useEffect } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { motion } from 'framer-motion'
import {
  User,
  Calendar,
  Heart,
  Settings,
  MapPin,
  Clock,
  Star,
  Bell,
  CreditCard,
  Download,
  Eye,
  Edit,
  Trash2,
  Plus,
  Filter,
  Search
} from 'lucide-react'
import Image from 'next/image'
import Link from 'next/link'
import Button from '@/components/ui/Button'
import { useAuth } from '@/contexts/AuthContext'
import { createClientSupabase } from '@/lib/supabase-client'
import ProfileManagement from './ProfileManagement'

interface Booking {
  id: string
  tripTitle: string
  destination: string
  startDate: string
  endDate: string
  participants: number
  status: 'confirmed' | 'pending' | 'cancelled' | 'completed'
  totalAmount: number
  image: string
  bookingReference: string
}

interface SavedTrip {
  id: string
  title: string
  destination: string
  price: number
  image: string
  rating: number
  duration: string
  savedAt: string
}

interface UserPreferences {
  destinations: string[]
  budgetRange: { min: number; max: number }
  travelStyle: string[]
  groupSize: string
  notifications: {
    email: boolean
    sms: boolean
    push: boolean
  }
}

export default function UserDashboard() {
  const { user, loading: authLoading } = useAuth()
  const router = useRouter()
  const searchParams = useSearchParams()
  const [activeTab, setActiveTab] = useState('overview')
  const [bookings, setBookings] = useState<Booking[]>([])
  const [savedTrips, setSavedTrips] = useState<SavedTrip[]>([])
  const [preferences, setPreferences] = useState<UserPreferences | null>(null)
  const [loading, setLoading] = useState(true)

  // Handle tab parameter from URL
  useEffect(() => {
    const tab = searchParams.get('tab')
    if (tab && ['overview', 'bookings', 'saved', 'profile', 'preferences'].includes(tab)) {
      setActiveTab(tab)
    }
  }, [searchParams])

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!authLoading && !user) {
      router.push('/auth/login')
      return
    }
  }, [user, authLoading, router])

  // Fetch real user data
  useEffect(() => {
    const fetchUserData = async () => {
      if (!user) return

      try {
        const supabase = createClientSupabase()

        // Fetch user's bookings directly from Supabase
        const { data: bookingsData, error: bookingsError } = await supabase
          .from('bookings')
          .select(`
            id,
            booking_reference,
            booking_date,
            number_of_participants,
            total_amount,
            status,
            participants,
            emergency_contact,
            payment_details,
            special_requirements,
            created_at,
            updated_at,
            trips (
              id,
              title,
              destination,
              duration_days,
              featured_image_url
            )
          `)
          .eq('user_id', user.id)
          .order('created_at', { ascending: false })

        if (bookingsError) {
          console.error('Error fetching bookings:', bookingsError)
        } else {
          // Transform the data to match our interface
          const transformedBookings = bookingsData?.map((booking: any) => ({
            id: booking.id,
            tripTitle: booking.trips?.title || 'Unknown Trip',
            destination: booking.trips?.destination || 'Unknown Destination',
            startDate: booking.booking_date,
            endDate: booking.booking_date, // Using booking_date for both
            participants: booking.number_of_participants,
            status: booking.status,
            totalAmount: parseFloat(booking.total_amount || '0'),
            image: booking.trips?.featured_image_url || 'https://images.unsplash.com/photo-1469474968028-56623f02e42e?w=400&h=300&fit=crop',
            bookingReference: booking.booking_reference
          })) || []
          setBookings(transformedBookings)
        }

        // Set default preferences for now
        setPreferences({
          destinations: ['Rajasthan', 'Kerala', 'Himachal Pradesh'],
          budgetRange: { min: 5000, max: 15000 },
          travelStyle: ['Cultural', 'Adventure', 'Educational'],
          groupSize: '20-30 students',
          notifications: {
            email: true,
            sms: false,
            push: true
          }
        })

        setLoading(false)
      } catch (error) {
        console.error('Error fetching user data:', error)
        setLoading(false)
      }
    }

    fetchUserData()
  }, [user])

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed': return 'text-green-600 bg-green-100'
      case 'pending': return 'text-yellow-600 bg-yellow-100'
      case 'cancelled': return 'text-red-600 bg-red-100'
      case 'completed': return 'text-blue-600 bg-blue-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      day: 'numeric',
      month: 'short',
      year: 'numeric'
    })
  }

  const tabs = [
    { id: 'overview', label: 'Overview', icon: User },
    { id: 'bookings', label: 'My Bookings', icon: Calendar },
    { id: 'saved', label: 'Saved Trips', icon: Heart },
    { id: 'profile', label: 'Profile', icon: Settings },
    { id: 'preferences', label: 'Preferences', icon: Bell }
  ]

  // Show loading while checking auth or fetching data
  if (authLoading || loading || !user) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">Welcome back, {user?.full_name || 'User'}!</h1>
        <p className="text-gray-600 mt-2">Manage your educational tours and preferences</p>
      </div>

      {/* Tab Navigation */}
      <div className="border-b border-gray-200 mb-8">
        <nav className="-mb-px flex space-x-8">
          {tabs.map((tab) => {
            const Icon = tab.icon
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center gap-2 ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Icon className="w-4 h-4" />
                {tab.label}
              </button>
            )
          })}
        </nav>
      </div>

      {/* Tab Content */}
      <motion.div
        key={activeTab}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        {activeTab === 'overview' && (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Quick Stats */}
            <div className="lg:col-span-2 space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="bg-white p-6 rounded-xl shadow-sm border">
                  <div className="flex items-center">
                    <div className="p-2 bg-blue-100 rounded-lg">
                      <Calendar className="w-6 h-6 text-blue-600" />
                    </div>
                    <div className="ml-4">
                      <p className="text-sm font-medium text-gray-600">Total Bookings</p>
                      <p className="text-2xl font-bold text-gray-900">{bookings.length}</p>
                    </div>
                  </div>
                </div>

                <div className="bg-white p-6 rounded-xl shadow-sm border">
                  <div className="flex items-center">
                    <div className="p-2 bg-green-100 rounded-lg">
                      <Heart className="w-6 h-6 text-green-600" />
                    </div>
                    <div className="ml-4">
                      <p className="text-sm font-medium text-gray-600">Saved Trips</p>
                      <p className="text-2xl font-bold text-gray-900">{savedTrips.length}</p>
                    </div>
                  </div>
                </div>

                <div className="bg-white p-6 rounded-xl shadow-sm border">
                  <div className="flex items-center">
                    <div className="p-2 bg-purple-100 rounded-lg">
                      <MapPin className="w-6 h-6 text-purple-600" />
                    </div>
                    <div className="ml-4">
                      <p className="text-sm font-medium text-gray-600">Destinations</p>
                      <p className="text-2xl font-bold text-gray-900">{preferences?.destinations.length || 0}</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Recent Bookings */}
              <div className="bg-white rounded-xl shadow-sm border">
                <div className="p-6 border-b border-gray-200">
                  <h3 className="text-lg font-semibold text-gray-900">Recent Bookings</h3>
                </div>
                <div className="p-6">
                  {bookings.slice(0, 2).map((booking) => (
                    <div key={booking.id} className="flex items-center justify-between py-4 border-b border-gray-100 last:border-b-0">
                      <div className="flex items-center">
                        <div className="relative w-16 h-16 rounded-lg overflow-hidden">
                          <Image
                            src={booking.image}
                            alt={booking.tripTitle}
                            fill
                            className="object-cover"
                          />
                        </div>
                        <div className="ml-4">
                          <h4 className="font-medium text-gray-900">{booking.tripTitle}</h4>
                          <p className="text-sm text-gray-600">{booking.destination}</p>
                          <p className="text-sm text-gray-500">{formatDate(booking.startDate)} - {formatDate(booking.endDate)}</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(booking.status)}`}>
                          {booking.status.charAt(0).toUpperCase() + booking.status.slice(1)}
                        </span>
                        <p className="text-sm font-medium text-gray-900 mt-1">₹{booking.totalAmount.toLocaleString()}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* Profile Card */}
              <div className="bg-white rounded-xl shadow-sm border p-6">
                <div className="text-center">
                  <div className="w-20 h-20 bg-gradient-to-r from-blue-600 to-green-600 rounded-full flex items-center justify-center text-white text-2xl font-bold mx-auto mb-4">
                    {user?.full_name?.charAt(0) || 'U'}
                  </div>
                  <h3 className="font-semibold text-gray-900">{user?.full_name || 'User'}</h3>
                  <p className="text-sm text-gray-600">{user?.email || '<EMAIL>'}</p>
                  <Button
                    variant="outline"
                    size="sm"
                    className="mt-4"
                    onClick={() => setActiveTab('profile')}
                  >
                    <Edit className="w-4 h-4 mr-2" />
                    Edit Profile
                  </Button>
                </div>
              </div>

              {/* Quick Actions */}
              <div className="bg-white rounded-xl shadow-sm border p-6">
                <h3 className="font-semibold text-gray-900 mb-4">Quick Actions</h3>
                <div className="space-y-3">
                  <Link href="/search">
                    <Button variant="outline" className="w-full justify-start">
                      <Search className="w-4 h-4 mr-2" />
                      Browse Trips
                    </Button>
                  </Link>
                  <Button variant="outline" className="w-full justify-start">
                    <Bell className="w-4 h-4 mr-2" />
                    Notifications
                  </Button>
                  <Button variant="outline" className="w-full justify-start">
                    <Download className="w-4 h-4 mr-2" />
                    Download Receipts
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'bookings' && (
          <div className="space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="text-2xl font-bold text-gray-900">My Bookings</h2>
              <div className="flex gap-4">
                <select className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                  <option value="">All Status</option>
                  <option value="confirmed">Confirmed</option>
                  <option value="pending">Pending</option>
                  <option value="completed">Completed</option>
                  <option value="cancelled">Cancelled</option>
                </select>
                <Link href="/search">
                  <Button>
                    <Plus className="w-4 h-4 mr-2" />
                    New Booking
                  </Button>
                </Link>
              </div>
            </div>

            <div className="grid gap-6">
              {bookings.map((booking) => (
                <div key={booking.id} className="bg-white rounded-xl shadow-sm border overflow-hidden">
                  <div className="p-6">
                    <div className="flex items-start justify-between">
                      <div className="flex">
                        <div className="relative w-24 h-24 rounded-lg overflow-hidden">
                          <Image
                            src={booking.image}
                            alt={booking.tripTitle}
                            fill
                            className="object-cover"
                          />
                        </div>
                        <div className="ml-6">
                          <h3 className="text-xl font-semibold text-gray-900">{booking.tripTitle}</h3>
                          <p className="text-gray-600 mt-1">{booking.destination}</p>
                          <div className="flex items-center gap-4 mt-3 text-sm text-gray-500">
                            <div className="flex items-center">
                              <Calendar className="w-4 h-4 mr-1" />
                              {formatDate(booking.startDate)} - {formatDate(booking.endDate)}
                            </div>
                            <div className="flex items-center">
                              <User className="w-4 h-4 mr-1" />
                              {booking.participants} participants
                            </div>
                          </div>
                          <p className="text-sm text-gray-500 mt-2">Booking Ref: {booking.bookingReference}</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <span className={`inline-flex px-3 py-1 text-sm font-medium rounded-full ${getStatusColor(booking.status)}`}>
                          {booking.status.charAt(0).toUpperCase() + booking.status.slice(1)}
                        </span>
                        <p className="text-2xl font-bold text-gray-900 mt-2">₹{booking.totalAmount.toLocaleString()}</p>
                      </div>
                    </div>
                    <div className="flex justify-end gap-3 mt-6">
                      <Button variant="outline" size="sm">
                        <Eye className="w-4 h-4 mr-2" />
                        View Details
                      </Button>
                      <Button variant="outline" size="sm">
                        <Download className="w-4 h-4 mr-2" />
                        Download
                      </Button>
                      {booking.status === 'pending' && (
                        <Button variant="outline" size="sm" className="text-red-600 hover:text-red-700">
                          <Trash2 className="w-4 h-4 mr-2" />
                          Cancel
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {activeTab === 'saved' && (
          <div className="space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="text-2xl font-bold text-gray-900">Saved Trips</h2>
              <p className="text-gray-600">{savedTrips.length} trips saved</p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {savedTrips.map((trip) => (
                <div key={trip.id} className="bg-white rounded-xl shadow-sm border overflow-hidden group hover:shadow-lg transition-shadow duration-300">
                  <div className="relative h-48">
                    <Image
                      src={trip.image}
                      alt={trip.title}
                      fill
                      className="object-cover group-hover:scale-105 transition-transform duration-300"
                    />
                    <button className="absolute top-3 right-3 p-2 bg-white/90 rounded-full hover:bg-white transition-colors">
                      <Heart className="w-5 h-5 text-red-500 fill-current" />
                    </button>
                  </div>
                  <div className="p-6">
                    <h3 className="font-semibold text-gray-900 mb-2">{trip.title}</h3>
                    <p className="text-gray-600 text-sm mb-3">{trip.destination}</p>
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center">
                        <Star className="w-4 h-4 text-yellow-400 fill-current" />
                        <span className="text-sm text-gray-600 ml-1">{trip.rating}</span>
                      </div>
                      <span className="text-sm text-gray-500">{trip.duration}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-xl font-bold text-blue-600">₹{trip.price.toLocaleString()}</span>
                      <Link href={`/trips/${trip.id}`}>
                        <Button size="sm">View Details</Button>
                      </Link>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {activeTab === 'profile' && (
          <ProfileManagement />
        )}

        {activeTab === 'preferences' && preferences && (
          <div className="space-y-8">
            <h2 className="text-2xl font-bold text-gray-900">Travel Preferences</h2>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Destination Preferences */}
              <div className="bg-white rounded-xl shadow-sm border p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Preferred Destinations</h3>
                <div className="flex flex-wrap gap-2 mb-4">
                  {preferences.destinations.map((destination, index) => (
                    <span key={index} className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm">
                      {destination}
                    </span>
                  ))}
                </div>
                <Button variant="outline" size="sm">
                  <Edit className="w-4 h-4 mr-2" />
                  Edit Destinations
                </Button>
              </div>

              {/* Budget Range */}
              <div className="bg-white rounded-xl shadow-sm border p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Budget Range</h3>
                <div className="mb-4">
                  <p className="text-2xl font-bold text-green-600">
                    ₹{preferences.budgetRange.min.toLocaleString()} - ₹{preferences.budgetRange.max.toLocaleString()}
                  </p>
                  <p className="text-sm text-gray-600">Per person</p>
                </div>
                <Button variant="outline" size="sm">
                  <Edit className="w-4 h-4 mr-2" />
                  Update Budget
                </Button>
              </div>

              {/* Travel Style */}
              <div className="bg-white rounded-xl shadow-sm border p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Travel Style</h3>
                <div className="flex flex-wrap gap-2 mb-4">
                  {preferences.travelStyle.map((style, index) => (
                    <span key={index} className="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm">
                      {style}
                    </span>
                  ))}
                </div>
                <Button variant="outline" size="sm">
                  <Edit className="w-4 h-4 mr-2" />
                  Update Style
                </Button>
              </div>

              {/* Group Size */}
              <div className="bg-white rounded-xl shadow-sm border p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Preferred Group Size</h3>
                <p className="text-xl font-semibold text-gray-900 mb-4">{preferences.groupSize}</p>
                <Button variant="outline" size="sm">
                  <Edit className="w-4 h-4 mr-2" />
                  Change Size
                </Button>
              </div>
            </div>

            {/* Notification Preferences */}
            <div className="bg-white rounded-xl shadow-sm border p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-6">Notification Preferences</h3>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium text-gray-900">Email Notifications</p>
                    <p className="text-sm text-gray-600">Receive booking updates and offers via email</p>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={preferences.notifications.email}
                      className="sr-only peer"
                      onChange={() => {}}
                    />
                    <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                  </label>
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium text-gray-900">SMS Notifications</p>
                    <p className="text-sm text-gray-600">Get important updates via SMS</p>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={preferences.notifications.sms}
                      className="sr-only peer"
                      onChange={() => {}}
                    />
                    <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                  </label>
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium text-gray-900">Push Notifications</p>
                    <p className="text-sm text-gray-600">Receive push notifications on your device</p>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={preferences.notifications.push}
                      className="sr-only peer"
                      onChange={() => {}}
                    />
                    <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                  </label>
                </div>
              </div>
            </div>
          </div>
        )}
      </motion.div>
    </div>
  )
}
