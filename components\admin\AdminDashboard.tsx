'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { motion } from 'framer-motion'
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  AreaChart,
  Area
} from 'recharts'
import {
  TrendingUp,
  TrendingDown,
  Users,
  MapPin,
  Calendar,
  DollarSign,
  Star,
  Activity,
  Eye,
  Download,
  Filter,
  RefreshCw,
  BookOpen,
  MessageSquare,
  Settings,
  BarChart3
} from 'lucide-react'
import Link from 'next/link'
import Button from '@/components/ui/Button'
import { createClientSupabase } from '@/lib/supabase-client'
import { useAuth } from '@/contexts/AuthContext'

interface AnalyticsData {
  totalBookings: number
  totalRevenue: number
  averageRating: number
  activeUsers: number
  bookingTrend: Array<{ month: string; bookings: number; revenue: number }>
  popularDestinations: Array<{ name: string; bookings: number; revenue: number }>
  customerDemographics: Array<{ age: string; count: number }>
  seasonalTrends: Array<{ season: string; bookings: number }>
  tripPerformance: Array<{ trip: string; bookings: number; rating: number; revenue: number }>
}

export default function AdminDashboard() {
  const { user, loading: authLoading } = useAuth();
  const router = useRouter();
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null)
  const [loading, setLoading] = useState(true)
  const [timeRange, setTimeRange] = useState('6months')
  const [refreshing, setRefreshing] = useState(false)

  // Log auth state for debugging
  useEffect(() => {
    console.log('AdminDashboard: Auth state - authLoading:', authLoading, 'user:', user?.email, 'role:', user?.role);
  }, [user, authLoading]);

  // Fetch real analytics data from database
  useEffect(() => {
    const fetchAnalytics = async () => {
      // Only fetch if user is authenticated and is admin
      if (!user || user.role !== 'admin') {
        return;
      }

      setLoading(true);
      console.log('AdminDashboard: Fetching analytics data');
      try {
        const supabase = createClientSupabase()

        // Fetch total bookings
        const { data: bookingsData, error: bookingsError } = await supabase
          .from('bookings')
          .select('id, total_amount, created_at, status')

        // Fetch total users
        const { data: usersData, error: usersError } = await supabase
          .from('users')
          .select('id, created_at')

        // Fetch trips with booking counts
        const { data: tripsData, error: tripsError } = await supabase
          .from('trips')
          .select(`
            id,
            title,
            destination,
            bookings(id, total_amount, created_at)
          `)

        if (bookingsError || usersError || tripsError) {
          console.error('Error fetching analytics:', { bookingsError, usersError, tripsError })
          setLoading(false)
          return
        }

        // Calculate analytics
        const totalBookings = bookingsData?.length || 0
        const totalRevenue = bookingsData?.reduce((sum, booking) => sum + (parseFloat(booking.total_amount) || 0), 0) || 0
        const activeUsers = usersData?.length || 0

        // Calculate booking trends by month
        const bookingsByMonth = bookingsData?.reduce((acc: any, booking) => {
          const month = new Date(booking.created_at).toLocaleDateString('en-US', { month: 'short' })
          if (!acc[month]) {
            acc[month] = { bookings: 0, revenue: 0 }
          }
          acc[month].bookings += 1
          acc[month].revenue += parseFloat(booking.total_amount) || 0
          return acc
        }, {}) || {}

        const bookingTrend = Object.entries(bookingsByMonth).map(([month, data]: [string, any]) => ({
          month,
          bookings: data.bookings,
          revenue: data.revenue
        }))

        // Calculate popular destinations
        const destinationStats = tripsData?.reduce((acc: any, trip) => {
          const destination = trip.destination
          if (!acc[destination]) {
            acc[destination] = { bookings: 0, revenue: 0 }
          }
          const tripBookings = trip.bookings || []
          acc[destination].bookings += tripBookings.length
          acc[destination].revenue += tripBookings.reduce((sum: number, booking: any) =>
            sum + (parseFloat(booking.total_amount) || 0), 0)
          return acc
        }, {}) || {}

        const popularDestinations = Object.entries(destinationStats)
          .map(([name, stats]: [string, any]) => ({
            name,
            bookings: stats.bookings,
            revenue: stats.revenue
          }))
          .sort((a, b) => b.bookings - a.bookings)
          .slice(0, 5)

        // Calculate trip performance
        const tripPerformance = tripsData?.map(trip => {
          const tripBookings = trip.bookings || []
          const revenue = tripBookings.reduce((sum: number, booking: any) =>
            sum + (parseFloat(booking.total_amount) || 0), 0)
          return {
            trip: trip.title,
            bookings: tripBookings.length,
            rating: 4.5 + Math.random() * 0.5, // Placeholder rating
            revenue
          }
        }).sort((a, b) => b.bookings - a.bookings).slice(0, 5) || []

        setAnalyticsData({
          totalBookings,
          totalRevenue,
          averageRating: 4.7, // Placeholder - would need testimonials/reviews table
          activeUsers,
          bookingTrend,
          popularDestinations,
          customerDemographics: [
            { age: '15-18', count: Math.floor(activeUsers * 0.45) },
            { age: '18-25', count: Math.floor(activeUsers * 0.35) },
            { age: '25-35', count: Math.floor(activeUsers * 0.15) },
            { age: '35+', count: Math.floor(activeUsers * 0.05) }
          ],
          seasonalTrends: [
            { season: 'Spring', bookings: Math.floor(totalBookings * 0.25) },
            { season: 'Summer', bookings: Math.floor(totalBookings * 0.22) },
            { season: 'Monsoon', bookings: Math.floor(totalBookings * 0.18) },
            { season: 'Winter', bookings: Math.floor(totalBookings * 0.35) }
          ],
          tripPerformance
        })
      } catch (error) {
        console.error('Error fetching analytics:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchAnalytics()
  }, [timeRange, user])

  const handleRefresh = async () => {
    setRefreshing(true)
    // Simulate refresh
    setTimeout(() => {
      setRefreshing(false)
    }, 1000)
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount)
  }

  const COLORS = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6']

  // Show loading while fetching data
  if (loading || !user || user.role !== 'admin') {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
        <div className="ml-4 text-gray-600">Loading analytics...</div>
      </div>
    );
  }

  if (!analyticsData) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="text-gray-500 mb-4">No analytics data available</div>
          <Button onClick={() => window.location.reload()}>Retry</Button>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Admin Dashboard</h1>
          <p className="text-gray-600 mt-2">Manage your business operations and view analytics</p>
        </div>
        <div className="flex gap-4">
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
          >
            <option value="1month">Last Month</option>
            <option value="3months">Last 3 Months</option>
            <option value="6months">Last 6 Months</option>
            <option value="1year">Last Year</option>
          </select>
          <Button
            onClick={handleRefresh}
            disabled={refreshing}
            variant="outline"
          >
            <RefreshCw className={`w-4 h-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button>
            <Download className="w-4 h-4 mr-2" />
            Export Report
          </Button>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <Link href="/admin/bookings">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white p-6 rounded-xl shadow-sm border hover:shadow-md transition-shadow cursor-pointer group"
          >
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 group-hover:text-blue-600">
                  Manage Bookings
                </h3>
                <p className="text-gray-600 mt-1">View and manage customer bookings</p>
              </div>
              <div className="p-3 bg-blue-100 rounded-lg group-hover:bg-blue-200 transition-colors">
                <Calendar className="w-6 h-6 text-blue-600" />
              </div>
            </div>
          </motion.div>
        </Link>

        <Link href="/admin/inquiries">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="bg-white p-6 rounded-xl shadow-sm border hover:shadow-md transition-shadow cursor-pointer group"
          >
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 group-hover:text-green-600">
                  Handle Inquiries
                </h3>
                <p className="text-gray-600 mt-1">Respond to customer inquiries</p>
              </div>
              <div className="p-3 bg-green-100 rounded-lg group-hover:bg-green-200 transition-colors">
                <MessageSquare className="w-6 h-6 text-green-600" />
              </div>
            </div>
          </motion.div>
        </Link>

        <Link href="/admin/trips">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="bg-white p-6 rounded-xl shadow-sm border hover:shadow-md transition-shadow cursor-pointer group"
          >
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 group-hover:text-purple-600">
                  Manage Trips
                </h3>
                <p className="text-gray-600 mt-1">Create and edit trip packages</p>
              </div>
              <div className="p-3 bg-purple-100 rounded-lg group-hover:bg-purple-200 transition-colors">
                <BookOpen className="w-6 h-6 text-purple-600" />
              </div>
            </div>
          </motion.div>
        </Link>

        <Link href="/admin/users">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="bg-white p-6 rounded-xl shadow-sm border hover:shadow-md transition-shadow cursor-pointer group"
          >
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 group-hover:text-orange-600">
                  User Management
                </h3>
                <p className="text-gray-600 mt-1">Manage user accounts and roles</p>
              </div>
              <div className="p-3 bg-orange-100 rounded-lg group-hover:bg-orange-200 transition-colors">
                <Users className="w-6 h-6 text-orange-600" />
              </div>
            </div>
          </motion.div>
        </Link>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white p-6 rounded-xl shadow-sm border"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Bookings</p>
              <p className="text-3xl font-bold text-gray-900">{analyticsData.totalBookings.toLocaleString()}</p>
              <div className="flex items-center mt-2">
                <TrendingUp className="w-4 h-4 text-green-500 mr-1" />
                <span className="text-sm text-green-600">+12.5% from last month</span>
              </div>
            </div>
            <div className="p-3 bg-blue-100 rounded-lg">
              <Calendar className="w-6 h-6 text-blue-600" />
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-white p-6 rounded-xl shadow-sm border"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Revenue</p>
              <p className="text-3xl font-bold text-gray-900">{formatCurrency(analyticsData.totalRevenue)}</p>
              <div className="flex items-center mt-2">
                <TrendingUp className="w-4 h-4 text-green-500 mr-1" />
                <span className="text-sm text-green-600">+18.2% from last month</span>
              </div>
            </div>
            <div className="p-3 bg-green-100 rounded-lg">
              <DollarSign className="w-6 h-6 text-green-600" />
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-white p-6 rounded-xl shadow-sm border"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Average Rating</p>
              <p className="text-3xl font-bold text-gray-900">{analyticsData.averageRating}</p>
              <div className="flex items-center mt-2">
                <TrendingUp className="w-4 h-4 text-green-500 mr-1" />
                <span className="text-sm text-green-600">+0.2 from last month</span>
              </div>
            </div>
            <div className="p-3 bg-yellow-100 rounded-lg">
              <Star className="w-6 h-6 text-yellow-600" />
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="bg-white p-6 rounded-xl shadow-sm border"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Active Users</p>
              <p className="text-3xl font-bold text-gray-900">{analyticsData.activeUsers.toLocaleString()}</p>
              <div className="flex items-center mt-2">
                <TrendingUp className="w-4 h-4 text-green-500 mr-1" />
                <span className="text-sm text-green-600">+8.1% from last month</span>
              </div>
            </div>
            <div className="p-3 bg-purple-100 rounded-lg">
              <Users className="w-6 h-6 text-purple-600" />
            </div>
          </div>
        </motion.div>
      </div>

      {/* Charts Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        {/* Booking Trend */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="bg-white p-6 rounded-xl shadow-sm border"
        >
          <h3 className="text-lg font-semibold text-gray-900 mb-6">Booking Trends</h3>
          <ResponsiveContainer width="100%" height={300}>
            <AreaChart data={analyticsData.bookingTrend}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis />
              <Tooltip />
              <Area
                type="monotone"
                dataKey="bookings"
                stroke="#3B82F6"
                fill="#3B82F6"
                fillOpacity={0.1}
              />
            </AreaChart>
          </ResponsiveContainer>
        </motion.div>

        {/* Revenue Trend */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
          className="bg-white p-6 rounded-xl shadow-sm border"
        >
          <h3 className="text-lg font-semibold text-gray-900 mb-6">Revenue Trends</h3>
          <ResponsiveContainer width="100%" height={300}>
            <LineChart data={analyticsData.bookingTrend}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis />
              <Tooltip formatter={(value) => [formatCurrency(value as number), 'Revenue']} />
              <Line
                type="monotone"
                dataKey="revenue"
                stroke="#10B981"
                strokeWidth={3}
                dot={{ fill: '#10B981', strokeWidth: 2, r: 4 }}
              />
            </LineChart>
          </ResponsiveContainer>
        </motion.div>
      </div>

      {/* Popular Destinations & Demographics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        {/* Popular Destinations */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
          className="bg-white p-6 rounded-xl shadow-sm border"
        >
          <h3 className="text-lg font-semibold text-gray-900 mb-6">Popular Destinations</h3>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={analyticsData.popularDestinations}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis />
              <Tooltip />
              <Bar dataKey="bookings" fill="#3B82F6" radius={[4, 4, 0, 0]} />
            </BarChart>
          </ResponsiveContainer>
        </motion.div>

        {/* Customer Demographics */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.7 }}
          className="bg-white p-6 rounded-xl shadow-sm border"
        >
          <h3 className="text-lg font-semibold text-gray-900 mb-6">Customer Demographics</h3>
          <ResponsiveContainer width="100%" height={300}>
            <PieChart>
              <Pie
                data={analyticsData.customerDemographics}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={({ age, percent }) => `${age}: ${(percent * 100).toFixed(0)}%`}
                outerRadius={80}
                fill="#8884d8"
                dataKey="count"
              >
                {analyticsData.customerDemographics.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                ))}
              </Pie>
              <Tooltip />
            </PieChart>
          </ResponsiveContainer>
        </motion.div>
      </div>

      {/* Trip Performance Table */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.8 }}
        className="bg-white rounded-xl shadow-sm border overflow-hidden"
      >
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Trip Performance</h3>
        </div>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Trip Name
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Bookings
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Rating
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Revenue
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {analyticsData.tripPerformance.map((trip, index) => (
                <tr key={index} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="font-medium text-gray-900">{trip.trip}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-gray-900">{trip.bookings}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <Star className="w-4 h-4 text-yellow-400 fill-current mr-1" />
                      <span className="text-gray-900">{trip.rating}</span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-gray-900">{formatCurrency(trip.revenue)}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <Button variant="outline" size="sm">
                      <Eye className="w-4 h-4 mr-2" />
                      View Details
                    </Button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </motion.div>
    </div>
  )
}
