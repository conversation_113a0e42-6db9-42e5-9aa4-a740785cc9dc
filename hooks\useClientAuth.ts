'use client'

import { useState, useEffect } from 'react'
import { createClientSupabase } from '@/lib/supabase-client'
import type { User } from '@/types/database'

export function useClientAuth() {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)
  const supabase = createClientSupabase()

  useEffect(() => {
    console.log('useClientAuth: Initializing...')
    
    const checkAuth = async () => {
      try {
        // Get current session
        const { data: { session }, error } = await supabase.auth.getSession()
        
        console.log('useClientAuth: Session check - session:', !!session, 'error:', error)
        
        if (error) {
          console.error('useClientAuth: Session error:', error)
          setLoading(false)
          return
        }

        if (session?.user) {
          console.log('useClientAuth: Found session for user:', session.user.email)
          
          // Fetch user profile
          const { data: userProfile, error: profileError } = await supabase
            .from('users')
            .select('*')
            .eq('id', session.user.id)
            .single()

          if (profileError) {
            console.error('useClientAuth: Profile error:', profileError)
          } else {
            console.log('useClientAuth: User profile loaded:', userProfile?.email, userProfile?.role)
            setUser(userProfile as User)
          }
        } else {
          console.log('useClientAuth: No session found')
          setUser(null)
        }
      } catch (error) {
        console.error('useClientAuth: Error:', error)
      } finally {
        setLoading(false)
      }
    }

    checkAuth()

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(async (event, session) => {
      console.log('useClientAuth: Auth state changed:', event, !!session)
      
      if (session?.user) {
        // Fetch user profile
        const { data: userProfile } = await supabase
          .from('users')
          .select('*')
          .eq('id', session.user.id)
          .single()

        setUser(userProfile as User)
      } else {
        setUser(null)
      }
    })

    return () => subscription.unsubscribe()
  }, [])

  return { user, loading }
}
