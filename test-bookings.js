// Test script to test bookings API
const BASE_URL = 'http://localhost:3001';

async function testLogin(email, password) {
  try {
    console.log(`🔄 Logging in as ${email}...`);
    
    const response = await fetch(`${BASE_URL}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include',
      body: JSON.stringify({ email, password }),
    });

    const data = await response.json();
    
    if (!response.ok) {
      console.error(`❌ Login failed:`, data.error);
      return null;
    }
    
    console.log(`✅ Login successful for ${data.user.full_name}`);
    
    // Extract cookies for subsequent requests
    const cookies = response.headers.get('set-cookie');
    return cookies;
  } catch (error) {
    console.error(`❌ Login error:`, error.message);
    return null;
  }
}

async function testBookings(cookies) {
  try {
    console.log(`\n🔄 Testing bookings API...`);
    
    const response = await fetch(`${BASE_URL}/api/bookings`, {
      method: 'GET',
      headers: {
        'Cookie': cookies || ''
      },
      credentials: 'include',
    });

    const data = await response.json();
    
    if (!response.ok) {
      console.error(`❌ Bookings API failed:`, data.error);
      return false;
    }
    
    console.log(`✅ Bookings API successful`);
    console.log(`   Found ${data.data?.length || 0} bookings`);
    
    if (data.data && data.data.length > 0) {
      console.log(`   First booking: ${data.data[0].booking_reference} - ${data.data[0].trip?.title}`);
    }
    
    return true;
  } catch (error) {
    console.error(`❌ Bookings API error:`, error.message);
    return false;
  }
}

async function runTests() {
  console.log('🚀 Starting bookings API tests...\n');

  // Test with customer user
  const cookies = await testLogin('<EMAIL>', 'customer123456');
  
  if (cookies) {
    await testBookings(cookies);
  }

  console.log('\n✨ Bookings API tests completed!');
}

// Run the tests
runTests().catch(console.error);
