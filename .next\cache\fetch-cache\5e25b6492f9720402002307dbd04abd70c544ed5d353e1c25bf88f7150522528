{"kind": "FETCH", "data": {"headers": {"alt-svc": "h3=\":443\"; ma=86400", "cf-cache-status": "DYNAMIC", "cf-ray": "947573a47985482c-BOM", "connection": "keep-alive", "content-encoding": "gzip", "content-location": "/trips?id=neq.58eb8753-9dd5-491a-bd62-e55aed3c3da3&is_active=eq.true&limit=3&select=id%2Ctitle%2Cslug%2Cdescription%2Cdestination%2Cduration_days%2Cprice_per_person%2Cdifficulty%2Cfeatured_image_url", "content-profile": "public", "content-range": "0-2/*", "content-type": "application/json; charset=utf-8", "date": "Thu, 29 May 2025 10:54:05 GMT", "sb-gateway-version": "1", "sb-project-ref": "soaoag<PERSON><PERSON><PERSON>", "server": "cloudflare", "set-cookie": "__cf_bm=LSLSIoA2xqtDSFGgbFdvU4e76suZ5nVLIbvfBuBbtnY-1748516045-*******-RCwIAGT.JoSN3xPf0Ci7oWu1_ER.MAR2aIY.KKdJ3HIU.V6hS8u5LXVMmWURSBL6RDgkoPxj3cAD7uoMC78mijRa_DFkfT.MWjbH3.WNb58; path=/; expires=Thu, 29-May-25 11:24:05 GMT; domain=.supabase.co; HttpOnly; Secure; SameSite=None", "strict-transport-security": "max-age=********; includeSubDomains; preload", "transfer-encoding": "chunked", "vary": "Accept-Encoding", "x-content-type-options": "nosniff", "x-envoy-attempt-count": "1", "x-envoy-upstream-service-time": "2"}, "body": "W3siaWQiOiI3N2NlYWVkYi1lYjdjLTQ5MGItYmM4YS1lZWU1NWE3MWM4YTkiLCJ0aXRsZSI6Ik1hbmFsaSBBZHZlbnR1cmUgVG91ciIsInNsdWciOiJtYW5hbGktYWR2ZW50dXJlLXRvdXIiLCJkZXNjcmlwdGlvbiI6IkV4cGVyaWVuY2UgdGhlIGJyZWF0aHRha2luZyBiZWF1dHkgb2YgSGltYWNoYWwgUHJhZGVzaCB3aXRoIHNub3ctY2FwcGVkIHBlYWtzIGFuZCBhZHZlbnR1cmUgYWN0aXZpdGllcy4iLCJkZXN0aW5hdGlvbiI6Ik1hbmFsaSwgSGltYWNoYWwgUHJhZGVzaCIsImR1cmF0aW9uX2RheXMiOjYsInByaWNlX3Blcl9wZXJzb24iOjE1MDAwLjAwLCJkaWZmaWN1bHR5IjoibW9kZXJhdGUiLCJmZWF0dXJlZF9pbWFnZV91cmwiOiJodHRwczovL3Bvc2l0aXZlNy5pbi93cC1jb250ZW50L3VwbG9hZHMvMjAyMi8wNy9NYW5hbGktMi5qcGcifSwgCiB7ImlkIjoiYjQyMmUxMWEtMzAxNi00NTU3LWIzYzMtYzVkMDhiN2FkMzkyIiwidGl0bGUiOiJSaXNoaWtlc2ggU3Bpcml0dWFsIEpvdXJuZXkiLCJzbHVnIjoicmlzaGlrZXNoLXNwaXJpdHVhbC1qb3VybmV5IiwiZGVzY3JpcHRpb24iOiJEaXNjb3ZlciBpbm5lciBwZWFjZSBpbiB0aGUgWW9nYSBDYXBpdGFsIG9mIHRoZSBXb3JsZCB3aXRoIG1lZGl0YXRpb24gYW5kIGFkdmVudHVyZS4iLCJkZXN0aW5hdGlvbiI6IlJpc2hpa2VzaCwgVXR0YXJha2hhbmQiLCJkdXJhdGlvbl9kYXlzIjo1LCJwcmljZV9wZXJfcGVyc29uIjoxMjAwMC4wMCwiZGlmZmljdWx0eSI6ImVhc3kiLCJmZWF0dXJlZF9pbWFnZV91cmwiOiJodHRwczovL3Bvc2l0aXZlNy5pbi93cC1jb250ZW50L3VwbG9hZHMvMjAyMi8wOS9kdXNrLXRpbWUtcmlzaGlrZXNoLWhvbHktdG93bi10cmF2ZWwtZGVzdGluYXRpb24taW5kaWEtMTAyNHg2ODQuanBnIn0sIAogeyJpZCI6IjM0YWNlNTJmLTc5M2MtNDMyNS05ZTAwLWVmY2FiNzBjZTVlYSIsInRpdGxlIjoiUmlzaGlrZXNoIEFkdmVudHVyZSBUb3VyIiwic2x1ZyI6InJpc2hpa2VzaC1hZHZlbnR1cmUtdG91ciIsImRlc2NyaXB0aW9uIjoiUmlzaGlrZXNoLCBuZXN0bGVkIGluIHRoZSBmb290aGlsbHMgb2YgdGhlIEhpbWFsYXlhcyBhbG9uZyB0aGUgYmFua3Mgb2YgdGhlIEdhbmdlcyBSaXZlciwgaXMgYSBjYXB0aXZhdGluZyBkZXN0aW5hdGlvbiBrbm93biBhcyB0aGUgXCJZb2dhIENhcGl0YWwgb2YgdGhlIFdvcmxkXCIuIEV4cGVyaWVuY2Ugcml2ZXIgcmFmdGluZywgYnVuZ2VlIGp1bXBpbmcsIGFuZCBzcGlyaXR1YWwgYWN0aXZpdGllcy4iLCJkZXN0aW5hdGlvbiI6IlJpc2hpa2VzaCwgVXR0YXJha2hhbmQiLCJkdXJhdGlvbl9kYXlzIjo3LCJwcmljZV9wZXJfcGVyc29uIjoyMjUwMC4wMCwiZGlmZmljdWx0eSI6Im1vZGVyYXRlIiwiZmVhdHVyZWRfaW1hZ2VfdXJsIjoiaHR0cHM6Ly9wb3NpdGl2ZTcuaW4vd3AtY29udGVudC91cGxvYWRzLzIwMjIvMDkvZHVzay10aW1lLXJpc2hpa2VzaC1ob2x5LXRvd24tdHJhdmVsLWRlc3RpbmF0aW9uLWluZGlhLTEwMjR4Njg0LmpwZyJ9XQ==", "status": 200, "url": "https://soaoagcuubtzojytoati.supabase.co/rest/v1/trips?select=id%2Ctitle%2Cslug%2Cdescription%2Cdestination%2Cduration_days%2Cprice_per_person%2Cdifficulty%2Cfeatured_image_url&is_active=eq.true&id=neq.58eb8753-9dd5-491a-bd62-e55aed3c3da3&limit=3"}, "revalidate": ********, "tags": []}