// Simple test to check authentication status
const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function testAuth() {
  console.log('Testing authentication...');
  
  try {
    // Test getUser() method
    const { data: { user }, error } = await supabase.auth.getUser();
    
    console.log('getUser() result:');
    console.log('- User:', user ? `${user.email} (${user.id})` : 'null');
    console.log('- Error:', error);
    
    // Test getSession() method
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    
    console.log('\ngetSession() result:');
    console.log('- Session:', session ? `${session.user.email} (${session.user.id})` : 'null');
    console.log('- Error:', sessionError);
    
    if (user) {
      // Test database access
      const { data: userProfile, error: profileError } = await supabase
        .from('users')
        .select('*')
        .eq('id', user.id)
        .single();
      
      console.log('\nUser profile from database:');
      console.log('- Profile:', userProfile ? `${userProfile.full_name} (${userProfile.role})` : 'null');
      console.log('- Error:', profileError);
    }
    
  } catch (error) {
    console.error('Test failed:', error);
  }
}

testAuth();
