'use client';

import Image from 'next/image'
import Link from 'next/link'
import { motion } from 'framer-motion'
import {
  Users,
  Award,
  MapPin,
  Calendar,
  Heart,
  Star,
  Target,
  Eye,
  Lightbulb,
  Shield,
  Globe,
  BookOpen
} from 'lucide-react'
import <PERSON><PERSON> from '@/components/ui/Button'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'
import { TeamSection } from '@/components/about/TeamSection'
import { TimelineSection } from '@/components/about/TimelineSection'
import { ValuesSection } from '@/components/about/ValuesSection'
import { StatsSection } from '@/components/about/StatsSection'

export default function AboutPage() {
  return (
    <>
      <Header />
      <main className="flex-1">
        <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50">
      {/* Hero Section */}
      <section className="relative py-20 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-blue-600/10 to-green-600/10" />
        <div className="max-w-7xl mx-auto px-4 relative">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div>
              <h1 className="text-5xl font-bold text-gray-900 mb-6">
                Transforming Education Through
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-green-600"> Travel</span>
              </h1>
              <p className="text-xl text-gray-700 mb-8 leading-relaxed">
                Since 2009, Positive7 has been pioneering experiential learning through carefully crafted educational tours.
                We believe that the world is the greatest classroom, and every journey is an opportunity to learn, grow, and discover.
              </p>
              <div className="flex flex-wrap gap-4">
                <Link href="/contact">
                  <Button size="lg" className="bg-gradient-to-r from-blue-600 to-green-600">
                    Contact Us
                  </Button>
                </Link>
                <Link href="/trips">
                  <Button variant="outline" size="lg">
                    View Our Trips
                  </Button>
                </Link>
              </div>
            </div>
            <div className="relative">
              <div className="relative h-96 rounded-2xl overflow-hidden shadow-2xl">
                <Image
                  src="https://positive7.in/wp-content/uploads/2022/07/Manali-2.jpg"
                  alt="Students on educational tour"
                  fill
                  className="object-cover"
                />
              </div>
              {/* Floating Stats */}
              <div className="absolute -bottom-6 -left-6 bg-white rounded-xl p-6 shadow-lg">
                <div className="text-3xl font-bold text-blue-600">15+</div>
                <div className="text-sm text-gray-600">Years of Excellence</div>
              </div>
              <div className="absolute -top-6 -right-6 bg-white rounded-xl p-6 shadow-lg">
                <div className="text-3xl font-bold text-green-600">50,000+</div>
                <div className="text-sm text-gray-600">Students Impacted</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Mission & Vision */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">Our Purpose</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              We exist to create transformative learning experiences that go beyond textbooks and classrooms
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-12">
            {/* Mission */}
            <div className="bg-white rounded-2xl p-8 shadow-lg">
              <div className="w-16 h-16 bg-gradient-to-r from-blue-600 to-blue-700 rounded-full flex items-center justify-center mb-6">
                <Target className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4">Our Mission</h3>
              <p className="text-gray-700 leading-relaxed">
                To provide safe, educational, and transformative travel experiences that foster personal growth,
                cultural understanding, and lifelong learning. We are committed to creating memories that last a
                lifetime while ensuring the highest standards of safety and educational value.
              </p>
            </div>

            {/* Vision */}
            <div className="bg-white rounded-2xl p-8 shadow-lg">
              <div className="w-16 h-16 bg-gradient-to-r from-green-600 to-green-700 rounded-full flex items-center justify-center mb-6">
                <Eye className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4">Our Vision</h3>
              <p className="text-gray-700 leading-relaxed">
                To be India's most trusted educational tour company, recognized for our innovative approach to
                experiential learning. We envision a world where every student has access to transformative
                travel experiences that shape them into confident, culturally aware global citizens.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Values Section */}
      <ValuesSection />

      {/* Stats Section */}
      <StatsSection />

      {/* Our Story Timeline */}
      <TimelineSection />

      {/* Team Section */}
      <TeamSection />

      {/* Why Choose Us */}
      <section className="py-20 bg-gradient-to-r from-blue-600 to-green-600">
        <div className="max-w-7xl mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-white mb-4">Why Choose Positive7?</h2>
            <p className="text-xl text-blue-100 max-w-3xl mx-auto">
              We're not just a tour company - we're your partners in creating life-changing educational experiences
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {[
              {
                icon: Shield,
                title: 'Safety First',
                description: 'Comprehensive safety protocols, trained guides, and 24/7 support ensure peace of mind for parents and educators.'
              },
              {
                icon: BookOpen,
                title: 'Educational Excellence',
                description: 'Every trip is designed with clear learning objectives, pre and post-trip activities, and curriculum alignment.'
              },
              {
                icon: Heart,
                title: 'Personal Growth',
                description: 'We focus on building confidence, independence, and life skills through carefully designed challenges and experiences.'
              },
              {
                icon: Globe,
                title: 'Cultural Immersion',
                description: 'Authentic local experiences that foster cultural understanding and global citizenship.'
              },
              {
                icon: Users,
                title: 'Expert Team',
                description: 'Experienced educators and travel professionals who understand both learning and adventure.'
              },
              {
                icon: Award,
                title: 'Proven Track Record',
                description: '15+ years of excellence with thousands of successful trips and satisfied students and schools.'
              }
            ].map((feature, index) => (
              <div key={index} className="text-center">
                <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-6">
                  <feature.icon className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-xl font-bold text-white mb-4">{feature.title}</h3>
                <p className="text-blue-100 leading-relaxed">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-20">
        <div className="max-w-4xl mx-auto px-4 text-center">
          <h2 className="text-4xl font-bold text-gray-900 mb-6">
            Ready to Create Unforgettable Learning Experiences?
          </h2>
          <p className="text-xl text-gray-600 mb-8">
            Join thousands of students who have transformed their learning through our educational tours
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/contact">
              <Button size="lg" className="bg-gradient-to-r from-blue-600 to-green-600">
                Plan Your Trip
              </Button>
            </Link>
            <Link href="/trips">
              <Button variant="outline" size="lg">
                Browse Destinations
              </Button>
            </Link>
          </div>
        </div>
      </section>
        </div>
      </main>
      <Footer />
    </>
  )
}
