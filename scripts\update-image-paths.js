const fs = require('fs');
const path = require('path');

// Load the image mapping
const mapping = JSON.parse(fs.readFileSync('scripts/image-mapping.json', 'utf8'));

// Convert Windows paths to Unix paths and remove 'public' prefix
const normalizedMapping = {};
Object.keys(mapping).forEach(url => {
  const localPath = mapping[url].replace(/\\/g, '/').replace('public/', '/');
  normalizedMapping[url] = localPath;
});

// Function to update a file with new image paths
function updateFile(filePath, replacements) {
  if (!fs.existsSync(filePath)) {
    console.log(`⚠️  File not found: ${filePath}`);
    return;
  }
  
  let content = fs.readFileSync(filePath, 'utf8');
  let updated = false;
  
  replacements.forEach(({ from, to }) => {
    if (content.includes(from)) {
      content = content.replace(new RegExp(from.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), to);
      updated = true;
    }
  });
  
  if (updated) {
    fs.writeFileSync(filePath, content);
    console.log(`✅ Updated: ${filePath}`);
  } else {
    console.log(`⏭️  No changes needed: ${filePath}`);
  }
}

// Files to update with their specific replacements
const filesToUpdate = [
  {
    path: 'lib/constants.ts',
    replacements: Object.keys(normalizedMapping).map(url => ({
      from: url,
      to: normalizedMapping[url]
    }))
  },
  {
    path: 'app/gallery/page.tsx',
    replacements: Object.keys(normalizedMapping).map(url => ({
      from: url,
      to: normalizedMapping[url]
    }))
  },
  {
    path: 'app/trips-photos/page.tsx',
    replacements: Object.keys(normalizedMapping).map(url => ({
      from: url,
      to: normalizedMapping[url]
    }))
  },
  {
    path: 'components/sections/HeroSection.tsx',
    replacements: Object.keys(normalizedMapping).map(url => ({
      from: url,
      to: normalizedMapping[url]
    }))
  }
];

// Update all files
console.log('🔄 Updating image paths in project files...\n');

filesToUpdate.forEach(({ path: filePath, replacements }) => {
  updateFile(filePath, replacements);
});

console.log('\n📊 Update Summary:');
console.log(`🔗 Total image mappings: ${Object.keys(normalizedMapping).length}`);
console.log('✅ All files updated with local image paths');
console.log('🎯 External images now have local fallbacks');

// Create a utility function for the project
const utilityCode = `
// Auto-generated image utility - DO NOT EDIT MANUALLY
// This file maps external image URLs to local fallbacks

export const IMAGE_FALLBACKS: Record<string, string> = ${JSON.stringify(normalizedMapping, null, 2)};

export function getImageWithFallback(url: string): string {
  // If it's already a local path, return as is
  if (url.startsWith('/')) return url;
  
  // Check if we have a local fallback for this external URL
  if (IMAGE_FALLBACKS[url]) {
    return IMAGE_FALLBACKS[url];
  }
  
  // Return original URL if no fallback available
  return url;
}
`;

fs.writeFileSync('lib/image-fallbacks.ts', utilityCode);
console.log('📄 Created utility file: lib/image-fallbacks.ts');

console.log('\n🎉 Image localization complete!');
console.log('📝 Next steps:');
console.log('   1. Import getImageWithFallback in components that use external images');
console.log('   2. Wrap image URLs with getImageWithFallback(url)');
console.log('   3. External images will automatically fallback to local copies');
