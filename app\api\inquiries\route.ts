import { NextRequest, NextResponse } from 'next/server';
import type { CreateInquiryData } from '@/types/database';

// GET /api/inquiries - Get inquiries (Admin only)
export async function GET(request: NextRequest) {
  try {
    // Authentication removed - return empty array for now
    return NextResponse.json([]);
  } catch (error) {
    console.error('Error in GET /api/inquiries:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST /api/inquiries - Create a new inquiry
export async function POST(request: NextRequest) {
  try {
    const body: CreateInquiryData = await request.json();

    // Basic validation
    const requiredFields = ['name', 'email', 'message'];
    for (const field of requiredFields) {
      if (!body[field as keyof CreateInquiryData]) {
        return NextResponse.json(
          { error: `Missing required field: ${field}` },
          { status: 400 }
        );
      }
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(body.email)) {
      return NextResponse.json(
        { error: 'Invalid email format' },
        { status: 400 }
      );
    }

    // For now, just return success without saving to database
    return NextResponse.json({
      message: 'Inquiry submitted successfully. We will get back to you soon!',
      data: { id: 'temp-' + Date.now(), ...body, status: 'new' }
    }, { status: 201 });
  } catch (error) {
    console.error('Error in POST /api/inquiries:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
