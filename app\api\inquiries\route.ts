import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabase } from '@/lib/supabase-server';
import type { CreateInquiryData } from '@/types/database';

// GET /api/inquiries - Get inquiries (Admin only)
export async function GET(request: NextRequest) {
  try {
    const supabase = createServerSupabase();
    const { searchParams } = new URL(request.url);

    // Parse query parameters
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const status = searchParams.get('status');
    const inquiryType = searchParams.get('inquiryType');

    // Build query
    let query = supabase
      .from('inquiries')
      .select(`
        id,
        name,
        email,
        phone,
        subject,
        message,
        inquiry_type,
        status,
        created_at
      `, { count: 'exact' });

    // Apply filters
    if (status && status !== 'all') {
      query = query.eq('status', status);
    }
    if (inquiryType && inquiryType !== 'all') {
      query = query.eq('inquiry_type', inquiryType);
    }

    // Apply pagination
    const from = (page - 1) * limit;
    const to = from + limit - 1;

    query = query
      .order('created_at', { ascending: false })
      .range(from, to);

    const { data: inquiries, error, count } = await query;

    if (error) {
      console.error('Error fetching inquiries:', error);
      return NextResponse.json(
        { error: 'Failed to fetch inquiries' },
        { status: 500 }
      );
    }

    const totalPages = Math.ceil((count || 0) / limit);

    return NextResponse.json({
      data: inquiries || [],
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages,
      },
    });
  } catch (error) {
    console.error('Error in GET /api/inquiries:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST /api/inquiries - Create a new inquiry
export async function POST(request: NextRequest) {
  try {
    const body: CreateInquiryData = await request.json();

    // Basic validation
    const requiredFields = ['name', 'email', 'message'];
    for (const field of requiredFields) {
      if (!body[field as keyof CreateInquiryData]) {
        return NextResponse.json(
          { error: `Missing required field: ${field}` },
          { status: 400 }
        );
      }
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(body.email)) {
      return NextResponse.json(
        { error: 'Invalid email format' },
        { status: 400 }
      );
    }

    const supabase = createServerSupabase();

    // Save inquiry to database
    const { data: inquiry, error: dbError } = await supabase
      .from('inquiries')
      .insert({
        name: body.name,
        email: body.email,
        phone: body.phone || null,
        subject: body.subject || null,
        message: body.message,
        inquiry_type: body.inquiry_type || 'General Inquiry',
        status: 'new'
      })
      .select()
      .single();

    if (dbError) {
      console.error('Error saving inquiry to database:', dbError);
      return NextResponse.json(
        { error: 'Failed to save inquiry' },
        { status: 500 }
      );
    }

    // Send email notification (if email settings are configured)
    try {
      await sendInquiryEmail(inquiry);
    } catch (emailError) {
      console.error('Error sending email:', emailError);
      // Don't fail the request if email fails, just log it
    }

    return NextResponse.json({
      message: 'Inquiry submitted successfully. We will get back to you soon!',
      data: inquiry
    }, { status: 201 });
  } catch (error) {
    console.error('Error in POST /api/inquiries:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Helper function to send inquiry email
async function sendInquiryEmail(inquiry: any) {
  const adminEmail = process.env.ADMIN_EMAIL;

  if (!adminEmail) {
    console.log('ADMIN_EMAIL not configured, skipping email notification');
    return;
  }

  // Email content
  const emailContent = `
    New Inquiry Received - Positive7 Educational Tours

    Name: ${inquiry.name}
    Email: ${inquiry.email}
    Phone: ${inquiry.phone || 'Not provided'}
    Subject: ${inquiry.subject || 'No subject'}
    Inquiry Type: ${inquiry.inquiry_type}

    Message:
    ${inquiry.message}

    Submitted at: ${new Date(inquiry.created_at).toLocaleString()}
    Inquiry ID: ${inquiry.id}
  `;

  // Here you would integrate with your email service (SendGrid, Nodemailer, etc.)
  // For now, we'll just log the email content
  console.log('Email notification would be sent to:', adminEmail);
  console.log('Email content:', emailContent);

  // TODO: Implement actual email sending
  // Example with Nodemailer or SendGrid would go here
}
