import { Metadata } from 'next'
import { Suspense } from 'react'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'
import TripManagement from '@/components/admin/TripManagement'
import LoadingSpinner from '@/components/ui/LoadingSpinner'

export const metadata: Metadata = {
  title: 'Trip Management - Admin Dashboard | Positive7 Educational Tours',
  description: 'Create, edit, and manage educational trips and tour packages for Positive7 Educational Tours.',
  keywords: 'admin, trip management, educational tours, trip creation, Positive7'
}

export default function AdminTripsPage() {
  return (
    <>
      <Header />
      <main className="min-h-screen bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <Suspense fallback={
            <div className="flex items-center justify-center min-h-screen">
              <LoadingSpinner size="lg" />
            </div>
          }>
            <TripManagement />
          </Suspense>
        </div>
      </main>
      <Footer />
    </>
  )
}
