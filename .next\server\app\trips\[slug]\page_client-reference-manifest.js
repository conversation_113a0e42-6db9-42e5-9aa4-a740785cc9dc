globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/trips/[slug]/page"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/layout/Footer.tsx":{"*":{"id":"(ssr)/./components/layout/Footer.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/layout/Header.tsx":{"*":{"id":"(ssr)/./components/layout/Header.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/sections/AboutSection.tsx":{"*":{"id":"(ssr)/./components/sections/AboutSection.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/sections/ContactSection.tsx":{"*":{"id":"(ssr)/./components/sections/ContactSection.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/sections/FeaturedTripsSection.tsx":{"*":{"id":"(ssr)/./components/sections/FeaturedTripsSection.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/sections/HeroSection.tsx":{"*":{"id":"(ssr)/./components/sections/HeroSection.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/sections/TestimonialsSection.tsx":{"*":{"id":"(ssr)/./components/sections/TestimonialsSection.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/performance/PerformanceOptimization.tsx":{"*":{"id":"(ssr)/./components/performance/PerformanceOptimization.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/security/SecurityAccessibility.tsx":{"*":{"id":"(ssr)/./components/security/SecurityAccessibility.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/script.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/script.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/trips/TripsClient.tsx":{"*":{"id":"(ssr)/./components/trips/TripsClient.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/trips/TripDetailClient.tsx":{"*":{"id":"(ssr)/./components/trips/TripDetailClient.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"C:\\Users\\<USER>\\Documents\\projects\\p7-comprehensive\\node_modules\\next\\dist\\client\\components\\app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\projects\\p7-comprehensive\\node_modules\\next\\dist\\esm\\client\\components\\app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\projects\\p7-comprehensive\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\projects\\p7-comprehensive\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\projects\\p7-comprehensive\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\projects\\p7-comprehensive\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\projects\\p7-comprehensive\\node_modules\\next\\dist\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\projects\\p7-comprehensive\\node_modules\\next\\dist\\esm\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\projects\\p7-comprehensive\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\projects\\p7-comprehensive\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\projects\\p7-comprehensive\\node_modules\\next\\dist\\client\\components\\static-generation-searchparams-bailout-provider.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\projects\\p7-comprehensive\\node_modules\\next\\dist\\esm\\client\\components\\static-generation-searchparams-bailout-provider.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\projects\\p7-comprehensive\\components\\layout\\Footer.tsx":{"id":"(app-pages-browser)/./components/layout/Footer.tsx","name":"*","chunks":["app/trips/page","static/chunks/app/trips/page.js"],"async":false},"C:\\Users\\<USER>\\Documents\\projects\\p7-comprehensive\\components\\layout\\Header.tsx":{"id":"(app-pages-browser)/./components/layout/Header.tsx","name":"*","chunks":["app/trips/page","static/chunks/app/trips/page.js"],"async":false},"C:\\Users\\<USER>\\Documents\\projects\\p7-comprehensive\\components\\sections\\AboutSection.tsx":{"id":"(app-pages-browser)/./components/sections/AboutSection.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Documents\\projects\\p7-comprehensive\\components\\sections\\ContactSection.tsx":{"id":"(app-pages-browser)/./components/sections/ContactSection.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Documents\\projects\\p7-comprehensive\\components\\sections\\FeaturedTripsSection.tsx":{"id":"(app-pages-browser)/./components/sections/FeaturedTripsSection.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Documents\\projects\\p7-comprehensive\\components\\sections\\HeroSection.tsx":{"id":"(app-pages-browser)/./components/sections/HeroSection.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Documents\\projects\\p7-comprehensive\\components\\sections\\TestimonialsSection.tsx":{"id":"(app-pages-browser)/./components/sections/TestimonialsSection.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Documents\\projects\\p7-comprehensive\\app\\globals.css":{"id":"(app-pages-browser)/./app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Documents\\projects\\p7-comprehensive\\components\\performance\\PerformanceOptimization.tsx":{"id":"(app-pages-browser)/./components/performance/PerformanceOptimization.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Documents\\projects\\p7-comprehensive\\components\\security\\SecurityAccessibility.tsx":{"id":"(app-pages-browser)/./components/security/SecurityAccessibility.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Documents\\projects\\p7-comprehensive\\node_modules\\next\\dist\\client\\script.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/script.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Documents\\projects\\p7-comprehensive\\node_modules\\next\\dist\\esm\\client\\script.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/script.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Documents\\projects\\p7-comprehensive\\node_modules\\next\\font\\google\\target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\",\"display\":\"swap\"}],\"variableName\":\"inter\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\",\"display\":\"swap\"}],\"variableName\":\"inter\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Documents\\projects\\p7-comprehensive\\node_modules\\next\\font\\google\\target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Poppins\",\"arguments\":[{\"subsets\":[\"latin\"],\"weight\":[\"300\",\"400\",\"500\",\"600\",\"700\",\"800\"],\"variable\":\"--font-poppins\",\"display\":\"swap\"}],\"variableName\":\"poppins\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Poppins\",\"arguments\":[{\"subsets\":[\"latin\"],\"weight\":[\"300\",\"400\",\"500\",\"600\",\"700\",\"800\"],\"variable\":\"--font-poppins\",\"display\":\"swap\"}],\"variableName\":\"poppins\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Documents\\projects\\p7-comprehensive\\components\\trips\\TripsClient.tsx":{"id":"(app-pages-browser)/./components/trips/TripsClient.tsx","name":"*","chunks":["app/trips/page","static/chunks/app/trips/page.js"],"async":false},"C:\\Users\\<USER>\\Documents\\projects\\p7-comprehensive\\components\\trips\\TripDetailClient.tsx":{"id":"(app-pages-browser)/./components/trips/TripDetailClient.tsx","name":"*","chunks":["app/trips/[slug]/page","static/chunks/app/trips/%5Bslug%5D/page.js"],"async":false}},"entryCSSFiles":{"C:\\Users\\<USER>\\Documents\\projects\\p7-comprehensive\\app\\page":[],"C:\\Users\\<USER>\\Documents\\projects\\p7-comprehensive\\app\\layout":["static/css/app/layout.css"],"C:\\Users\\<USER>\\Documents\\projects\\p7-comprehensive\\app\\trips\\page":[],"C:\\Users\\<USER>\\Documents\\projects\\p7-comprehensive\\app\\trips\\[slug]\\page":[]}}