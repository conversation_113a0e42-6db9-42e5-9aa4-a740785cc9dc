"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/d3-time-format";
exports.ids = ["vendor-chunks/d3-time-format"];
exports.modules = {

/***/ "(ssr)/./node_modules/d3-time-format/src/defaultLocale.js":
/*!**********************************************************!*\
  !*** ./node_modules/d3-time-format/src/defaultLocale.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ defaultLocale),\n/* harmony export */   timeFormat: () => (/* binding */ timeFormat),\n/* harmony export */   timeParse: () => (/* binding */ timeParse),\n/* harmony export */   utcFormat: () => (/* binding */ utcFormat),\n/* harmony export */   utcParse: () => (/* binding */ utcParse)\n/* harmony export */ });\n/* harmony import */ var _locale_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./locale.js */ \"(ssr)/./node_modules/d3-time-format/src/locale.js\");\n\nvar locale;\nvar timeFormat;\nvar timeParse;\nvar utcFormat;\nvar utcParse;\ndefaultLocale({\n    dateTime: \"%x, %X\",\n    date: \"%-m/%-d/%Y\",\n    time: \"%-I:%M:%S %p\",\n    periods: [\n        \"AM\",\n        \"PM\"\n    ],\n    days: [\n        \"Sunday\",\n        \"Monday\",\n        \"Tuesday\",\n        \"Wednesday\",\n        \"Thursday\",\n        \"Friday\",\n        \"Saturday\"\n    ],\n    shortDays: [\n        \"Sun\",\n        \"Mon\",\n        \"Tue\",\n        \"Wed\",\n        \"Thu\",\n        \"Fri\",\n        \"Sat\"\n    ],\n    months: [\n        \"January\",\n        \"February\",\n        \"March\",\n        \"April\",\n        \"May\",\n        \"June\",\n        \"July\",\n        \"August\",\n        \"September\",\n        \"October\",\n        \"November\",\n        \"December\"\n    ],\n    shortMonths: [\n        \"Jan\",\n        \"Feb\",\n        \"Mar\",\n        \"Apr\",\n        \"May\",\n        \"Jun\",\n        \"Jul\",\n        \"Aug\",\n        \"Sep\",\n        \"Oct\",\n        \"Nov\",\n        \"Dec\"\n    ]\n});\nfunction defaultLocale(definition) {\n    locale = (0,_locale_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(definition);\n    timeFormat = locale.format;\n    timeParse = locale.parse;\n    utcFormat = locale.utcFormat;\n    utcParse = locale.utcParse;\n    return locale;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-time-format/src/defaultLocale.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-time-format/src/locale.js":
/*!***************************************************!*\
  !*** ./node_modules/d3-time-format/src/locale.js ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ formatLocale)\n/* harmony export */ });\n/* harmony import */ var d3_time__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! d3-time */ \"(ssr)/./node_modules/d3-time/src/week.js\");\n/* harmony import */ var d3_time__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! d3-time */ \"(ssr)/./node_modules/d3-time/src/day.js\");\n/* harmony import */ var d3_time__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! d3-time */ \"(ssr)/./node_modules/d3-time/src/year.js\");\n\nfunction localDate(d) {\n    if (0 <= d.y && d.y < 100) {\n        var date = new Date(-1, d.m, d.d, d.H, d.M, d.S, d.L);\n        date.setFullYear(d.y);\n        return date;\n    }\n    return new Date(d.y, d.m, d.d, d.H, d.M, d.S, d.L);\n}\nfunction utcDate(d) {\n    if (0 <= d.y && d.y < 100) {\n        var date = new Date(Date.UTC(-1, d.m, d.d, d.H, d.M, d.S, d.L));\n        date.setUTCFullYear(d.y);\n        return date;\n    }\n    return new Date(Date.UTC(d.y, d.m, d.d, d.H, d.M, d.S, d.L));\n}\nfunction newDate(y, m, d) {\n    return {\n        y: y,\n        m: m,\n        d: d,\n        H: 0,\n        M: 0,\n        S: 0,\n        L: 0\n    };\n}\nfunction formatLocale(locale) {\n    var locale_dateTime = locale.dateTime, locale_date = locale.date, locale_time = locale.time, locale_periods = locale.periods, locale_weekdays = locale.days, locale_shortWeekdays = locale.shortDays, locale_months = locale.months, locale_shortMonths = locale.shortMonths;\n    var periodRe = formatRe(locale_periods), periodLookup = formatLookup(locale_periods), weekdayRe = formatRe(locale_weekdays), weekdayLookup = formatLookup(locale_weekdays), shortWeekdayRe = formatRe(locale_shortWeekdays), shortWeekdayLookup = formatLookup(locale_shortWeekdays), monthRe = formatRe(locale_months), monthLookup = formatLookup(locale_months), shortMonthRe = formatRe(locale_shortMonths), shortMonthLookup = formatLookup(locale_shortMonths);\n    var formats = {\n        \"a\": formatShortWeekday,\n        \"A\": formatWeekday,\n        \"b\": formatShortMonth,\n        \"B\": formatMonth,\n        \"c\": null,\n        \"d\": formatDayOfMonth,\n        \"e\": formatDayOfMonth,\n        \"f\": formatMicroseconds,\n        \"g\": formatYearISO,\n        \"G\": formatFullYearISO,\n        \"H\": formatHour24,\n        \"I\": formatHour12,\n        \"j\": formatDayOfYear,\n        \"L\": formatMilliseconds,\n        \"m\": formatMonthNumber,\n        \"M\": formatMinutes,\n        \"p\": formatPeriod,\n        \"q\": formatQuarter,\n        \"Q\": formatUnixTimestamp,\n        \"s\": formatUnixTimestampSeconds,\n        \"S\": formatSeconds,\n        \"u\": formatWeekdayNumberMonday,\n        \"U\": formatWeekNumberSunday,\n        \"V\": formatWeekNumberISO,\n        \"w\": formatWeekdayNumberSunday,\n        \"W\": formatWeekNumberMonday,\n        \"x\": null,\n        \"X\": null,\n        \"y\": formatYear,\n        \"Y\": formatFullYear,\n        \"Z\": formatZone,\n        \"%\": formatLiteralPercent\n    };\n    var utcFormats = {\n        \"a\": formatUTCShortWeekday,\n        \"A\": formatUTCWeekday,\n        \"b\": formatUTCShortMonth,\n        \"B\": formatUTCMonth,\n        \"c\": null,\n        \"d\": formatUTCDayOfMonth,\n        \"e\": formatUTCDayOfMonth,\n        \"f\": formatUTCMicroseconds,\n        \"g\": formatUTCYearISO,\n        \"G\": formatUTCFullYearISO,\n        \"H\": formatUTCHour24,\n        \"I\": formatUTCHour12,\n        \"j\": formatUTCDayOfYear,\n        \"L\": formatUTCMilliseconds,\n        \"m\": formatUTCMonthNumber,\n        \"M\": formatUTCMinutes,\n        \"p\": formatUTCPeriod,\n        \"q\": formatUTCQuarter,\n        \"Q\": formatUnixTimestamp,\n        \"s\": formatUnixTimestampSeconds,\n        \"S\": formatUTCSeconds,\n        \"u\": formatUTCWeekdayNumberMonday,\n        \"U\": formatUTCWeekNumberSunday,\n        \"V\": formatUTCWeekNumberISO,\n        \"w\": formatUTCWeekdayNumberSunday,\n        \"W\": formatUTCWeekNumberMonday,\n        \"x\": null,\n        \"X\": null,\n        \"y\": formatUTCYear,\n        \"Y\": formatUTCFullYear,\n        \"Z\": formatUTCZone,\n        \"%\": formatLiteralPercent\n    };\n    var parses = {\n        \"a\": parseShortWeekday,\n        \"A\": parseWeekday,\n        \"b\": parseShortMonth,\n        \"B\": parseMonth,\n        \"c\": parseLocaleDateTime,\n        \"d\": parseDayOfMonth,\n        \"e\": parseDayOfMonth,\n        \"f\": parseMicroseconds,\n        \"g\": parseYear,\n        \"G\": parseFullYear,\n        \"H\": parseHour24,\n        \"I\": parseHour24,\n        \"j\": parseDayOfYear,\n        \"L\": parseMilliseconds,\n        \"m\": parseMonthNumber,\n        \"M\": parseMinutes,\n        \"p\": parsePeriod,\n        \"q\": parseQuarter,\n        \"Q\": parseUnixTimestamp,\n        \"s\": parseUnixTimestampSeconds,\n        \"S\": parseSeconds,\n        \"u\": parseWeekdayNumberMonday,\n        \"U\": parseWeekNumberSunday,\n        \"V\": parseWeekNumberISO,\n        \"w\": parseWeekdayNumberSunday,\n        \"W\": parseWeekNumberMonday,\n        \"x\": parseLocaleDate,\n        \"X\": parseLocaleTime,\n        \"y\": parseYear,\n        \"Y\": parseFullYear,\n        \"Z\": parseZone,\n        \"%\": parseLiteralPercent\n    };\n    // These recursive directive definitions must be deferred.\n    formats.x = newFormat(locale_date, formats);\n    formats.X = newFormat(locale_time, formats);\n    formats.c = newFormat(locale_dateTime, formats);\n    utcFormats.x = newFormat(locale_date, utcFormats);\n    utcFormats.X = newFormat(locale_time, utcFormats);\n    utcFormats.c = newFormat(locale_dateTime, utcFormats);\n    function newFormat(specifier, formats) {\n        return function(date) {\n            var string = [], i = -1, j = 0, n = specifier.length, c, pad, format;\n            if (!(date instanceof Date)) date = new Date(+date);\n            while(++i < n){\n                if (specifier.charCodeAt(i) === 37) {\n                    string.push(specifier.slice(j, i));\n                    if ((pad = pads[c = specifier.charAt(++i)]) != null) c = specifier.charAt(++i);\n                    else pad = c === \"e\" ? \" \" : \"0\";\n                    if (format = formats[c]) c = format(date, pad);\n                    string.push(c);\n                    j = i + 1;\n                }\n            }\n            string.push(specifier.slice(j, i));\n            return string.join(\"\");\n        };\n    }\n    function newParse(specifier, Z) {\n        return function(string) {\n            var d = newDate(1900, undefined, 1), i = parseSpecifier(d, specifier, string += \"\", 0), week, day;\n            if (i != string.length) return null;\n            // If a UNIX timestamp is specified, return it.\n            if (\"Q\" in d) return new Date(d.Q);\n            if (\"s\" in d) return new Date(d.s * 1000 + (\"L\" in d ? d.L : 0));\n            // If this is utcParse, never use the local timezone.\n            if (Z && !(\"Z\" in d)) d.Z = 0;\n            // The am-pm flag is 0 for AM, and 1 for PM.\n            if (\"p\" in d) d.H = d.H % 12 + d.p * 12;\n            // If the month was not specified, inherit from the quarter.\n            if (d.m === undefined) d.m = \"q\" in d ? d.q : 0;\n            // Convert day-of-week and week-of-year to day-of-year.\n            if (\"V\" in d) {\n                if (d.V < 1 || d.V > 53) return null;\n                if (!(\"w\" in d)) d.w = 1;\n                if (\"Z\" in d) {\n                    week = utcDate(newDate(d.y, 0, 1)), day = week.getUTCDay();\n                    week = day > 4 || day === 0 ? d3_time__WEBPACK_IMPORTED_MODULE_0__.utcMonday.ceil(week) : (0,d3_time__WEBPACK_IMPORTED_MODULE_0__.utcMonday)(week);\n                    week = d3_time__WEBPACK_IMPORTED_MODULE_1__.utcDay.offset(week, (d.V - 1) * 7);\n                    d.y = week.getUTCFullYear();\n                    d.m = week.getUTCMonth();\n                    d.d = week.getUTCDate() + (d.w + 6) % 7;\n                } else {\n                    week = localDate(newDate(d.y, 0, 1)), day = week.getDay();\n                    week = day > 4 || day === 0 ? d3_time__WEBPACK_IMPORTED_MODULE_0__.timeMonday.ceil(week) : (0,d3_time__WEBPACK_IMPORTED_MODULE_0__.timeMonday)(week);\n                    week = d3_time__WEBPACK_IMPORTED_MODULE_1__.timeDay.offset(week, (d.V - 1) * 7);\n                    d.y = week.getFullYear();\n                    d.m = week.getMonth();\n                    d.d = week.getDate() + (d.w + 6) % 7;\n                }\n            } else if (\"W\" in d || \"U\" in d) {\n                if (!(\"w\" in d)) d.w = \"u\" in d ? d.u % 7 : \"W\" in d ? 1 : 0;\n                day = \"Z\" in d ? utcDate(newDate(d.y, 0, 1)).getUTCDay() : localDate(newDate(d.y, 0, 1)).getDay();\n                d.m = 0;\n                d.d = \"W\" in d ? (d.w + 6) % 7 + d.W * 7 - (day + 5) % 7 : d.w + d.U * 7 - (day + 6) % 7;\n            }\n            // If a time zone is specified, all fields are interpreted as UTC and then\n            // offset according to the specified time zone.\n            if (\"Z\" in d) {\n                d.H += d.Z / 100 | 0;\n                d.M += d.Z % 100;\n                return utcDate(d);\n            }\n            // Otherwise, all fields are in local time.\n            return localDate(d);\n        };\n    }\n    function parseSpecifier(d, specifier, string, j) {\n        var i = 0, n = specifier.length, m = string.length, c, parse;\n        while(i < n){\n            if (j >= m) return -1;\n            c = specifier.charCodeAt(i++);\n            if (c === 37) {\n                c = specifier.charAt(i++);\n                parse = parses[c in pads ? specifier.charAt(i++) : c];\n                if (!parse || (j = parse(d, string, j)) < 0) return -1;\n            } else if (c != string.charCodeAt(j++)) {\n                return -1;\n            }\n        }\n        return j;\n    }\n    function parsePeriod(d, string, i) {\n        var n = periodRe.exec(string.slice(i));\n        return n ? (d.p = periodLookup.get(n[0].toLowerCase()), i + n[0].length) : -1;\n    }\n    function parseShortWeekday(d, string, i) {\n        var n = shortWeekdayRe.exec(string.slice(i));\n        return n ? (d.w = shortWeekdayLookup.get(n[0].toLowerCase()), i + n[0].length) : -1;\n    }\n    function parseWeekday(d, string, i) {\n        var n = weekdayRe.exec(string.slice(i));\n        return n ? (d.w = weekdayLookup.get(n[0].toLowerCase()), i + n[0].length) : -1;\n    }\n    function parseShortMonth(d, string, i) {\n        var n = shortMonthRe.exec(string.slice(i));\n        return n ? (d.m = shortMonthLookup.get(n[0].toLowerCase()), i + n[0].length) : -1;\n    }\n    function parseMonth(d, string, i) {\n        var n = monthRe.exec(string.slice(i));\n        return n ? (d.m = monthLookup.get(n[0].toLowerCase()), i + n[0].length) : -1;\n    }\n    function parseLocaleDateTime(d, string, i) {\n        return parseSpecifier(d, locale_dateTime, string, i);\n    }\n    function parseLocaleDate(d, string, i) {\n        return parseSpecifier(d, locale_date, string, i);\n    }\n    function parseLocaleTime(d, string, i) {\n        return parseSpecifier(d, locale_time, string, i);\n    }\n    function formatShortWeekday(d) {\n        return locale_shortWeekdays[d.getDay()];\n    }\n    function formatWeekday(d) {\n        return locale_weekdays[d.getDay()];\n    }\n    function formatShortMonth(d) {\n        return locale_shortMonths[d.getMonth()];\n    }\n    function formatMonth(d) {\n        return locale_months[d.getMonth()];\n    }\n    function formatPeriod(d) {\n        return locale_periods[+(d.getHours() >= 12)];\n    }\n    function formatQuarter(d) {\n        return 1 + ~~(d.getMonth() / 3);\n    }\n    function formatUTCShortWeekday(d) {\n        return locale_shortWeekdays[d.getUTCDay()];\n    }\n    function formatUTCWeekday(d) {\n        return locale_weekdays[d.getUTCDay()];\n    }\n    function formatUTCShortMonth(d) {\n        return locale_shortMonths[d.getUTCMonth()];\n    }\n    function formatUTCMonth(d) {\n        return locale_months[d.getUTCMonth()];\n    }\n    function formatUTCPeriod(d) {\n        return locale_periods[+(d.getUTCHours() >= 12)];\n    }\n    function formatUTCQuarter(d) {\n        return 1 + ~~(d.getUTCMonth() / 3);\n    }\n    return {\n        format: function(specifier) {\n            var f = newFormat(specifier += \"\", formats);\n            f.toString = function() {\n                return specifier;\n            };\n            return f;\n        },\n        parse: function(specifier) {\n            var p = newParse(specifier += \"\", false);\n            p.toString = function() {\n                return specifier;\n            };\n            return p;\n        },\n        utcFormat: function(specifier) {\n            var f = newFormat(specifier += \"\", utcFormats);\n            f.toString = function() {\n                return specifier;\n            };\n            return f;\n        },\n        utcParse: function(specifier) {\n            var p = newParse(specifier += \"\", true);\n            p.toString = function() {\n                return specifier;\n            };\n            return p;\n        }\n    };\n}\nvar pads = {\n    \"-\": \"\",\n    \"_\": \" \",\n    \"0\": \"0\"\n}, numberRe = /^\\s*\\d+/, percentRe = /^%/, requoteRe = /[\\\\^$*+?|[\\]().{}]/g;\nfunction pad(value, fill, width) {\n    var sign = value < 0 ? \"-\" : \"\", string = (sign ? -value : value) + \"\", length = string.length;\n    return sign + (length < width ? new Array(width - length + 1).join(fill) + string : string);\n}\nfunction requote(s) {\n    return s.replace(requoteRe, \"\\\\$&\");\n}\nfunction formatRe(names) {\n    return new RegExp(\"^(?:\" + names.map(requote).join(\"|\") + \")\", \"i\");\n}\nfunction formatLookup(names) {\n    return new Map(names.map((name, i)=>[\n            name.toLowerCase(),\n            i\n        ]));\n}\nfunction parseWeekdayNumberSunday(d, string, i) {\n    var n = numberRe.exec(string.slice(i, i + 1));\n    return n ? (d.w = +n[0], i + n[0].length) : -1;\n}\nfunction parseWeekdayNumberMonday(d, string, i) {\n    var n = numberRe.exec(string.slice(i, i + 1));\n    return n ? (d.u = +n[0], i + n[0].length) : -1;\n}\nfunction parseWeekNumberSunday(d, string, i) {\n    var n = numberRe.exec(string.slice(i, i + 2));\n    return n ? (d.U = +n[0], i + n[0].length) : -1;\n}\nfunction parseWeekNumberISO(d, string, i) {\n    var n = numberRe.exec(string.slice(i, i + 2));\n    return n ? (d.V = +n[0], i + n[0].length) : -1;\n}\nfunction parseWeekNumberMonday(d, string, i) {\n    var n = numberRe.exec(string.slice(i, i + 2));\n    return n ? (d.W = +n[0], i + n[0].length) : -1;\n}\nfunction parseFullYear(d, string, i) {\n    var n = numberRe.exec(string.slice(i, i + 4));\n    return n ? (d.y = +n[0], i + n[0].length) : -1;\n}\nfunction parseYear(d, string, i) {\n    var n = numberRe.exec(string.slice(i, i + 2));\n    return n ? (d.y = +n[0] + (+n[0] > 68 ? 1900 : 2000), i + n[0].length) : -1;\n}\nfunction parseZone(d, string, i) {\n    var n = /^(Z)|([+-]\\d\\d)(?::?(\\d\\d))?/.exec(string.slice(i, i + 6));\n    return n ? (d.Z = n[1] ? 0 : -(n[2] + (n[3] || \"00\")), i + n[0].length) : -1;\n}\nfunction parseQuarter(d, string, i) {\n    var n = numberRe.exec(string.slice(i, i + 1));\n    return n ? (d.q = n[0] * 3 - 3, i + n[0].length) : -1;\n}\nfunction parseMonthNumber(d, string, i) {\n    var n = numberRe.exec(string.slice(i, i + 2));\n    return n ? (d.m = n[0] - 1, i + n[0].length) : -1;\n}\nfunction parseDayOfMonth(d, string, i) {\n    var n = numberRe.exec(string.slice(i, i + 2));\n    return n ? (d.d = +n[0], i + n[0].length) : -1;\n}\nfunction parseDayOfYear(d, string, i) {\n    var n = numberRe.exec(string.slice(i, i + 3));\n    return n ? (d.m = 0, d.d = +n[0], i + n[0].length) : -1;\n}\nfunction parseHour24(d, string, i) {\n    var n = numberRe.exec(string.slice(i, i + 2));\n    return n ? (d.H = +n[0], i + n[0].length) : -1;\n}\nfunction parseMinutes(d, string, i) {\n    var n = numberRe.exec(string.slice(i, i + 2));\n    return n ? (d.M = +n[0], i + n[0].length) : -1;\n}\nfunction parseSeconds(d, string, i) {\n    var n = numberRe.exec(string.slice(i, i + 2));\n    return n ? (d.S = +n[0], i + n[0].length) : -1;\n}\nfunction parseMilliseconds(d, string, i) {\n    var n = numberRe.exec(string.slice(i, i + 3));\n    return n ? (d.L = +n[0], i + n[0].length) : -1;\n}\nfunction parseMicroseconds(d, string, i) {\n    var n = numberRe.exec(string.slice(i, i + 6));\n    return n ? (d.L = Math.floor(n[0] / 1000), i + n[0].length) : -1;\n}\nfunction parseLiteralPercent(d, string, i) {\n    var n = percentRe.exec(string.slice(i, i + 1));\n    return n ? i + n[0].length : -1;\n}\nfunction parseUnixTimestamp(d, string, i) {\n    var n = numberRe.exec(string.slice(i));\n    return n ? (d.Q = +n[0], i + n[0].length) : -1;\n}\nfunction parseUnixTimestampSeconds(d, string, i) {\n    var n = numberRe.exec(string.slice(i));\n    return n ? (d.s = +n[0], i + n[0].length) : -1;\n}\nfunction formatDayOfMonth(d, p) {\n    return pad(d.getDate(), p, 2);\n}\nfunction formatHour24(d, p) {\n    return pad(d.getHours(), p, 2);\n}\nfunction formatHour12(d, p) {\n    return pad(d.getHours() % 12 || 12, p, 2);\n}\nfunction formatDayOfYear(d, p) {\n    return pad(1 + d3_time__WEBPACK_IMPORTED_MODULE_1__.timeDay.count((0,d3_time__WEBPACK_IMPORTED_MODULE_2__.timeYear)(d), d), p, 3);\n}\nfunction formatMilliseconds(d, p) {\n    return pad(d.getMilliseconds(), p, 3);\n}\nfunction formatMicroseconds(d, p) {\n    return formatMilliseconds(d, p) + \"000\";\n}\nfunction formatMonthNumber(d, p) {\n    return pad(d.getMonth() + 1, p, 2);\n}\nfunction formatMinutes(d, p) {\n    return pad(d.getMinutes(), p, 2);\n}\nfunction formatSeconds(d, p) {\n    return pad(d.getSeconds(), p, 2);\n}\nfunction formatWeekdayNumberMonday(d) {\n    var day = d.getDay();\n    return day === 0 ? 7 : day;\n}\nfunction formatWeekNumberSunday(d, p) {\n    return pad(d3_time__WEBPACK_IMPORTED_MODULE_0__.timeSunday.count((0,d3_time__WEBPACK_IMPORTED_MODULE_2__.timeYear)(d) - 1, d), p, 2);\n}\nfunction dISO(d) {\n    var day = d.getDay();\n    return day >= 4 || day === 0 ? (0,d3_time__WEBPACK_IMPORTED_MODULE_0__.timeThursday)(d) : d3_time__WEBPACK_IMPORTED_MODULE_0__.timeThursday.ceil(d);\n}\nfunction formatWeekNumberISO(d, p) {\n    d = dISO(d);\n    return pad(d3_time__WEBPACK_IMPORTED_MODULE_0__.timeThursday.count((0,d3_time__WEBPACK_IMPORTED_MODULE_2__.timeYear)(d), d) + ((0,d3_time__WEBPACK_IMPORTED_MODULE_2__.timeYear)(d).getDay() === 4), p, 2);\n}\nfunction formatWeekdayNumberSunday(d) {\n    return d.getDay();\n}\nfunction formatWeekNumberMonday(d, p) {\n    return pad(d3_time__WEBPACK_IMPORTED_MODULE_0__.timeMonday.count((0,d3_time__WEBPACK_IMPORTED_MODULE_2__.timeYear)(d) - 1, d), p, 2);\n}\nfunction formatYear(d, p) {\n    return pad(d.getFullYear() % 100, p, 2);\n}\nfunction formatYearISO(d, p) {\n    d = dISO(d);\n    return pad(d.getFullYear() % 100, p, 2);\n}\nfunction formatFullYear(d, p) {\n    return pad(d.getFullYear() % 10000, p, 4);\n}\nfunction formatFullYearISO(d, p) {\n    var day = d.getDay();\n    d = day >= 4 || day === 0 ? (0,d3_time__WEBPACK_IMPORTED_MODULE_0__.timeThursday)(d) : d3_time__WEBPACK_IMPORTED_MODULE_0__.timeThursday.ceil(d);\n    return pad(d.getFullYear() % 10000, p, 4);\n}\nfunction formatZone(d) {\n    var z = d.getTimezoneOffset();\n    return (z > 0 ? \"-\" : (z *= -1, \"+\")) + pad(z / 60 | 0, \"0\", 2) + pad(z % 60, \"0\", 2);\n}\nfunction formatUTCDayOfMonth(d, p) {\n    return pad(d.getUTCDate(), p, 2);\n}\nfunction formatUTCHour24(d, p) {\n    return pad(d.getUTCHours(), p, 2);\n}\nfunction formatUTCHour12(d, p) {\n    return pad(d.getUTCHours() % 12 || 12, p, 2);\n}\nfunction formatUTCDayOfYear(d, p) {\n    return pad(1 + d3_time__WEBPACK_IMPORTED_MODULE_1__.utcDay.count((0,d3_time__WEBPACK_IMPORTED_MODULE_2__.utcYear)(d), d), p, 3);\n}\nfunction formatUTCMilliseconds(d, p) {\n    return pad(d.getUTCMilliseconds(), p, 3);\n}\nfunction formatUTCMicroseconds(d, p) {\n    return formatUTCMilliseconds(d, p) + \"000\";\n}\nfunction formatUTCMonthNumber(d, p) {\n    return pad(d.getUTCMonth() + 1, p, 2);\n}\nfunction formatUTCMinutes(d, p) {\n    return pad(d.getUTCMinutes(), p, 2);\n}\nfunction formatUTCSeconds(d, p) {\n    return pad(d.getUTCSeconds(), p, 2);\n}\nfunction formatUTCWeekdayNumberMonday(d) {\n    var dow = d.getUTCDay();\n    return dow === 0 ? 7 : dow;\n}\nfunction formatUTCWeekNumberSunday(d, p) {\n    return pad(d3_time__WEBPACK_IMPORTED_MODULE_0__.utcSunday.count((0,d3_time__WEBPACK_IMPORTED_MODULE_2__.utcYear)(d) - 1, d), p, 2);\n}\nfunction UTCdISO(d) {\n    var day = d.getUTCDay();\n    return day >= 4 || day === 0 ? (0,d3_time__WEBPACK_IMPORTED_MODULE_0__.utcThursday)(d) : d3_time__WEBPACK_IMPORTED_MODULE_0__.utcThursday.ceil(d);\n}\nfunction formatUTCWeekNumberISO(d, p) {\n    d = UTCdISO(d);\n    return pad(d3_time__WEBPACK_IMPORTED_MODULE_0__.utcThursday.count((0,d3_time__WEBPACK_IMPORTED_MODULE_2__.utcYear)(d), d) + ((0,d3_time__WEBPACK_IMPORTED_MODULE_2__.utcYear)(d).getUTCDay() === 4), p, 2);\n}\nfunction formatUTCWeekdayNumberSunday(d) {\n    return d.getUTCDay();\n}\nfunction formatUTCWeekNumberMonday(d, p) {\n    return pad(d3_time__WEBPACK_IMPORTED_MODULE_0__.utcMonday.count((0,d3_time__WEBPACK_IMPORTED_MODULE_2__.utcYear)(d) - 1, d), p, 2);\n}\nfunction formatUTCYear(d, p) {\n    return pad(d.getUTCFullYear() % 100, p, 2);\n}\nfunction formatUTCYearISO(d, p) {\n    d = UTCdISO(d);\n    return pad(d.getUTCFullYear() % 100, p, 2);\n}\nfunction formatUTCFullYear(d, p) {\n    return pad(d.getUTCFullYear() % 10000, p, 4);\n}\nfunction formatUTCFullYearISO(d, p) {\n    var day = d.getUTCDay();\n    d = day >= 4 || day === 0 ? (0,d3_time__WEBPACK_IMPORTED_MODULE_0__.utcThursday)(d) : d3_time__WEBPACK_IMPORTED_MODULE_0__.utcThursday.ceil(d);\n    return pad(d.getUTCFullYear() % 10000, p, 4);\n}\nfunction formatUTCZone() {\n    return \"+0000\";\n}\nfunction formatLiteralPercent() {\n    return \"%\";\n}\nfunction formatUnixTimestamp(d) {\n    return +d;\n}\nfunction formatUnixTimestampSeconds(d) {\n    return Math.floor(+d / 1000);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-time-format/src/locale.js\n");

/***/ })

};
;