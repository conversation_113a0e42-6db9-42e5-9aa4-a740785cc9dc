'use client'

import { motion } from 'framer-motion'
import {
  Phone,
  Mail,
  MapPin,
  Clock,
  MessageCircle,
  Users,
  Award,
  Shield,
  Star,
  ExternalLink
} from 'lucide-react'
import Button from '@/components/ui/Button'

const CONTACT_METHODS = [
  {
    icon: Phone,
    title: 'Call Us',
    description: 'Speak directly with our travel experts',
    primary: '+91 78780 05500',
    secondary: 'Available 9 AM - 8 PM IST',
    action: 'tel:+917878005500',
    color: 'from-blue-500 to-blue-600'
  },
  {
    icon: Mail,
    title: 'Email Us',
    description: 'Get detailed information and quotes',
    primary: '<EMAIL>',
    secondary: 'Response within 24 hours',
    action: 'mailto:<EMAIL>',
    color: 'from-green-500 to-green-600'
  },
  {
    icon: MessageCircle,
    title: 'WhatsApp',
    description: 'Quick queries and instant support',
    primary: '+91 78780 05500',
    secondary: 'Available 9 AM - 8 PM IST',
    action: 'https://wa.me/917878005500',
    color: 'from-emerald-500 to-emerald-600'
  }
]

const OFFICE_INFO = {
  address: '904, Shivalik Highstreet, B/S ITC Narmada Hotel, Vastrapur, Ahmedabad, Gujarat 380015',
  hours: {
    weekdays: '9:00 AM - 8:00 PM',
    saturday: '9:00 AM - 6:00 PM',
    sunday: 'Closed (Emergency support available)'
  },
  coordinates: {
    lat: 23.0225,
    lng: 72.5714
  }
}

const QUICK_STATS = [
  {
    icon: Users,
    value: '50,000+',
    label: 'Students Served',
    color: 'text-blue-600'
  },
  {
    icon: Award,
    value: '15+',
    label: 'Years Experience',
    color: 'text-green-600'
  },
  {
    icon: Shield,
    value: '100%',
    label: 'Safety Record',
    color: 'text-purple-600'
  },
  {
    icon: Star,
    value: '4.8/5',
    label: 'Average Rating',
    color: 'text-yellow-600'
  }
]

export function ContactInfo() {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  }

  const itemVariants = {
    hidden: { opacity: 0, x: 20 },
    visible: { opacity: 1, x: 0 }
  }

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      className="space-y-8"
    >
      {/* Header */}
      <motion.div variants={itemVariants}>
        <h2 className="text-3xl font-bold text-gray-900 mb-4">Get in Touch</h2>
        <p className="text-gray-600 leading-relaxed">
          Our experienced team is ready to help you plan the perfect educational adventure.
          Choose your preferred way to connect with us.
        </p>
      </motion.div>

      {/* Contact Methods */}
      <motion.div variants={itemVariants} className="space-y-4">
        {CONTACT_METHODS.map((method, index) => (
          <div key={index} className="bg-white rounded-xl p-6 shadow-lg border border-gray-200 hover:shadow-xl transition-shadow duration-300">
            <div className="flex items-start gap-4">
              <div className={`w-12 h-12 bg-gradient-to-r ${method.color} rounded-full flex items-center justify-center flex-shrink-0`}>
                <method.icon className="w-6 h-6 text-white" />
              </div>
              <div className="flex-1">
                <h3 className="text-lg font-semibold text-gray-900 mb-1">{method.title}</h3>
                <p className="text-gray-600 text-sm mb-3">{method.description}</p>
                <div className="space-y-1">
                  <a
                    href={method.action}
                    className="text-blue-600 hover:text-blue-700 font-medium flex items-center gap-2"
                    target={method.action.startsWith('http') ? '_blank' : undefined}
                    rel={method.action.startsWith('http') ? 'noopener noreferrer' : undefined}
                  >
                    {method.primary}
                    <ExternalLink className="w-4 h-4" />
                  </a>
                  <p className="text-gray-500 text-sm">{method.secondary}</p>
                </div>
              </div>
            </div>
          </div>
        ))}
      </motion.div>

      {/* Office Information */}
      <motion.div variants={itemVariants} className="bg-gradient-to-br from-blue-50 to-green-50 rounded-xl p-6 border border-gray-200">
        <h3 className="text-xl font-semibold text-gray-900 mb-4 flex items-center gap-2">
          <MapPin className="w-5 h-5 text-blue-600" />
          Our Office
        </h3>

        <div className="space-y-4">
          <div>
            <h4 className="font-medium text-gray-900 mb-2">Address</h4>
            <p className="text-gray-700 text-sm leading-relaxed">{OFFICE_INFO.address}</p>
          </div>

          <div>
            <h4 className="font-medium text-gray-900 mb-2 flex items-center gap-2">
              <Clock className="w-4 h-4" />
              Office Hours
            </h4>
            <div className="space-y-1 text-sm text-gray-700">
              <div className="flex justify-between">
                <span>Monday - Friday:</span>
                <span className="font-medium">{OFFICE_INFO.hours.weekdays}</span>
              </div>
              <div className="flex justify-between">
                <span>Saturday:</span>
                <span className="font-medium">{OFFICE_INFO.hours.saturday}</span>
              </div>
              <div className="flex justify-between">
                <span>Sunday:</span>
                <span className="font-medium text-gray-500">{OFFICE_INFO.hours.sunday}</span>
              </div>
            </div>
          </div>

          <div className="pt-4 border-t border-gray-200">
            <Button
              variant="outline"
              size="sm"
              className="w-full"
              onClick={() => window.open(`https://maps.google.com/?q=${OFFICE_INFO.coordinates.lat},${OFFICE_INFO.coordinates.lng}`, '_blank')}
            >
              <MapPin className="w-4 h-4 mr-2" />
              Get Directions
            </Button>
          </div>
        </div>
      </motion.div>

      {/* Quick Stats */}
      <motion.div variants={itemVariants} className="bg-white rounded-xl p-6 shadow-lg border border-gray-200">
        <h3 className="text-xl font-semibold text-gray-900 mb-6 text-center">Why Choose Positive7?</h3>

        <div className="grid grid-cols-2 gap-4">
          {QUICK_STATS.map((stat, index) => (
            <div key={index} className="text-center">
              <div className={`w-10 h-10 ${stat.color} bg-opacity-10 rounded-full flex items-center justify-center mx-auto mb-2`}>
                <stat.icon className={`w-5 h-5 ${stat.color}`} />
              </div>
              <div className={`text-2xl font-bold ${stat.color} mb-1`}>{stat.value}</div>
              <div className="text-sm text-gray-600">{stat.label}</div>
            </div>
          ))}
        </div>
      </motion.div>

      {/* Emergency Contact */}
      <motion.div variants={itemVariants} className="bg-red-50 border border-red-200 rounded-xl p-6">
        <h3 className="text-lg font-semibold text-red-900 mb-3 flex items-center gap-2">
          <Shield className="w-5 h-5" />
          Emergency Support
        </h3>
        <p className="text-red-800 text-sm mb-4">
          For urgent matters during trips or emergencies, our 24/7 support team is always available.
        </p>
        <div className="flex flex-col sm:flex-row gap-3">
          <Button
            variant="outline"
            size="sm"
            className="border-red-300 text-red-700 hover:bg-red-100"
            onClick={() => window.open('tel:+917878005500', '_self')}
          >
            <Phone className="w-4 h-4 mr-2" />
            Emergency Hotline
          </Button>
          <Button
            variant="outline"
            size="sm"
            className="border-red-300 text-red-700 hover:bg-red-100"
            onClick={() => window.open('https://wa.me/917878005500?text=Emergency%20Support%20Required', '_blank')}
          >
            <MessageCircle className="w-4 h-4 mr-2" />
            WhatsApp Support
          </Button>
        </div>
      </motion.div>

      {/* Social Proof */}
      <motion.div variants={itemVariants} className="text-center">
        <div className="inline-flex items-center gap-2 bg-yellow-50 border border-yellow-200 rounded-full px-4 py-2">
          <Star className="w-4 h-4 text-yellow-600 fill-current" />
          <span className="text-sm text-yellow-800">
            <span className="font-semibold">Trusted by 500+ schools</span> across India
          </span>
        </div>
      </motion.div>
    </motion.div>
  )
}
