import { NextRequest, NextResponse } from 'next/server';
import type { CreateTestimonialData } from '@/types/database';

// GET /api/testimonials - Get testimonials with filtering
export async function GET(request: NextRequest) {
  try {
    // Return empty array for now (database removed)
    return NextResponse.json({
      data: [],
      pagination: {
        page: 1,
        limit: 10,
        total: 0,
        totalPages: 0,
      },
    });
  } catch (error) {
    console.error('Error in GET /api/testimonials:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST /api/testimonials - Create a new testimonial
export async function POST(request: NextRequest) {
  try {
    const body: CreateTestimonialData = await request.json();

    // Basic validation
    const requiredFields = ['name', 'rating', 'content'];
    for (const field of requiredFields) {
      if (!body[field as keyof CreateTestimonialData]) {
        return NextResponse.json(
          { error: `Missing required field: ${field}` },
          { status: 400 }
        );
      }
    }

    // Validate rating
    if (body.rating < 1 || body.rating > 5) {
      return NextResponse.json(
        { error: 'Rating must be between 1 and 5' },
        { status: 400 }
      );
    }

    // For now, just return success without saving to database
    return NextResponse.json({
      message: 'Testimonial submitted for review',
      data: { id: 'temp-' + Date.now(), ...body, is_approved: false }
    }, { status: 201 });
  } catch (error) {
    console.error('Error in POST /api/testimonials:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
