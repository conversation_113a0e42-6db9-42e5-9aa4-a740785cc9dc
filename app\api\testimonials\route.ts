import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabase } from '@/lib/supabase-server';
import type { CreateTestimonialData } from '@/types/database';

// GET /api/testimonials - Get testimonials with filtering
export async function GET(request: NextRequest) {
  try {
    const supabase = createServerSupabase();
    const { searchParams } = new URL(request.url);

    // Parse query parameters
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const featured = searchParams.get('featured');
    const tripId = searchParams.get('tripId');

    // Build query
    let query = supabase
      .from('testimonials')
      .select(`
        id,
        name,
        rating,
        title,
        content,
        image_url,
        is_featured,
        created_at,
        trip_id
      `, { count: 'exact' })
      .eq('is_approved', true);

    // Apply filters
    if (featured === 'true') {
      query = query.eq('is_featured', true);
    }
    if (tripId) {
      query = query.eq('trip_id', tripId);
    }

    // Apply pagination
    const from = (page - 1) * limit;
    const to = from + limit - 1;

    query = query
      .order('is_featured', { ascending: false })
      .order('created_at', { ascending: false })
      .range(from, to);

    const { data: testimonials, error, count } = await query;

    if (error) {
      console.error('Error fetching testimonials:', error);
      return NextResponse.json(
        { error: 'Failed to fetch testimonials' },
        { status: 500 }
      );
    }

    const totalPages = Math.ceil((count || 0) / limit);

    return NextResponse.json({
      data: testimonials || [],
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages,
      },
    });
  } catch (error) {
    console.error('Error in GET /api/testimonials:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST /api/testimonials - Create a new testimonial
export async function POST(request: NextRequest) {
  try {
    const body: CreateTestimonialData = await request.json();

    // Basic validation
    const requiredFields = ['name', 'rating', 'content'];
    for (const field of requiredFields) {
      if (!body[field as keyof CreateTestimonialData]) {
        return NextResponse.json(
          { error: `Missing required field: ${field}` },
          { status: 400 }
        );
      }
    }

    // Validate rating
    if (body.rating < 1 || body.rating > 5) {
      return NextResponse.json(
        { error: 'Rating must be between 1 and 5' },
        { status: 400 }
      );
    }

    // For now, just return success without saving to database
    return NextResponse.json({
      message: 'Testimonial submitted for review',
      data: { id: 'temp-' + Date.now(), ...body, is_approved: false }
    }, { status: 201 });
  } catch (error) {
    console.error('Error in POST /api/testimonials:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
