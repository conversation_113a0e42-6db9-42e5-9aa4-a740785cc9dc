import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabase } from '@/lib/supabase-server';

export async function POST(request: NextRequest) {
  try {
    console.log('Logout API: Starting logout process');
    
    const supabase = createServerSupabase();
    
    // Sign out from Supabase
    const { error } = await supabase.auth.signOut();
    
    if (error) {
      console.error('Logout API: Supabase signout error:', error);
      // Continue with cookie cleanup even if Supabase signout fails
    }

    console.log('Logout API: Supabase signout completed');

    // Create response
    const response = NextResponse.json({
      message: 'Logout successful'
    });

    // Clear session cookies
    response.cookies.delete('sb-access-token');
    response.cookies.delete('sb-refresh-token');
    
    // Also set expired cookies to ensure cleanup
    response.cookies.set('sb-access-token', '', {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 0,
      path: '/',
    });

    response.cookies.set('sb-refresh-token', '', {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 0,
      path: '/',
    });

    console.log('Logout API: Cookies cleared');

    return response;

  } catch (error) {
    console.error('Logout API: Unexpected error:', error);
    
    // Still try to clear cookies even if there's an error
    const response = NextResponse.json(
      { error: 'Logout completed with errors' },
      { status: 200 } // Use 200 since logout should always succeed from client perspective
    );

    response.cookies.delete('sb-access-token');
    response.cookies.delete('sb-refresh-token');

    return response;
  }
}
