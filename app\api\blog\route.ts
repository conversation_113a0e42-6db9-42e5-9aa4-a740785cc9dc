import { NextRequest, NextResponse } from 'next/server';

// GET /api/blog - Get blog posts with filtering
export async function GET(request: NextRequest) {
  try {
    // Return empty array for now (database removed)
    return NextResponse.json({
      data: [],
      pagination: {
        page: 1,
        limit: 10,
        total: 0,
        totalPages: 0,
      },
    });
  } catch (error) {
    console.error('Error in GET /api/blog:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST /api/blog - Create a new blog post (Admin only)
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // For now, return placeholder response
    return NextResponse.json({
      message: 'Blog post creation functionality temporarily disabled',
      data: null
    }, { status: 501 });
  } catch (error) {
    console.error('Error in POST /api/blog:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
