'use client';

import { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';

export default function TestAuthPage() {
  const { user, session, loading, signIn, signOut } = useAuth();
  const [email, setEmail] = useState('<EMAIL>');
  const [password, setPassword] = useState('customer123456');
  const [message, setMessage] = useState('');

  const handleLogin = async () => {
    setMessage('Attempting login...');
    const result = await signIn(email, password);
    
    if (result.error) {
      setMessage(`Login failed: ${result.error}`);
    } else {
      setMessage('Login successful!');
    }
  };

  const handleLogout = async () => {
    setMessage('Attempting logout...');
    const result = await signOut();
    
    if (result.error) {
      setMessage(`Logout failed: ${result.error}`);
    } else {
      setMessage('Logout successful!');
    }
  };

  if (loading) {
    return <div className="p-8">Loading...</div>;
  }

  return (
    <div className="p-8 max-w-md mx-auto">
      <h1 className="text-2xl font-bold mb-6">Test Authentication</h1>
      
      {user ? (
        <div className="space-y-4">
          <div className="p-4 bg-green-100 rounded">
            <h2 className="font-semibold">Logged in as:</h2>
            <p>Name: {user.full_name}</p>
            <p>Email: {user.email}</p>
            <p>Role: {user.role}</p>
          </div>
          
          <div className="p-4 bg-blue-100 rounded">
            <h2 className="font-semibold">Session Info:</h2>
            <p>User ID: {session?.user?.id}</p>
            <p>Expires: {session?.expires_at ? new Date(session.expires_at * 1000).toLocaleString() : 'N/A'}</p>
          </div>
          
          <button 
            onClick={handleLogout}
            className="w-full bg-red-500 text-white p-2 rounded hover:bg-red-600"
          >
            Logout
          </button>
        </div>
      ) : (
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-1">Email:</label>
            <input
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="w-full p-2 border rounded"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium mb-1">Password:</label>
            <input
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              className="w-full p-2 border rounded"
            />
          </div>
          
          <button 
            onClick={handleLogin}
            className="w-full bg-blue-500 text-white p-2 rounded hover:bg-blue-600"
          >
            Login
          </button>
        </div>
      )}
      
      {message && (
        <div className="mt-4 p-3 bg-gray-100 rounded">
          <p className="text-sm">{message}</p>
        </div>
      )}
    </div>
  );
}
