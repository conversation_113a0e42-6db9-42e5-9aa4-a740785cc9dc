{"c": ["app/layout", "webpack"], "r": ["app/admin/dashboard/page"], "m": ["(app-pages-browser)/./components/admin/AdminDashboard.tsx", "(app-pages-browser)/./components/auth/AuthGuard.tsx", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js", "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Ccomponents%5Cadmin%5CAdminDashboard.tsx&modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Ccomponents%5Cauth%5CAuthGuard.tsx&modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Ccomponents%5Clayout%5CFooter.tsx&modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Ccomponents%5Clayout%5CHeader.tsx&modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Ccomponents%5Cui%5CLoadingSpinner.tsx&server=false!"]}