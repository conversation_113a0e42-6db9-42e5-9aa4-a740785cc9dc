'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Star,
  Quote,
  ChevronLeft,
  ChevronRight,
  Play,
  Pause,
  User
} from 'lucide-react';
import { cn } from '@/lib/utils';
import type { Testimonial } from '@/types/database';

interface TestimonialData {
  id: string;
  name: string;
  rating: number;
  title: string | null;
  content: string;
  image_url: string | null;
  is_featured: boolean;
}

interface TestimonialsSectionProps {
  testimonials: TestimonialData[];
}

// Fallback testimonials if none are provided
const fallbackTestimonials: TestimonialData[] = [
  {
    id: 'fallback-1',
    name: 'Student Parent',
    rating: 5,
    title: 'Amazing Experience',
    content: 'Positive7 provided an incredible educational experience for our children. The attention to detail and care for student safety was exceptional.',
    image_url: null,
    is_featured: true,
  },
  {
    id: 'fallback-2',
    name: 'School Coordinator',
    rating: 5,
    title: 'Professional Service',
    content: 'Working with Positive7 has been a pleasure. Their team is professional, organized, and truly cares about creating meaningful educational experiences.',
    image_url: null,
    is_featured: true,
  },
];

export default function TestimonialsSection({ testimonials }: TestimonialsSectionProps) {
  // Use provided testimonials or fallback
  const displayTestimonials = testimonials.length > 0 ? testimonials : fallbackTestimonials;

  const [currentIndex, setCurrentIndex] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(true);

  useEffect(() => {
    if (!isAutoPlaying) return;

    const interval = setInterval(() => {
      setCurrentIndex((prev) => (prev + 1) % displayTestimonials.length);
    }, 5000);

    return () => clearInterval(interval);
  }, [isAutoPlaying, displayTestimonials.length]);

  const nextTestimonial = () => {
    setCurrentIndex((prev) => (prev + 1) % displayTestimonials.length);
  };

  const prevTestimonial = () => {
    setCurrentIndex((prev) => (prev - 1 + displayTestimonials.length) % displayTestimonials.length);
  };

  const goToTestimonial = (index: number) => {
    setCurrentIndex(index);
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={cn(
          'h-5 w-5',
          i < rating
            ? 'text-yellow-400 fill-current'
            : 'text-gray-300'
        )}
      />
    ));
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
      },
    },
  };

  return (
    <section className="section-padding bg-gradient-to-br from-gray-50 to-primary-50">
      <div className="container-custom">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <h2 className="section-title">
            What Our <span className="text-gradient">Students Say</span>
          </h2>
          <p className="section-subtitle">
            Real experiences from students, parents, and educators who have joined our educational journeys.
            Their stories inspire us to continue creating meaningful travel experiences.
          </p>
        </motion.div>

        {/* Main Testimonial Display */}
        <div className="relative max-w-6xl mx-auto">
          <AnimatePresence mode="wait">
            <motion.div
              key={currentIndex}
              initial={{ opacity: 0, x: 100 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -100 }}
              transition={{ duration: 0.5 }}
              className="bg-white rounded-2xl shadow-xl p-8 md:p-12 relative overflow-hidden"
            >
              {/* Background Pattern */}
              <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-primary-100 to-secondary-100 rounded-full -translate-y-16 translate-x-16 opacity-50"></div>
              <div className="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-accent-100 to-primary-100 rounded-full translate-y-12 -translate-x-12 opacity-50"></div>

              {/* Quote Icon */}
              <div className="absolute top-6 left-6 w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center">
                <Quote className="h-6 w-6 text-primary-600" />
              </div>

              <div className="relative z-10">
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 items-center">
                  {/* Testimonial Content */}
                  <div className="lg:col-span-2">
                    {/* Rating */}
                    <div className="flex items-center space-x-1 mb-6">
                      {renderStars(displayTestimonials[currentIndex].rating)}
                      <span className="ml-2 text-gray-600 font-medium">
                        {displayTestimonials[currentIndex].rating}.0
                      </span>
                    </div>

                    {/* Content */}
                    <blockquote className="text-lg md:text-xl text-gray-800 leading-relaxed mb-6 italic">
                      "{displayTestimonials[currentIndex].content}"
                    </blockquote>

                    {/* Author Info */}
                    <div className="flex items-center space-x-4">
                      <div className="w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center">
                        {displayTestimonials[currentIndex].image_url ? (
                          <Image
                            src={displayTestimonials[currentIndex].image_url}
                            alt={displayTestimonials[currentIndex].name}
                            width={48}
                            height={48}
                            className="rounded-full object-cover"
                          />
                        ) : (
                          <User className="h-6 w-6 text-primary-600" />
                        )}
                      </div>
                      <div>
                        <h4 className="font-semibold text-gray-900">
                          {displayTestimonials[currentIndex].name}
                        </h4>
                        <p className="text-primary-600 font-medium">
                          {displayTestimonials[currentIndex].title || 'Valued Customer'}
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Author Image (Large) */}
                  <div className="lg:col-span-1 flex justify-center">
                    <div className="relative">
                      <div className="w-48 h-48 bg-gradient-to-br from-primary-100 to-secondary-100 rounded-full flex items-center justify-center">
                        {displayTestimonials[currentIndex].image_url ? (
                          <Image
                            src={displayTestimonials[currentIndex].image_url}
                            alt={displayTestimonials[currentIndex].name}
                            width={192}
                            height={192}
                            className="rounded-full object-cover"
                          />
                        ) : (
                          <User className="h-24 w-24 text-primary-600" />
                        )}
                      </div>

                      {/* Decorative Elements */}
                      <div className="absolute -top-4 -right-4 w-8 h-8 bg-secondary-400 rounded-full"></div>
                      <div className="absolute -bottom-4 -left-4 w-6 h-6 bg-accent-400 rounded-full"></div>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          </AnimatePresence>

          {/* Navigation Controls */}
          <div className="flex items-center justify-center mt-8 space-x-4">
            {/* Previous Button */}
            <button
              onClick={prevTestimonial}
              className="p-3 bg-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-110 group"
              aria-label="Previous testimonial"
            >
              <ChevronLeft className="h-5 w-5 text-gray-600 group-hover:text-primary-600" />
            </button>

            {/* Dots Indicator */}
            <div className="flex space-x-2">
              {displayTestimonials.map((_, index) => (
                <button
                  key={index}
                  onClick={() => goToTestimonial(index)}
                  className={cn(
                    'w-3 h-3 rounded-full transition-all duration-300',
                    currentIndex === index
                      ? 'bg-primary-600 scale-125'
                      : 'bg-gray-300 hover:bg-primary-300'
                  )}
                  aria-label={`Go to testimonial ${index + 1}`}
                />
              ))}
            </div>

            {/* Next Button */}
            <button
              onClick={nextTestimonial}
              className="p-3 bg-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-110 group"
              aria-label="Next testimonial"
            >
              <ChevronRight className="h-5 w-5 text-gray-600 group-hover:text-primary-600" />
            </button>

            {/* Play/Pause Button */}
            <button
              onClick={() => setIsAutoPlaying(!isAutoPlaying)}
              className="p-3 bg-primary-600 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-110"
              aria-label={isAutoPlaying ? 'Pause slideshow' : 'Play slideshow'}
            >
              {isAutoPlaying ? (
                <Pause className="h-5 w-5" />
              ) : (
                <Play className="h-5 w-5" />
              )}
            </button>
          </div>
        </div>

        {/* Testimonial Grid Preview */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-16"
        >
          {displayTestimonials.slice(0, 3).map((testimonial, index) => (
            <motion.div
              key={testimonial.id}
              variants={itemVariants}
              onClick={() => goToTestimonial(index)}
              className={cn(
                'bg-white rounded-lg p-6 shadow-md hover:shadow-lg transition-all duration-300 cursor-pointer border-2',
                currentIndex === index
                  ? 'border-primary-600 scale-105'
                  : 'border-transparent hover:border-primary-200'
              )}
            >
              <div className="flex items-center space-x-1 mb-3">
                {renderStars(testimonial.rating)}
              </div>

              <p className="text-gray-600 text-sm line-clamp-3 mb-4">
                "{testimonial.content}"
              </p>

              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
                  {testimonial.image_url ? (
                    <Image
                      src={testimonial.image_url}
                      alt={testimonial.name}
                      width={32}
                      height={32}
                      className="rounded-full object-cover"
                    />
                  ) : (
                    <User className="h-4 w-4 text-primary-600" />
                  )}
                </div>
                <div>
                  <h4 className="font-medium text-gray-900 text-sm">
                    {testimonial.name}
                  </h4>
                  <p className="text-primary-600 text-xs">
                    {testimonial.title || 'Valued Customer'}
                  </p>
                </div>
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* CTA */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="text-center mt-12"
        >
          <p className="text-gray-600 mb-6">
            Ready to create your own memorable experience?
          </p>
          <Link href="/trips" className="btn-primary px-8 py-4 text-lg inline-block">
            Start Your Journey
          </Link>
        </motion.div>
      </div>
    </section>
  );
}
