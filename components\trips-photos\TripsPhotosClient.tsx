'use client'

import React, { useState } from 'react'
import Image from 'next/image'
import { motion } from 'framer-motion'
import {
  Download,
  Calendar,
  MapPin,
  Users,
  Camera,
  Search,
  ExternalLink,
  Clock,
  AlertCircle
} from 'lucide-react'

interface TripPhotoAlbum {
  id: string
  title: string
  date: string
  coverImage: string
  photoCount: number
  downloadLink: string
  location: string
  participants: string
}

interface TripsPhotosClientProps {
  albums: TripPhotoAlbum[]
}

export default function TripsPhotosClient({ albums }: TripsPhotosClientProps) {
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedLocation, setSelectedLocation] = useState('All')

  // Get unique locations
  const locations = ['All', ...Array.from(new Set(albums.map(album => album.location.split(',')[1]?.trim() || album.location)))]

  // Filter albums
  const filteredAlbums = albums.filter(album => {
    const matchesSearch = !searchQuery || 
      album.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      album.location.toLowerCase().includes(searchQuery.toLowerCase()) ||
      album.participants.toLowerCase().includes(searchQuery.toLowerCase())
    
    const matchesLocation = selectedLocation === 'All' || album.location.includes(selectedLocation)
    
    return matchesSearch && matchesLocation
  })

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center">
        <motion.div
          className="inline-flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full mb-6"
          initial={{ opacity: 0, scale: 0.5 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.6 }}
        >
          <Camera className="w-8 h-8 text-blue-600" />
        </motion.div>
        
        <motion.h1 
          className="text-4xl md:text-5xl font-bold text-gray-900 mb-4"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.1 }}
        >
          Trips Photos
        </motion.h1>
        
        <motion.div
          className="max-w-3xl mx-auto space-y-4"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          <p className="text-xl text-gray-600">
            "I don't trust words. I trust pictures." – Gilles Peress
          </p>
          <p className="text-gray-600">
            All the TRIPS PHOTOS of all trips will be uploaded here. You can download photos and see where the participants are enjoying their time.
          </p>
        </motion.div>

        {/* Important Notice */}
        <motion.div
          className="mt-6 p-4 bg-amber-50 border border-amber-200 rounded-lg max-w-2xl mx-auto"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
        >
          <div className="flex items-center gap-2 text-amber-800">
            <AlertCircle className="w-5 h-5" />
            <span className="font-medium">Important Note:</span>
          </div>
          <p className="text-amber-700 mt-1">
            After the end of the trip, all the Trips Photos will be removed after 14 days.
          </p>
        </motion.div>
      </div>

      {/* Search and Filters */}
      <motion.div
        className="bg-white rounded-xl shadow-sm border border-gray-200 p-6"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.4 }}
      >
        <div className="flex flex-col lg:flex-row gap-4 items-center justify-between">
          {/* Search */}
          <div className="relative flex-1 max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <input
              type="text"
              placeholder="Search trips..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          {/* Location Filter */}
          <div className="flex items-center gap-4">
            <select
              value={selectedLocation}
              onChange={(e) => setSelectedLocation(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              {locations.map(location => (
                <option key={location} value={location}>
                  {location}
                </option>
              ))}
            </select>
          </div>
        </div>

        {/* Results Count */}
        <div className="mt-4 text-sm text-gray-600">
          {filteredAlbums.length} {filteredAlbums.length === 1 ? 'album' : 'albums'} found
        </div>
      </motion.div>

      {/* Photo Albums Grid */}
      {filteredAlbums.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
          {filteredAlbums.map((album, index) => (
            <TripPhotoCard
              key={album.id}
              album={album}
              index={index}
            />
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <div className="text-gray-400 mb-4">
            <Camera className="w-16 h-16 mx-auto" />
          </div>
          <h3 className="text-xl font-medium text-gray-900 mb-2">No photo albums found</h3>
          <p className="text-gray-600">
            Try adjusting your search criteria or filters
          </p>
        </div>
      )}
    </div>
  )
}

// Trip Photo Card Component
function TripPhotoCard({ album, index }: { album: TripPhotoAlbum; index: number }) {
  const handleDownload = () => {
    if (album.downloadLink && album.downloadLink !== '#') {
      window.open(album.downloadLink, '_blank')
    }
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, delay: index * 0.1 }}
      className="group bg-white rounded-xl overflow-hidden shadow-md hover:shadow-xl transition-all duration-300 border border-gray-200"
    >
      {/* Album Cover Image */}
      <div className="relative h-48 overflow-hidden">
        <Image
          src={album.coverImage}
          alt={album.title}
          fill
          className="object-cover transition-transform duration-300 group-hover:scale-105"
        />
        <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300" />
        
        {/* Photo Count Badge */}
        <div className="absolute top-3 right-3">
          <div className="flex items-center gap-1 px-2 py-1 bg-black bg-opacity-70 text-white text-xs rounded-full">
            <Camera className="w-3 h-3" />
            {album.photoCount} photos
          </div>
        </div>

        {/* Download Button Overlay */}
        <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
          <button
            onClick={handleDownload}
            disabled={album.downloadLink === '#'}
            className={`
              flex items-center gap-2 px-4 py-2 rounded-lg font-medium transition-colors
              ${album.downloadLink === '#'
                ? 'bg-gray-500 text-white cursor-not-allowed'
                : 'bg-blue-600 hover:bg-blue-700 text-white'
              }
            `}
          >
            {album.downloadLink === '#' ? (
              <>
                <Clock className="w-4 h-4" />
                Coming Soon
              </>
            ) : (
              <>
                <Download className="w-4 h-4" />
                Download
              </>
            )}
          </button>
        </div>
      </div>

      {/* Album Info */}
      <div className="p-6">
        <h3 className="text-lg font-bold text-gray-900 mb-3 line-clamp-2 group-hover:text-blue-600 transition-colors">
          {album.title}
        </h3>

        <div className="space-y-2 text-sm text-gray-600">
          <div className="flex items-center gap-2">
            <Calendar className="w-4 h-4 text-gray-400" />
            {album.date}
          </div>
          
          <div className="flex items-center gap-2">
            <MapPin className="w-4 h-4 text-gray-400" />
            {album.location}
          </div>
          
          <div className="flex items-center gap-2">
            <Users className="w-4 h-4 text-gray-400" />
            {album.participants}
          </div>
        </div>

        {/* Download Button */}
        <div className="mt-4 pt-4 border-t border-gray-100">
          <button
            onClick={handleDownload}
            disabled={album.downloadLink === '#'}
            className={`
              w-full flex items-center justify-center gap-2 px-4 py-2 rounded-lg font-medium transition-colors
              ${album.downloadLink === '#'
                ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                : 'bg-blue-50 text-blue-600 hover:bg-blue-100'
              }
            `}
          >
            {album.downloadLink === '#' ? (
              <>
                <Clock className="w-4 h-4" />
                Photos Coming Soon
              </>
            ) : (
              <>
                <ExternalLink className="w-4 h-4" />
                View & Download Photos
              </>
            )}
          </button>
        </div>
      </div>
    </motion.div>
  )
}
