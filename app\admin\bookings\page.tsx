import { Metadata } from 'next'
import { Suspense } from 'react'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'
import BookingManagement from '@/components/admin/BookingManagement'
import LoadingSpinner from '@/components/ui/LoadingSpinner'

export const metadata: Metadata = {
  title: 'Booking Management - Admin Dashboard | Positive7 Educational Tours',
  description: 'Manage customer bookings, reservations, and booking status for Positive7 Educational Tours.',
  keywords: 'admin, booking management, reservations, customer bookings, Positive7'
}

export default function AdminBookingsPage() {
  return (
    <>
      <Header />
      <main className="min-h-screen bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <Suspense fallback={
            <div className="flex items-center justify-center min-h-screen">
              <LoadingSpinner size="lg" />
            </div>
          }>
            <BookingManagement />
          </Suspense>
        </div>
      </main>
      <Footer />
    </>
  )
}
