import { Metadata } from 'next'
import { notFound } from 'next/navigation'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'
import BlogPostClient from '@/components/blog/BlogPostClient'
import { createServerSupabase } from '@/lib/supabase-server'

interface BlogPostPageProps {
  params: {
    slug: string
  }
}

// Generate metadata for the blog post
export async function generateMetadata({ params }: BlogPostPageProps): Promise<Metadata> {
  const supabase = createServerSupabase()
  
  const { data: post } = await supabase
    .from('blog_posts')
    .select('title, excerpt, featured_image_url, created_at, author')
    .eq('slug', params.slug)
    .eq('status', 'published')
    .single()

  if (!post) {
    return {
      title: 'Blog Post Not Found - Positive7',
      description: 'The requested blog post could not be found.'
    }
  }

  return {
    title: `${post.title} - Positive7 Blog`,
    description: post.excerpt || `Read ${post.title} on Positive7 blog`,
    keywords: `${post.title}, Positive7, educational tours, travel blog, ${post.author}`,
    openGraph: {
      title: post.title,
      description: post.excerpt || '',
      images: post.featured_image_url ? [post.featured_image_url] : [],
      type: 'article',
      publishedTime: post.created_at,
      authors: [post.author]
    },
    twitter: {
      card: 'summary_large_image',
      title: post.title,
      description: post.excerpt || '',
      images: post.featured_image_url ? [post.featured_image_url] : []
    }
  }
}

// Generate static params for all published blog posts
export async function generateStaticParams() {
  const supabase = createServerSupabase()
  
  const { data: posts } = await supabase
    .from('blog_posts')
    .select('slug')
    .eq('status', 'published')

  return posts?.map((post) => ({
    slug: post.slug,
  })) || []
}

export default async function BlogPostPage({ params }: BlogPostPageProps) {
  const supabase = createServerSupabase()
  
  // Fetch the blog post
  const { data: post, error } = await supabase
    .from('blog_posts')
    .select(`
      id,
      title,
      slug,
      content,
      excerpt,
      featured_image_url,
      author,
      created_at,
      updated_at,
      category,
      tags,
      reading_time,
      status
    `)
    .eq('slug', params.slug)
    .eq('status', 'published')
    .single()

  if (error || !post) {
    notFound()
  }

  // Fetch related posts
  const { data: relatedPosts } = await supabase
    .from('blog_posts')
    .select('id, title, slug, excerpt, featured_image_url, author, created_at, category, reading_time')
    .eq('status', 'published')
    .neq('id', post.id)
    .eq('category', post.category)
    .limit(3)

  return (
    <>
      <Header />
      <main className="flex-1">
        <BlogPostClient 
          post={post} 
          relatedPosts={relatedPosts || []} 
        />
      </main>
      <Footer />
    </>
  )
}
