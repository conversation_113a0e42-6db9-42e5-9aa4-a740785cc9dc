"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/about/page",{

/***/ "(app-pages-browser)/./components/about/TeamSection.tsx":
/*!******************************************!*\
  !*** ./components/about/TeamSection.tsx ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TeamSection: function() { return /* binding */ TeamSection; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_MapPin_Users_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,MapPin,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_MapPin_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,MapPin,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_MapPin_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,MapPin,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_MapPin_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,MapPin,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* __next_internal_client_entry_do_not_use__ TeamSection auto */ \n\n\nconst TEAM_MEMBERS = [\n    {\n        name: \"Sunil Joseph\",\n        role: \"Director Operations\",\n        bio: \"Being born and raised in Rajasthan, his love and passion for the outdoor was built at the early age of 7. He was a good athlete and represented Nationals and Western Railways. Later he moved to Ahmedabad and worked for Dalmia Group and HFCL group. But his passion for traveling was intact and he quit his job to start with the job of his dreams. Today he travels for almost 20 days a month either on student trips or exploring new destinations.\",\n        image: \"/images/team/sunil-joseph.jpg\"\n    },\n    {\n        name: \"Sharif Mansuri\",\n        role: \"Director Administration\",\n        bio: \"Sharif is born and brought up in Ahmedabad, he did his master's in Environmental science from Gujarat University and made his career in the telecom industry, and worked for 10+ years. But his flair for nature and wildlife instigated him to leave his career in telecom and persuade his dream to explore his unfulfilled desire for nature. His crisis management skill is commendable and his helpful nature has been expressed in the form of social service.\",\n        image: \"/images/team/sharif-mansuri.jpg\"\n    },\n    {\n        name: \"Steve Everett\",\n        role: \"Director Client Relations\",\n        bio: \"Hailing from the city of Ajmer in Rajasthan, his passion for traveling and love for nature started in his early teens. He was a star athlete and won the best athlete award in school and college. His love for travel drove him to join the Airlines and started his career with Damania Airways then later with Jet Airways and as station head with SpiceJet. With a master's degree in English Literature, his interpersonal and communication skills have given him an added advantage.\",\n        image: \"/images/team/steve-everett.jpg\"\n    },\n    {\n        name: \"Arth Thakur\",\n        role: \"Team Management & Marketing Executive\",\n        bio: \"Arth was born and raised in Ahmedabad. He was mischievous during his younger days but creative and never left a chance to show his creativity. He was in different sports and he had immense love for dance. He has done his graduation with BCA in Design (Animations) and has expertise in Digital Marketing. His love for traveling made him join many trips with Positive7 as a volunteer and after many successful years, he joined Positive7 as Tour Manager.\",\n        image: \"/images/team/arth-thakur.jpg\"\n    }\n];\nfunction TeamSection() {\n    const containerVariants = {\n        hidden: {\n            opacity: 0\n        },\n        visible: {\n            opacity: 1,\n            transition: {\n                staggerChildren: 0.1\n            }\n        }\n    };\n    const itemVariants = {\n        hidden: {\n            opacity: 0,\n            y: 20\n        },\n        visible: {\n            opacity: 1,\n            y: 0\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"py-20\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-4xl font-bold text-gray-900 mb-4\",\n                            children: \"Meet Our Team\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                            lineNumber: 59,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-gray-600 max-w-3xl mx-auto\",\n                            children: \"Our passionate team of educators, adventure specialists, and travel experts work together to create unforgettable learning experiences for every student\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                            lineNumber: 60,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                    lineNumber: 58,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                    variants: containerVariants,\n                    initial: \"hidden\",\n                    whileInView: \"visible\",\n                    viewport: {\n                        once: true\n                    },\n                    className: \"grid md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                    children: TEAM_MEMBERS.map((member, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                            variants: itemVariants,\n                            className: \"group\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 h-full\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative w-32 h-32 mx-auto mb-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full h-full bg-gradient-to-r from-blue-100 to-green-100 rounded-full flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_MapPin_Users_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                className: \"w-16 h-16 text-gray-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                                lineNumber: 83,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                            lineNumber: 82,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                        lineNumber: 81,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-bold text-gray-900 mb-2\",\n                                                children: member.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                                lineNumber: 96,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-blue-600 font-medium mb-4\",\n                                                children: member.role\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                                lineNumber: 97,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 text-sm leading-relaxed\",\n                                                children: member.bio\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                                lineNumber: 98,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                        lineNumber: 95,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                lineNumber: 79,\n                                columnNumber: 15\n                            }, this)\n                        }, index, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                            lineNumber: 74,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                    lineNumber: 66,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-16 bg-gradient-to-r from-blue-50 to-green-50 rounded-2xl p-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-2xl font-bold text-gray-900 mb-4\",\n                                    children: \"Our Team Culture\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                    lineNumber: 108,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-700 max-w-3xl mx-auto\",\n                                    children: \"We believe that a passionate, diverse, and dedicated team is the foundation of exceptional educational experiences.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                    lineNumber: 109,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                            lineNumber: 107,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid md:grid-cols-4 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_MapPin_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                className: \"w-6 h-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                                lineNumber: 117,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                            lineNumber: 116,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-semibold text-gray-900 mb-2\",\n                                            children: \"Continuous Learning\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                            lineNumber: 119,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"We invest in our team's growth and development\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                            lineNumber: 120,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                    lineNumber: 115,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-green-600 rounded-full flex items-center justify-center mx-auto mb-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_MapPin_Users_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                className: \"w-6 h-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                                lineNumber: 125,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                            lineNumber: 124,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-semibold text-gray-900 mb-2\",\n                                            children: \"Collaboration\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                            lineNumber: 127,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"We work together to achieve common goals\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                            lineNumber: 128,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                    lineNumber: 123,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-purple-600 rounded-full flex items-center justify-center mx-auto mb-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_MapPin_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                className: \"w-6 h-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                                lineNumber: 133,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                            lineNumber: 132,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-semibold text-gray-900 mb-2\",\n                                            children: \"Excellence\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                            lineNumber: 135,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"We strive for the highest standards in everything\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                            lineNumber: 136,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                    lineNumber: 131,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-orange-600 rounded-full flex items-center justify-center mx-auto mb-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_MapPin_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"w-6 h-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                                lineNumber: 141,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                            lineNumber: 140,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-semibold text-gray-900 mb-2\",\n                                            children: \"Adventure Spirit\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                            lineNumber: 143,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"We embrace challenges and new experiences\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                            lineNumber: 144,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                            lineNumber: 114,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                    lineNumber: 106,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n            lineNumber: 57,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n        lineNumber: 56,\n        columnNumber: 5\n    }, this);\n}\n_c = TeamSection;\nvar _c;\n$RefreshReg$(_c, \"TeamSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/about/TeamSection.tsx\n"));

/***/ })

});