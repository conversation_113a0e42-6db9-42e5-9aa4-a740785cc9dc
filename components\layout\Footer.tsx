'use client';

import React, { useState } from 'react';
import <PERSON> from 'next/link';
import Image from 'next/image';
import { motion } from 'framer-motion';
import {
  Phone,
  Mail,
  MapPin,
  Clock,
  Facebook,
  Instagram,
  Youtube,
  MessageCircle,
  ArrowRight,
  Heart,
  CheckCircle,
  AlertCircle,
  Loader2
} from 'lucide-react';
import { COMPANY_INFO, SOCIAL_LINKS, QUICK_LINKS } from '@/lib/constants';

export default function Footer() {
  const currentYear = new Date().getFullYear();
  const [email, setEmail] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [errorMessage, setErrorMessage] = useState('');

  const handleNewsletterSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!email.trim()) return;

    setIsSubmitting(true);
    setSubmitStatus('idle');
    setErrorMessage('');

    try {
      const response = await fetch('/api/newsletter', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email: email.trim() }),
      });

      const data = await response.json();

      if (response.ok) {
        setSubmitStatus('success');
        setEmail('');
      } else {
        setSubmitStatus('error');
        setErrorMessage(data.error || 'Failed to subscribe');
      }
    } catch (error) {
      setSubmitStatus('error');
      setErrorMessage('Network error. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5,
      },
    },
  };

  return (
    <footer className="bg-gray-900 text-white">
      {/* Main Footer Content */}
      <motion.div
        variants={containerVariants}
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true }}
        className="container-custom py-16"
      >
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {/* Company Info */}
          <motion.div variants={itemVariants} className="lg:col-span-1">
            <Link href="/" className="flex items-center space-x-3 mb-6">
              <div className="relative h-16 w-16">
                <Image
                  src="/images/positive7-logo.png"
                  alt={COMPANY_INFO.name}
                  fill
                  className="object-contain"
                />
              </div>
              <div>
                <p className="text-base text-gray-300 font-medium">
                  {COMPANY_INFO.tagline}
                </p>
              </div>
            </Link>

            <p className="text-gray-300 mb-6 leading-relaxed">
              Gujarat Tourism affiliated outbound experiential learning company organizing educational trips, student tours, CAS Projects, adventure camps & workshops.
            </p>

            {/* Social Links */}
            <div className="flex space-x-4">
              <Link
                href={SOCIAL_LINKS.facebook}
                target="_blank"
                rel="noopener noreferrer"
                className="p-2 bg-gray-800 rounded-full hover:bg-primary-600 transition-colors group"
                aria-label="Facebook"
              >
                <Facebook className="h-5 w-5 group-hover:scale-110 transition-transform" />
              </Link>
              <Link
                href={SOCIAL_LINKS.instagram}
                target="_blank"
                rel="noopener noreferrer"
                className="p-2 bg-gray-800 rounded-full hover:bg-gradient-to-r hover:from-purple-500 hover:to-pink-500 transition-all group"
                aria-label="Instagram"
              >
                <Instagram className="h-5 w-5 group-hover:scale-110 transition-transform" />
              </Link>
              <Link
                href={SOCIAL_LINKS.youtube}
                target="_blank"
                rel="noopener noreferrer"
                className="p-2 bg-gray-800 rounded-full hover:bg-red-600 transition-colors group"
                aria-label="YouTube"
              >
                <Youtube className="h-5 w-5 group-hover:scale-110 transition-transform" />
              </Link>
              <Link
                href={SOCIAL_LINKS.whatsapp}
                target="_blank"
                rel="noopener noreferrer"
                className="p-2 bg-gray-800 rounded-full hover:bg-green-600 transition-colors group"
                aria-label="WhatsApp"
              >
                <MessageCircle className="h-5 w-5 group-hover:scale-110 transition-transform" />
              </Link>
            </div>
          </motion.div>

          {/* Quick Links */}
          <motion.div variants={itemVariants}>
            <h4 className="text-lg font-semibold mb-6 text-white">Quick Links</h4>
            <ul className="space-y-3">
              {QUICK_LINKS.map((link) => (
                <li key={link.name}>
                  <Link
                    href={link.href}
                    className="text-gray-300 hover:text-secondary-400 transition-colors flex items-center group"
                  >
                    <ArrowRight className="h-4 w-4 mr-2 opacity-0 group-hover:opacity-100 transition-opacity" />
                    <span className="group-hover:translate-x-1 transition-transform">
                      {link.name}
                    </span>
                  </Link>
                </li>
              ))}
            </ul>
          </motion.div>



          {/* Contact Info */}
          <motion.div variants={itemVariants}>
            <h4 className="text-lg font-semibold mb-6 text-white">Contact Info</h4>
            <div className="space-y-4">
              <div className="flex items-start space-x-3">
                <MapPin className="h-5 w-5 text-secondary-400 mt-1 flex-shrink-0" />
                <div>
                  <p className="text-gray-300 text-sm leading-relaxed">
                    904, Shivalik Highstreet,<br />
                    B/S ITC Narmada Hotel,<br />
                    Mansi-Keshavbaug Road,<br />
                    Vastrapur, Ahmedabad-380015
                  </p>
                </div>
              </div>

              <div className="flex items-center space-x-3">
                <Phone className="h-5 w-5 text-secondary-400 flex-shrink-0" />
                <div>
                  <Link
                    href={`tel:${COMPANY_INFO.phone}`}
                    className="text-gray-300 hover:text-secondary-400 transition-colors"
                  >
                    {COMPANY_INFO.phone}
                  </Link>
                  <br />
                  <Link
                    href={`tel:${COMPANY_INFO.alternatePhone}`}
                    className="text-gray-300 hover:text-secondary-400 transition-colors text-sm"
                  >
                    {COMPANY_INFO.alternatePhone}
                  </Link>
                </div>
              </div>

              <div className="flex items-center space-x-3">
                <Mail className="h-5 w-5 text-secondary-400 flex-shrink-0" />
                <Link
                  href={`mailto:${COMPANY_INFO.email}`}
                  className="text-gray-300 hover:text-secondary-400 transition-colors"
                >
                  {COMPANY_INFO.email}
                </Link>
              </div>

              <div className="flex items-center space-x-3">
                <Clock className="h-5 w-5 text-secondary-400 flex-shrink-0" />
                <div className="text-gray-300 text-sm">
                  <p>Mon - Sat: 9:00 AM - 7:00 PM</p>
                  <p>Sunday: 10:00 AM - 5:00 PM</p>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </motion.div>

      {/* Newsletter Section */}
      <motion.div
        variants={itemVariants}
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true }}
        className="border-t border-gray-800"
      >
        <div className="container-custom py-8">
          <div className="text-center">
            <h4 className="text-lg font-semibold mb-2 text-white">
              Stay Updated with Our Latest Adventures
            </h4>
            <p className="text-gray-400 mb-6">
              Subscribe to our newsletter for travel tips, destination guides, and exclusive offers.
            </p>

            {/* Success/Error Messages */}
            {submitStatus === 'success' && (
              <div className="mb-4 p-3 bg-green-900/50 border border-green-700 rounded-lg flex items-center justify-center space-x-2 max-w-md mx-auto">
                <CheckCircle className="h-5 w-5 text-green-400 flex-shrink-0" />
                <p className="text-green-300 text-sm">Successfully subscribed to newsletter!</p>
              </div>
            )}

            {submitStatus === 'error' && (
              <div className="mb-4 p-3 bg-red-900/50 border border-red-700 rounded-lg flex items-center justify-center space-x-2 max-w-md mx-auto">
                <AlertCircle className="h-5 w-5 text-red-400 flex-shrink-0" />
                <p className="text-red-300 text-sm">{errorMessage}</p>
              </div>
            )}

            <form onSubmit={handleNewsletterSubmit} className="max-w-md mx-auto flex">
              <input
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="Enter your email"
                required
                disabled={isSubmitting}
                className="flex-1 px-4 py-2 bg-gray-800 border border-gray-700 rounded-l-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent disabled:opacity-50"
              />
              <button
                type="submit"
                disabled={isSubmitting || !email.trim()}
                className="px-6 py-2 bg-primary-600 text-white rounded-r-md hover:bg-primary-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin mr-1" />
                    <span className="hidden sm:inline">Subscribing...</span>
                  </>
                ) : (
                  'Subscribe'
                )}
              </button>
            </form>
          </div>
        </div>
      </motion.div>

      {/* Bottom Bar */}
      <div className="border-t border-gray-800 bg-gray-950">
        <div className="container-custom py-6">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <div className="text-gray-400 text-sm text-center md:text-left">
              <p>
                © {currentYear} {COMPANY_INFO.name}. All rights reserved.
                <span className="hidden sm:inline"> | Gujarat Tourism Affiliated</span>
              </p>
            </div>

            <div className="flex items-center space-x-6 text-sm">
              <Link
                href="/contact"
                className="text-gray-400 hover:text-secondary-400 transition-colors"
              >
                Contact Us
              </Link>
              <Link
                href="/about"
                className="text-gray-400 hover:text-secondary-400 transition-colors"
              >
                About Us
              </Link>
              <div className="flex items-center text-gray-400">
                Made with <Heart className="h-4 w-4 mx-1 text-red-500" /> in India
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
