// Complete authentication flow test
const puppeteer = require('puppeteer');

async function testCompleteFlow() {
  console.log('🚀 Starting complete authentication flow test...\n');

  let browser;
  try {
    browser = await puppeteer.launch({ 
      headless: false, // Set to true for headless mode
      defaultViewport: null,
      args: ['--start-maximized']
    });
    
    const page = await browser.newPage();
    
    // Test 1: Access dashboard without authentication (should redirect to login)
    console.log('📋 Test 1: Accessing dashboard without authentication...');
    await page.goto('http://localhost:3001/dashboard');
    await page.waitForTimeout(2000);
    
    const currentUrl = page.url();
    if (currentUrl.includes('/auth/login')) {
      console.log('✅ Correctly redirected to login page');
    } else {
      console.log('❌ Failed to redirect to login page. Current URL:', currentUrl);
    }
    
    // Test 2: Login with valid credentials
    console.log('\n📋 Test 2: Logging in with valid credentials...');
    
    // Fill in login form
    await page.type('input[type="email"]', '<EMAIL>');
    await page.type('input[type="password"]', 'customer123456');
    
    // Click login button
    await page.click('button[type="submit"]');
    
    // Wait for navigation or response
    await page.waitForTimeout(3000);
    
    const afterLoginUrl = page.url();
    if (afterLoginUrl.includes('/dashboard')) {
      console.log('✅ Successfully logged in and redirected to dashboard');
      
      // Test 3: Check if user data is displayed
      console.log('\n📋 Test 3: Checking if user data is displayed...');
      
      try {
        await page.waitForSelector('h1', { timeout: 5000 });
        const welcomeText = await page.$eval('h1', el => el.textContent);
        
        if (welcomeText.includes('Welcome back')) {
          console.log('✅ User welcome message found:', welcomeText);
        } else {
          console.log('❌ User welcome message not found. Found:', welcomeText);
        }
        
        // Test 4: Check if bookings are loaded
        console.log('\n📋 Test 4: Checking if bookings are loaded...');
        
        // Wait a bit for data to load
        await page.waitForTimeout(2000);
        
        // Look for booking elements or "no bookings" message
        const bookingElements = await page.$$('[data-testid="booking-item"], .booking-item, .recent-booking');
        const noBookingsText = await page.$eval('body', el => el.textContent);
        
        if (bookingElements.length > 0) {
          console.log(`✅ Found ${bookingElements.length} booking(s) displayed`);
        } else if (noBookingsText.includes('No bookings') || noBookingsText.includes('Recent Bookings')) {
          console.log('✅ Bookings section is present (may be empty)');
        } else {
          console.log('⚠️ Could not determine booking status');
        }
        
        // Test 5: Test logout
        console.log('\n📋 Test 5: Testing logout...');
        
        // Look for user menu or logout button
        const userMenuButton = await page.$('button[aria-label="User menu"], button:has-text("Account"), .user-menu');
        if (userMenuButton) {
          await userMenuButton.click();
          await page.waitForTimeout(1000);
          
          const logoutButton = await page.$('button:has-text("Sign Out"), button:has-text("Logout"), a:has-text("Sign Out")');
          if (logoutButton) {
            await logoutButton.click();
            await page.waitForTimeout(2000);
            
            const afterLogoutUrl = page.url();
            if (afterLogoutUrl.includes('/auth/login') || afterLogoutUrl === 'http://localhost:3001/') {
              console.log('✅ Successfully logged out and redirected');
            } else {
              console.log('❌ Logout may have failed. Current URL:', afterLogoutUrl);
            }
          } else {
            console.log('❌ Logout button not found');
          }
        } else {
          console.log('❌ User menu not found');
        }
        
      } catch (error) {
        console.log('❌ Error during dashboard tests:', error.message);
      }
      
    } else {
      console.log('❌ Login failed or did not redirect to dashboard. Current URL:', afterLoginUrl);
      
      // Check for error messages
      const errorMessage = await page.$eval('body', el => el.textContent);
      if (errorMessage.includes('error') || errorMessage.includes('invalid')) {
        console.log('Error message found on page:', errorMessage.substring(0, 200));
      }
    }
    
  } catch (error) {
    console.error('❌ Test failed with error:', error.message);
  } finally {
    if (browser) {
      await browser.close();
    }
  }
  
  console.log('\n✨ Complete authentication flow test finished!');
}

// Check if puppeteer is available
try {
  testCompleteFlow();
} catch (error) {
  console.log('❌ Puppeteer not available. Please install it with: npm install puppeteer');
  console.log('Falling back to manual testing instructions...\n');
  
  console.log('📋 Manual Testing Instructions:');
  console.log('1. Open http://localhost:3001/dashboard in your browser');
  console.log('2. You should be redirected to http://localhost:3001/auth/login');
  console.log('3. Enter email: <EMAIL>');
  console.log('4. Enter password: customer123456');
  console.log('5. Click Login button');
  console.log('6. You should be redirected to the dashboard');
  console.log('7. Check if your name appears in the welcome message');
  console.log('8. Check if bookings are displayed');
  console.log('9. Click on your profile/user menu and logout');
  console.log('10. You should be redirected back to login or home page');
}
