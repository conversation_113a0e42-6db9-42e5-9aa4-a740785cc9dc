'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/contexts/AuthContext'

interface AuthGuardProps {
  children: React.ReactNode
  requireAuth?: boolean
  requireAdmin?: boolean
  redirectTo?: string
}

export default function AuthGuard({ 
  children, 
  requireAuth = true, 
  requireAdmin = false,
  redirectTo = '/auth/login'
}: AuthGuardProps) {
  const { user, loading } = useAuth()
  const router = useRouter()

  useEffect(() => {
    if (loading) return // Still checking auth state

    if (requireAuth && !user) {
      console.log('AuthGuard: No user found, redirecting to login')
      router.push(`${redirectTo}?redirectTo=${encodeURIComponent(window.location.pathname)}`)
      return
    }

    if (requireAdmin && (!user || user.role !== 'admin')) {
      console.log('AuthGuard: Admin access required, redirecting')
      router.push(`${redirectTo}?redirectTo=${encodeURIComponent(window.location.pathname)}`)
      return
    }
  }, [user, loading, requireAuth, requireAdmin, redirectTo, router])

  // Show loading while checking auth
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
        <div className="ml-4 text-gray-600">Checking authentication...</div>
      </div>
    )
  }

  // Don't render if auth requirements not met
  if (requireAuth && !user) {
    return null
  }

  if (requireAdmin && (!user || user.role !== 'admin')) {
    return null
  }

  return <>{children}</>
}
