'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/contexts/AuthContext'

interface AuthGuardProps {
  children: React.ReactNode
  requireAuth?: boolean
  requireAdmin?: boolean
  redirectTo?: string
}

export default function AuthGuard({
  children,
  requireAuth = true,
  requireAdmin = false,
  redirectTo = '/auth/login'
}: AuthGuardProps) {
  const { user, loading } = useAuth()

  // Show loading while checking auth
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
        <div className="ml-4 text-gray-600">Checking authentication...</div>
      </div>
    )
  }

  // Log auth state for debugging
  console.log('AuthGuard: Auth state - user:', user?.email, 'role:', user?.role, 'requireAdmin:', requireAdmin);

  // Don't render if auth requirements not met
  if (requireAuth && !user) {
    console.log('AuthGuard: No user found, not rendering content');
    return null
  }

  if (requireAdmin && (!user || user.role !== 'admin')) {
    console.log('AuthGuard: Admin access required but user is not admin, not rendering content');
    return null
  }

  console.log('AuthGuard: Auth requirements met, rendering content');
  return <>{children}</>
}
