import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabase } from '@/lib/supabase-server';

export async function POST(request: NextRequest) {
  try {
    console.log('Google Auth API: Starting Google OAuth process');
    
    const supabase = createServerSupabase();
    
    // Get the origin for redirect URL
    const origin = request.headers.get('origin') || 'http://localhost:3000';
    
    // Initiate Google OAuth
    const { data, error } = await supabase.auth.signInWithOAuth({
      provider: 'google',
      options: {
        redirectTo: `${origin}/auth/callback`,
        queryParams: {
          access_type: 'offline',
          prompt: 'consent',
        },
      },
    });

    if (error) {
      console.error('Google Auth API: OAuth error:', error);
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    console.log('Google Auth API: OAuth URL generated successfully');

    return NextResponse.json({
      url: data.url,
      provider: data.provider,
    });

  } catch (error) {
    console.error('Google Auth API: Unexpected error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
