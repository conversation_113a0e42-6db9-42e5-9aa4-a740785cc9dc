import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabase } from '@/lib/supabase-server';
import type { TripFilters, CreateTripData } from '@/types/database';

// GET /api/trips - Get all trips with filtering and pagination
export async function GET(request: NextRequest) {
  try {
    const supabase = createServerSupabase();
    const { searchParams } = new URL(request.url);

    // Parse query parameters
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search');
    const difficulty = searchParams.get('difficulty');
    const destination = searchParams.get('destination');
    const minPrice = searchParams.get('minPrice');
    const maxPrice = searchParams.get('maxPrice');

    // Build query
    let query = supabase
      .from('trips')
      .select(`
        id,
        title,
        slug,
        description,
        destination,
        duration_days,
        price_per_person,
        difficulty,
        featured_image_url,
        is_featured,
        is_active
      `, { count: 'exact' })
      .eq('is_active', true);

    // Apply filters
    if (search) {
      query = query.or(`title.ilike.%${search}%,description.ilike.%${search}%,destination.ilike.%${search}%`);
    }
    if (difficulty && difficulty !== 'all') {
      query = query.eq('difficulty', difficulty);
    }
    if (destination) {
      query = query.ilike('destination', `%${destination}%`);
    }
    if (minPrice) {
      query = query.gte('price_per_person', parseInt(minPrice));
    }
    if (maxPrice) {
      query = query.lte('price_per_person', parseInt(maxPrice));
    }

    // Apply pagination
    const from = (page - 1) * limit;
    const to = from + limit - 1;

    query = query
      .order('is_featured', { ascending: false })
      .order('created_at', { ascending: false })
      .range(from, to);

    const { data: trips, error, count } = await query;

    if (error) {
      console.error('Error fetching trips:', error);
      return NextResponse.json(
        { error: 'Failed to fetch trips' },
        { status: 500 }
      );
    }

    const totalPages = Math.ceil((count || 0) / limit);

    return NextResponse.json({
      data: trips || [],
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages,
      },
    });
  } catch (error) {
    console.error('Error in GET /api/trips:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST /api/trips - Create a new trip (Admin only)
export async function POST(request: NextRequest) {
  try {
    const body: CreateTripData = await request.json();

    // For now, return placeholder response
    return NextResponse.json({
      message: 'Trip creation functionality temporarily disabled',
      data: null
    }, { status: 501 });
  } catch (error) {
    console.error('Error in POST /api/trips:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
