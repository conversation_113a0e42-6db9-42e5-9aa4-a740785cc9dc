import { NextRequest, NextResponse } from 'next/server';
import type { TripFilters, CreateTripData } from '@/types/database';

// GET /api/trips - Get all trips with filtering and pagination
export async function GET(request: NextRequest) {
  try {
    // Return empty array for now (database removed)
    return NextResponse.json({
      data: [],
      pagination: {
        page: 1,
        limit: 10,
        total: 0,
        totalPages: 0,
      },
    });
  } catch (error) {
    console.error('Error in GET /api/trips:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST /api/trips - Create a new trip (Admin only)
export async function POST(request: NextRequest) {
  try {
    const body: CreateTripData = await request.json();

    // For now, return placeholder response
    return NextResponse.json({
      message: 'Trip creation functionality temporarily disabled',
      data: null
    }, { status: 501 });
  } catch (error) {
    console.error('Error in POST /api/trips:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
