'use client'

import { motion } from 'framer-motion'
import Image from 'next/image'
import {
  BookOpen,
  Users,
  MapPin,
  Award
} from 'lucide-react'

const TEAM_MEMBERS = [
  {
    name: '<PERSON><PERSON>',
    role: 'Director Operations',
    bio: 'Being born and raised in Rajasthan, his love and passion for the outdoor was built at the early age of 7. He was a good athlete and represented Nationals and Western Railways. Later he moved to Ahmedabad and worked for Dalmia Group and HFCL group. But his passion for traveling was intact and he quit his job to start with the job of his dreams. Today he travels for almost 20 days a month either on student trips or exploring new destinations.',
    image: '/images/team/sunil-joseph.jpg'
  },
  {
    name: '<PERSON>',
    role: 'Director Administration',
    bio: '<PERSON> is born and brought up in Ahmedabad, he did his master\'s in Environmental science from Gujarat University and made his career in the telecom industry, and worked for 10+ years. But his flair for nature and wildlife instigated him to leave his career in telecom and persuade his dream to explore his unfulfilled desire for nature. His crisis management skill is commendable and his helpful nature has been expressed in the form of social service.',
    image: '/images/team/sharif-mansuri.jpg'
  },
  {
    name: '<PERSON>',
    role: 'Director Client <PERSON>',
    bio: 'Hailing from the city of Ajmer in Rajasthan, his passion for traveling and love for nature started in his early teens. He was a star athlete and won the best athlete award in school and college. His love for travel drove him to join the Airlines and started his career with Damania Airways then later with Jet Airways and as station head with SpiceJet. With a master\'s degree in English Literature, his interpersonal and communication skills have given him an added advantage.',
    image: '/images/team/steve-everett.jpg'
  },
  {
    name: 'Arth Thakur',
    role: 'Team Management & Marketing Executive',
    bio: 'Arth was born and raised in Ahmedabad. He was mischievous during his younger days but creative and never left a chance to show his creativity. He was in different sports and he had immense love for dance. He has done his graduation with BCA in Design (Animations) and has expertise in Digital Marketing. His love for traveling made him join many trips with Positive7 as a volunteer and after many successful years, he joined Positive7 as Tour Manager.',
    image: '/images/team/arth-thakur.jpg'
  }
]

export function TeamSection() {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  }

  return (
    <section className="py-20">
      <div className="max-w-7xl mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">Meet Our Team</h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Our passionate team of educators, adventure specialists, and travel experts work together
            to create unforgettable learning experiences for every student
          </p>
        </div>

        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="grid md:grid-cols-2 lg:grid-cols-3 gap-8"
        >
          {TEAM_MEMBERS.map((member, index) => (
            <motion.div
              key={index}
              variants={itemVariants}
              className="group"
            >
              <div className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 h-full">
                {/* Profile Image */}
                <div className="relative w-32 h-32 mx-auto mb-6">
                  <div className="w-full h-full bg-gradient-to-r from-blue-100 to-green-100 rounded-full flex items-center justify-center">
                    <Users className="w-16 h-16 text-gray-400" />
                  </div>
                  {/* In a real implementation, you would use the actual image */}
                  {/* <Image
                    src={member.image}
                    alt={member.name}
                    fill
                    className="object-cover rounded-full"
                  /> */}
                </div>

                {/* Member Info */}
                <div className="text-center">
                  <h3 className="text-xl font-bold text-gray-900 mb-2">{member.name}</h3>
                  <p className="text-blue-600 font-medium mb-4">{member.role}</p>
                  <p className="text-gray-600 text-sm leading-relaxed">{member.bio}</p>
                </div>
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Team Culture */}
        <div className="mt-16 bg-gradient-to-r from-blue-50 to-green-50 rounded-2xl p-8">
          <div className="text-center mb-8">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">Our Team Culture</h3>
            <p className="text-gray-700 max-w-3xl mx-auto">
              We believe that a passionate, diverse, and dedicated team is the foundation of exceptional educational experiences.
            </p>
          </div>

          <div className="grid md:grid-cols-4 gap-6">
            <div className="text-center">
              <div className="w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-3">
                <BookOpen className="w-6 h-6 text-white" />
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">Continuous Learning</h4>
              <p className="text-sm text-gray-600">We invest in our team's growth and development</p>
            </div>

            <div className="text-center">
              <div className="w-12 h-12 bg-green-600 rounded-full flex items-center justify-center mx-auto mb-3">
                <Users className="w-6 h-6 text-white" />
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">Collaboration</h4>
              <p className="text-sm text-gray-600">We work together to achieve common goals</p>
            </div>

            <div className="text-center">
              <div className="w-12 h-12 bg-purple-600 rounded-full flex items-center justify-center mx-auto mb-3">
                <Award className="w-6 h-6 text-white" />
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">Excellence</h4>
              <p className="text-sm text-gray-600">We strive for the highest standards in everything</p>
            </div>

            <div className="text-center">
              <div className="w-12 h-12 bg-orange-600 rounded-full flex items-center justify-center mx-auto mb-3">
                <MapPin className="w-6 h-6 text-white" />
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">Adventure Spirit</h4>
              <p className="text-sm text-gray-600">We embrace challenges and new experiences</p>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
