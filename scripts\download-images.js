const fs = require('fs');
const path = require('path');
const https = require('https');
const http = require('http');

// Create directories if they don't exist
const createDir = (dir) => {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
};

// Download image function
const downloadImage = (url, filepath) => {
  return new Promise((resolve, reject) => {
    const protocol = url.startsWith('https') ? https : http;
    
    protocol.get(url, (response) => {
      if (response.statusCode === 200) {
        const fileStream = fs.createWriteStream(filepath);
        response.pipe(fileStream);
        
        fileStream.on('finish', () => {
          fileStream.close();
          console.log(`✅ Downloaded: ${path.basename(filepath)}`);
          resolve(filepath);
        });
        
        fileStream.on('error', (err) => {
          fs.unlink(filepath, () => {}); // Delete the file on error
          reject(err);
        });
      } else {
        reject(new Error(`Failed to download ${url}: ${response.statusCode}`));
      }
    }).on('error', (err) => {
      reject(err);
    });
  });
};

// Get file extension from URL
const getExtension = (url) => {
  const urlPath = new URL(url).pathname;
  const ext = path.extname(urlPath);
  return ext || '.jpg'; // Default to .jpg if no extension
};

// Generate local filename
const generateLocalFilename = (url, index) => {
  const ext = getExtension(url);
  const urlObj = new URL(url);
  const pathParts = urlObj.pathname.split('/').filter(Boolean);
  const filename = pathParts[pathParts.length - 1] || `image-${index}`;
  
  // Clean filename
  const cleanFilename = filename.replace(/[^a-zA-Z0-9.-]/g, '-');
  return cleanFilename.endsWith(ext) ? cleanFilename : `${cleanFilename}${ext}`;
};

// All external image URLs found in the project
const imageUrls = [
  // From constants.ts - FEATURED_TRIPS
  'https://positive7.in/wp-content/uploads/2025/01/gettyimages-1134041601-612x612-1.jpg',
  
  // From constants.ts - TESTIMONIALS (Pexels images)
  'https://images.pexels.com/photos/774909/pexels-photo-774909.jpeg?auto=compress&cs=tinysrgb&w=400&h=400&fit=crop',
  'https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?auto=compress&cs=tinysrgb&w=400&h=400&fit=crop',
  'https://images.pexels.com/photos/415829/pexels-photo-415829.jpeg?auto=compress&cs=tinysrgb&w=400&h=400&fit=crop',
  'https://images.pexels.com/photos/220453/pexels-photo-220453.jpeg?auto=compress&cs=tinysrgb&w=400&h=400&fit=crop',
  'https://images.pexels.com/photos/1181686/pexels-photo-1181686.jpeg?auto=compress&cs=tinysrgb&w=400&h=400&fit=crop',
  'https://images.pexels.com/photos/1043471/pexels-photo-1043471.jpeg?auto=compress&cs=tinysrgb&w=400&h=400&fit=crop',
  
  // From constants.ts - UDBHAV_INFO
  'https://positive7.in/wp-content/uploads/2022/07/Udbhav.jpg',
  'https://positive7.in/wp-content/uploads/2022/07/Udbhav-2-scaled.jpg',
  'https://positive7.in/wp-content/uploads/2022/07/Udbhav-1-scaled.jpg',
  'https://positive7.in/wp-content/uploads/2022/07/Udbhav-3-1024x467.jpg',
  
  // From gallery page
  'https://positive7.in/wp-content/uploads/2022/09/dusk-time-rishikesh-holy-town-travel-destination-india-1024x684.jpg',
  'https://positive7.in/wp-content/uploads/elementor/thumbs/Tirthan-Valley-Himalayan-1-scaled-qi2e45mk4qblqepqjw0jqlabp95dlyg00al0h5hit8.webp',
  'https://positive7.in/wp-content/uploads/2024/11/AMRITSAR-DHARAMSHALA-MCLEODGANJ-TRIUND-DALHOUSIE3.webp',
  'https://positive7.in/wp-content/uploads/2024/12/Jirawala_Parshwanath_Jain_Tirth-scaled.jpg',
  'https://positive7.in/wp-content/uploads/2024/11/bhrigu-lake3.webp',
  'https://positive7.in/wp-content/uploads/2024/01/Ranthambore-Rajasthan-scaled.webp',
  'https://positive7.in/wp-content/uploads/2023/01/kumbhalgarh-fort-india-scaled.webp',
  
  // From trips-photos page
  'https://positive7.in/wp-content/uploads/2025/04/WhatsApp-Image-2025-04-25-at-13.45.02-1.jpg',
  'https://positive7.in/wp-content/uploads/2025/04/WhatsApp-Image-2025-04-23-at-14.53.49.jpg',
  'https://positive7.in/wp-content/uploads/2025/04/WhatsApp-Image-2025-04-23-at-10.47.16-2.jpg',
  
  // From HeroSection fallback
  'https://positive7.in/wp-content/uploads/2022/09/dusk-time-rishikesh-holy-town-travel-destination-india-1024x684.jpg',
  
  // Additional positive7.in images from scraped content
  'https://positive7.in/wp-content/uploads/2024/11/TIRTHAN-VALLEY-JIBHI-1024x697.webp',
  'https://positive7.in/wp-content/uploads/2024/11/AMRITSAR-DHARAMSHALA-MCLEODGANJ-TRIUND-DALHOUSIE.webp',
  'https://positive7.in/wp-content/uploads/2024/11/Rajpura-1024x715.webp',
  'https://positive7.in/wp-content/uploads/2024/11/BRIGU-LAKE2.webp',
  'https://positive7.in/wp-content/uploads/2024/11/BEAS-KUND4-1024x682.webp',
  'https://positive7.in/wp-content/uploads/2022/11/Ranthambore-1024x643.jpg',
  'https://positive7.in/wp-content/uploads/2023/01/kumbhalgarh-fort-india-1024x683.webp',
  'https://positive7.in/wp-content/uploads/2023/01/lenstravelier-lwluGW5nZhU-unsplash-1024x683.webp',
  'https://positive7.in/wp-content/uploads/2024/11/Beas-Kund3-scaled.webp',
  'https://positive7.in/wp-content/uploads/2024/11/Rajpura.webp',
  'https://positive7.in/wp-content/uploads/2022/07/Manali-River.jpg',
  'https://positive7.in/wp-content/uploads/2022/09/temple-rishikesh-india-1113888.jpg',
  'https://positive7.in/wp-content/uploads/2024/01/Ranthambore-Rajasthan-scaled.webp',
  'https://positive7.in/wp-content/uploads/2023/01/kumbhalgarh-fort-india-scaled.webp',
  'https://positive7.in/wp-content/uploads/2023/01/lenstravelier-lwluGW5nZhU-unsplash-scaled.webp',
  'https://positive7.in/wp-content/uploads/2022/07/Meghalaya.jpg',
  'https://positive7.in/wp-content/uploads/2022/12/road-plains-himalayas-with-mountains-min-scaled.webp',
  'https://positive7.in/wp-content/uploads/2022/08/leh-palace-ladakh-mountains-3859217.jpg',
  'https://positive7.in/wp-content/uploads/2024/01/Jim-Corbett-uttarakhand-1-scaled.webp',
  'https://positive7.in/wp-content/uploads/2024/01/Kanha-Bhedaghat-Madhya-Pradesh-scaled.webp',
  'https://positive7.in/wp-content/uploads/2022/12/houseboat-kerala-backwaters-india-min-jpg.webp',
  'https://positive7.in/wp-content/uploads/2023/01/shivrajpur-beac.jpg',
  'https://positive7.in/wp-content/uploads/2022/12/spiti-valley-himachal-pradesh-india-min-jpg.webp',
  'https://positive7.in/wp-content/uploads/2023/01/kanj-lohana-village-resort-jpg.webp'
];

// Main download function
async function downloadAllImages() {
  console.log('🚀 Starting image download process...');
  
  // Create directories
  createDir('public/images/trips');
  createDir('public/images/testimonials');
  createDir('public/images/gallery');
  createDir('public/images/udbhav');
  createDir('public/images/misc');
  
  const downloadPromises = imageUrls.map(async (url, index) => {
    try {
      let targetDir = 'public/images/misc';
      
      // Categorize images based on URL patterns
      if (url.includes('pexels.com')) {
        targetDir = 'public/images/testimonials';
      } else if (url.includes('Udbhav')) {
        targetDir = 'public/images/udbhav';
      } else if (url.includes('positive7.in')) {
        targetDir = 'public/images/trips';
      }
      
      const filename = generateLocalFilename(url, index);
      const filepath = path.join(targetDir, filename);
      
      // Skip if file already exists
      if (fs.existsSync(filepath)) {
        console.log(`⏭️  Skipped (exists): ${filename}`);
        return { url, localPath: filepath.replace('public/', '/') };
      }
      
      await downloadImage(url, filepath);
      return { url, localPath: filepath.replace('public/', '/') };
    } catch (error) {
      console.error(`❌ Failed to download ${url}:`, error.message);
      return { url, localPath: null, error: error.message };
    }
  });
  
  const results = await Promise.all(downloadPromises);
  
  // Generate mapping file
  const mapping = {};
  results.forEach(result => {
    if (result.localPath) {
      mapping[result.url] = result.localPath;
    }
  });
  
  fs.writeFileSync('scripts/image-mapping.json', JSON.stringify(mapping, null, 2));
  
  console.log('\n📊 Download Summary:');
  console.log(`✅ Successfully downloaded: ${results.filter(r => r.localPath).length}`);
  console.log(`❌ Failed downloads: ${results.filter(r => !r.localPath).length}`);
  console.log('📄 Image mapping saved to scripts/image-mapping.json');
}

// Run the download
downloadAllImages().catch(console.error);
