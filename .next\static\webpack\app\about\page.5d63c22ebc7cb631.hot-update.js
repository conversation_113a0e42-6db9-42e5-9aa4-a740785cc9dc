"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/about/page",{

/***/ "(app-pages-browser)/./components/layout/Header.tsx":
/*!**************************************!*\
  !*** ./components/layout/Header.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Header; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_LogOut_Mail_MapPin_Menu_Phone_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=LogOut,Mail,MapPin,Menu,Phone,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_LogOut_Mail_MapPin_Menu_Phone_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=LogOut,Mail,MapPin,Menu,Phone,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_LogOut_Mail_MapPin_Menu_Phone_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=LogOut,Mail,MapPin,Menu,Phone,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_LogOut_Mail_MapPin_Menu_Phone_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=LogOut,Mail,MapPin,Menu,Phone,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_LogOut_Mail_MapPin_Menu_Phone_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=LogOut,Mail,MapPin,Menu,Phone,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _barrel_optimize_names_LogOut_Mail_MapPin_Menu_Phone_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=LogOut,Mail,MapPin,Menu,Phone,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_LogOut_Mail_MapPin_Menu_Phone_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=LogOut,Mail,MapPin,Menu,Phone,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./contexts/AuthContext.tsx\");\n/* harmony import */ var _hooks_useClientAuth__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/useClientAuth */ \"(app-pages-browser)/./hooks/useClientAuth.ts\");\n/* harmony import */ var _lib_constants__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/constants */ \"(app-pages-browser)/./lib/constants.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction Header() {\n    _s();\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isScrolled, setIsScrolled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isUserMenuOpen, setIsUserMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.usePathname)();\n    const { user: contextUser, signOut, loading: contextLoading } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth)();\n    const { user: clientUser, loading: clientLoading } = (0,_hooks_useClientAuth__WEBPACK_IMPORTED_MODULE_6__.useClientAuth)();\n    // Use client auth as primary, fallback to context auth\n    const user = clientUser || contextUser;\n    const loading = clientLoading && contextLoading;\n    // Debug logging for auth state\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        console.log(\"Header: Auth state - clientUser:\", clientUser === null || clientUser === void 0 ? void 0 : clientUser.email, \"contextUser:\", contextUser === null || contextUser === void 0 ? void 0 : contextUser.email, \"loading:\", loading);\n    }, [\n        clientUser,\n        contextUser,\n        loading\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleScroll = ()=>{\n            setIsScrolled(window.scrollY > 10);\n        };\n        window.addEventListener(\"scroll\", handleScroll);\n        return ()=>window.removeEventListener(\"scroll\", handleScroll);\n    }, []);\n    const handleSignOut = async ()=>{\n        console.log(\"Header: Signing out user\");\n        setIsUserMenuOpen(false);\n        const result = await signOut();\n        if (result.error) {\n            console.error(\"Header: Sign out error:\", result.error);\n        } else {\n            console.log(\"Header: Sign out successful\");\n            // Force a page refresh to ensure clean state\n            window.location.href = \"/\";\n        }\n    };\n    const isActivePath = (href)=>{\n        if (href === \"/\") return pathname === \"/\";\n        return pathname.startsWith(href);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-primary-900 text-white py-2 hidden lg:block\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container-custom\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center text-sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LogOut_Mail_MapPin_Menu_Phone_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Header.tsx\",\n                                                lineNumber: 66,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: _lib_constants__WEBPACK_IMPORTED_MODULE_7__.COMPANY_INFO.phone\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Header.tsx\",\n                                                lineNumber: 67,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 65,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LogOut_Mail_MapPin_Menu_Phone_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Header.tsx\",\n                                                lineNumber: 70,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: _lib_constants__WEBPACK_IMPORTED_MODULE_7__.COMPANY_INFO.email\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Header.tsx\",\n                                                lineNumber: 71,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 69,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LogOut_Mail_MapPin_Menu_Phone_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Header.tsx\",\n                                                lineNumber: 74,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Ahmedabad, Gujarat\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Header.tsx\",\n                                                lineNumber: 75,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 73,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 64,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: _lib_constants__WEBPACK_IMPORTED_MODULE_7__.SOCIAL_LINKS.facebook,\n                                        target: \"_blank\",\n                                        rel: \"noopener noreferrer\",\n                                        className: \"hover:text-secondary-400 transition-colors\",\n                                        \"aria-label\": \"Facebook\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"h-4 w-4\",\n                                            fill: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                d: \"M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Header.tsx\",\n                                                lineNumber: 87,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Header.tsx\",\n                                            lineNumber: 86,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 79,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: _lib_constants__WEBPACK_IMPORTED_MODULE_7__.SOCIAL_LINKS.instagram,\n                                        target: \"_blank\",\n                                        rel: \"noopener noreferrer\",\n                                        className: \"hover:text-secondary-400 transition-colors\",\n                                        \"aria-label\": \"Instagram\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"h-4 w-4\",\n                                            fill: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                d: \"M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987 6.62 0 11.987-5.367 11.987-11.987C24.014 5.367 18.637.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.49-3.323-1.297C4.198 14.895 3.708 13.744 3.708 12.447s.49-2.448 1.297-3.323c.875-.807 2.026-1.297 3.323-1.297s2.448.49 3.323 1.297c.807.875 1.297 2.026 1.297 3.323s-.49 2.448-1.297 3.323c-.875.807-2.026 1.297-3.323 1.297zm7.718-1.297c-.875.807-2.026 1.297-3.323 1.297s-2.448-.49-3.323-1.297c-.807-.875-1.297-2.026-1.297-3.323s.49-2.448 1.297-3.323c.875-.807 2.026-1.297 3.323-1.297s2.448.49 3.323 1.297c.807.875 1.297 2.026 1.297 3.323s-.49 2.448-1.297 3.323z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Header.tsx\",\n                                                lineNumber: 98,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Header.tsx\",\n                                            lineNumber: 97,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 90,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: _lib_constants__WEBPACK_IMPORTED_MODULE_7__.SOCIAL_LINKS.youtube,\n                                        target: \"_blank\",\n                                        rel: \"noopener noreferrer\",\n                                        className: \"hover:text-secondary-400 transition-colors\",\n                                        \"aria-label\": \"YouTube\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"h-4 w-4\",\n                                            fill: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                d: \"M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Header.tsx\",\n                                                lineNumber: 109,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Header.tsx\",\n                                            lineNumber: 108,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 101,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 78,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Header.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Header.tsx\",\n                    lineNumber: 62,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Header.tsx\",\n                lineNumber: 61,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_8__.cn)(\"sticky top-0 z-50 transition-all duration-300\", isScrolled ? \"bg-white/95 backdrop-blur-md shadow-lg\" : \"bg-white\"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"container-custom\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between h-16 lg:h-20\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/\",\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative h-10 w-10 lg:h-12 lg:w-12\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                src: \"/images/positive7-logo.svg\",\n                                                alt: _lib_constants__WEBPACK_IMPORTED_MODULE_7__.COMPANY_INFO.name,\n                                                fill: true,\n                                                className: \"object-contain\",\n                                                priority: true\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Header.tsx\",\n                                                lineNumber: 131,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Header.tsx\",\n                                            lineNumber: 130,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"hidden sm:block\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-xl lg:text-2xl font-bold text-primary-900\",\n                                                    children: _lib_constants__WEBPACK_IMPORTED_MODULE_7__.COMPANY_INFO.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Header.tsx\",\n                                                    lineNumber: 140,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs lg:text-sm text-gray-600 -mt-1\",\n                                                    children: _lib_constants__WEBPACK_IMPORTED_MODULE_7__.COMPANY_INFO.tagline\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Header.tsx\",\n                                                    lineNumber: 143,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Header.tsx\",\n                                            lineNumber: 139,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 129,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"hidden lg:flex items-center space-x-8\",\n                                    children: _lib_constants__WEBPACK_IMPORTED_MODULE_7__.NAVIGATION_ITEMS.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: item.href,\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_8__.cn)(\"text-sm font-medium transition-colors hover:text-primary-600\", isActivePath(item.href) ? \"text-primary-600 border-b-2 border-primary-600 pb-1\" : \"text-gray-700\"),\n                                            children: item.name\n                                        }, item.name, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Header.tsx\",\n                                            lineNumber: 152,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 150,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        user ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setIsUserMenuOpen(!isUserMenuOpen),\n                                                    className: \"flex items-center space-x-2 text-sm font-medium text-gray-700 hover:text-primary-600 transition-colors\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"h-8 w-8 bg-primary-100 rounded-full flex items-center justify-center\",\n                                                            children: user.profile_image_url ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                                src: user.profile_image_url,\n                                                                alt: user.full_name || \"User\",\n                                                                width: 32,\n                                                                height: 32,\n                                                                className: \"rounded-full\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Header.tsx\",\n                                                                lineNumber: 178,\n                                                                columnNumber: 25\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LogOut_Mail_MapPin_Menu_Phone_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"h-4 w-4 text-primary-600\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Header.tsx\",\n                                                                lineNumber: 186,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Header.tsx\",\n                                                            lineNumber: 176,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"hidden md:block\",\n                                                            children: user.full_name || \"Account\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Header.tsx\",\n                                                            lineNumber: 189,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Header.tsx\",\n                                                    lineNumber: 172,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.AnimatePresence, {\n                                                    children: isUserMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.motion.div, {\n                                                        initial: {\n                                                            opacity: 0,\n                                                            y: -10\n                                                        },\n                                                        animate: {\n                                                            opacity: 1,\n                                                            y: 0\n                                                        },\n                                                        exit: {\n                                                            opacity: 0,\n                                                            y: -10\n                                                        },\n                                                        className: \"absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg border border-gray-200 py-1 z-50\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                href: \"/dashboard\",\n                                                                className: \"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\",\n                                                                onClick: ()=>setIsUserMenuOpen(false),\n                                                                children: \"Dashboard\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Header.tsx\",\n                                                                lineNumber: 202,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                href: \"/dashboard?tab=profile\",\n                                                                className: \"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\",\n                                                                onClick: ()=>setIsUserMenuOpen(false),\n                                                                children: \"Profile\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Header.tsx\",\n                                                                lineNumber: 209,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                href: \"/dashboard?tab=bookings\",\n                                                                className: \"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\",\n                                                                onClick: ()=>setIsUserMenuOpen(false),\n                                                                children: \"My Bookings\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Header.tsx\",\n                                                                lineNumber: 216,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            user.role === \"admin\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                href: \"/admin/dashboard\",\n                                                                className: \"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\",\n                                                                onClick: ()=>setIsUserMenuOpen(false),\n                                                                children: \"Admin Panel\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Header.tsx\",\n                                                                lineNumber: 224,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                                                                className: \"my-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Header.tsx\",\n                                                                lineNumber: 232,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: handleSignOut,\n                                                                className: \"flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LogOut_Mail_MapPin_Menu_Phone_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                        className: \"h-4 w-4 mr-2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Header.tsx\",\n                                                                        lineNumber: 237,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    \"Sign Out\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Header.tsx\",\n                                                                lineNumber: 233,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Header.tsx\",\n                                                        lineNumber: 196,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Header.tsx\",\n                                                    lineNumber: 194,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Header.tsx\",\n                                            lineNumber: 171,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"hidden lg:flex items-center space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/auth/login\",\n                                                    className: \"text-sm font-medium text-gray-700 hover:text-primary-600 transition-colors\",\n                                                    children: \"Login\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Header.tsx\",\n                                                    lineNumber: 246,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/auth/register\",\n                                                    className: \"btn-primary text-sm\",\n                                                    children: \"Sign Up\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Header.tsx\",\n                                                    lineNumber: 252,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Header.tsx\",\n                                            lineNumber: 245,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setIsMenuOpen(!isMenuOpen),\n                                            className: \"lg:hidden p-2 text-gray-700 hover:text-primary-600 transition-colors\",\n                                            \"aria-label\": \"Toggle menu\",\n                                            children: isMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LogOut_Mail_MapPin_Menu_Phone_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"h-6 w-6\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Header.tsx\",\n                                                lineNumber: 268,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LogOut_Mail_MapPin_Menu_Phone_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"h-6 w-6\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Header.tsx\",\n                                                lineNumber: 270,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Header.tsx\",\n                                            lineNumber: 262,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 168,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 127,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Header.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.AnimatePresence, {\n                        children: isMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                height: 0\n                            },\n                            animate: {\n                                opacity: 1,\n                                height: \"auto\"\n                            },\n                            exit: {\n                                opacity: 0,\n                                height: 0\n                            },\n                            className: \"lg:hidden bg-white border-t border-gray-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"container-custom py-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"flex flex-col space-y-4\",\n                                    children: [\n                                        _lib_constants__WEBPACK_IMPORTED_MODULE_7__.NAVIGATION_ITEMS.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: item.href,\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_8__.cn)(\"text-base font-medium transition-colors\", isActivePath(item.href) ? \"text-primary-600\" : \"text-gray-700 hover:text-primary-600\"),\n                                                onClick: ()=>setIsMenuOpen(false),\n                                                children: item.name\n                                            }, item.name, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Header.tsx\",\n                                                lineNumber: 289,\n                                                columnNumber: 21\n                                            }, this)),\n                                        !user && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col space-y-2 pt-4 border-t border-gray-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/auth/login\",\n                                                    className: \"btn-outline text-center\",\n                                                    onClick: ()=>setIsMenuOpen(false),\n                                                    children: \"Login\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Header.tsx\",\n                                                    lineNumber: 306,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/auth/register\",\n                                                    className: \"btn-primary text-center\",\n                                                    onClick: ()=>setIsMenuOpen(false),\n                                                    children: \"Sign Up\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Header.tsx\",\n                                                    lineNumber: 313,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Header.tsx\",\n                                            lineNumber: 305,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 287,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 286,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 280,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Header.tsx\",\n                        lineNumber: 278,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Header.tsx\",\n                lineNumber: 118,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(Header, \"Yb1m3GyBmwj1C73QE5fiha2C74s=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_4__.usePathname,\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth,\n        _hooks_useClientAuth__WEBPACK_IMPORTED_MODULE_6__.useClientAuth\n    ];\n});\n_c = Header;\nvar _c;\n$RefreshReg$(_c, \"Header\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/layout/Header.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./hooks/useClientAuth.ts":
/*!********************************!*\
  !*** ./hooks/useClientAuth.ts ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useClientAuth: function() { return /* binding */ useClientAuth; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_supabase_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase-client */ \"(app-pages-browser)/./lib/supabase-client.ts\");\n/* __next_internal_client_entry_do_not_use__ useClientAuth auto */ \n\nfunction useClientAuth() {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const supabase = (0,_lib_supabase_client__WEBPACK_IMPORTED_MODULE_1__.createClientSupabase)();\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        console.log(\"useClientAuth: Initializing...\");\n        const checkAuth = async ()=>{\n            try {\n                // Get current session\n                const { data: { session }, error } = await supabase.auth.getSession();\n                console.log(\"useClientAuth: Session check - session:\", !!session, \"error:\", error);\n                if (error) {\n                    console.error(\"useClientAuth: Session error:\", error);\n                    setLoading(false);\n                    return;\n                }\n                if (session === null || session === void 0 ? void 0 : session.user) {\n                    console.log(\"useClientAuth: Found session for user:\", session.user.email);\n                    // Fetch user profile\n                    const { data: userProfile, error: profileError } = await supabase.from(\"users\").select(\"*\").eq(\"id\", session.user.id).single();\n                    if (profileError) {\n                        console.error(\"useClientAuth: Profile error:\", profileError);\n                    } else {\n                        console.log(\"useClientAuth: User profile loaded:\", userProfile === null || userProfile === void 0 ? void 0 : userProfile.email, userProfile === null || userProfile === void 0 ? void 0 : userProfile.role);\n                        setUser(userProfile);\n                    }\n                } else {\n                    console.log(\"useClientAuth: No session found\");\n                    setUser(null);\n                }\n            } catch (error) {\n                console.error(\"useClientAuth: Error:\", error);\n            } finally{\n                setLoading(false);\n            }\n        };\n        checkAuth();\n        // Listen for auth changes\n        const { data: { subscription } } = supabase.auth.onAuthStateChange(async (event, session)=>{\n            console.log(\"useClientAuth: Auth state changed:\", event, !!session);\n            if (session === null || session === void 0 ? void 0 : session.user) {\n                // Fetch user profile\n                const { data: userProfile } = await supabase.from(\"users\").select(\"*\").eq(\"id\", session.user.id).single();\n                setUser(userProfile);\n            } else {\n                setUser(null);\n            }\n        });\n        return ()=>subscription.unsubscribe();\n    }, []);\n    return {\n        user,\n        loading\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./hooks/useClientAuth.ts\n"));

/***/ })

});