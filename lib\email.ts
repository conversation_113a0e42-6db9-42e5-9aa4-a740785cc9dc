import nodemailer from 'nodemailer';

// Email configuration
const createTransporter = () => {
  const emailService = process.env.EMAIL_SERVICE || 'gmail';
  const emailUser = process.env.EMAIL_USER;
  const emailPassword = process.env.EMAIL_PASSWORD;

  if (!emailUser || !emailPassword) {
    console.log('Email credentials not configured. Emails will be logged to console only.');
    return null;
  }

  return nodemailer.createTransporter({
    service: emailService,
    auth: {
      user: emailUser,
      pass: emailPassword, // Use App Password for Gmail
    },
  });
};

export interface InquiryEmailData {
  id: string;
  name: string;
  email: string;
  phone?: string;
  subject?: string;
  message: string;
  inquiry_type: string;
  created_at: string;
}

export async function sendInquiryNotification(inquiry: InquiryEmailData): Promise<boolean> {
  const adminEmail = process.env.ADMIN_EMAIL;
  
  if (!adminEmail) {
    console.log('❌ ADMIN_EMAIL not configured, skipping email notification');
    return false;
  }

  const transporter = createTransporter();
  
  if (!transporter) {
    console.log('📧 Email service not configured, logging inquiry details:');
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
    console.log('📋 NEW INQUIRY RECEIVED');
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
    console.log(`👤 Name: ${inquiry.name}`);
    console.log(`📧 Email: ${inquiry.email}`);
    console.log(`📱 Phone: ${inquiry.phone || 'Not provided'}`);
    console.log(`📝 Subject: ${inquiry.subject || 'No subject'}`);
    console.log(`🏷️  Type: ${inquiry.inquiry_type}`);
    console.log(`📅 Date: ${new Date(inquiry.created_at).toLocaleString()}`);
    console.log(`🆔 ID: ${inquiry.id}`);
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
    console.log('💬 MESSAGE:');
    console.log(inquiry.message);
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
    return false;
  }

  try {
    const mailOptions = {
      from: process.env.EMAIL_USER,
      to: adminEmail,
      subject: `New Inquiry: ${inquiry.inquiry_type} - ${inquiry.name}`,
      html: `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>New Inquiry - Positive7</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 10px 10px 0 0; text-align: center;">
            <h1 style="margin: 0; font-size: 28px;">New Inquiry Received</h1>
            <p style="margin: 10px 0 0 0; opacity: 0.9;">Positive7 Educational Tours</p>
          </div>
          
          <div style="background: #f8f9fa; padding: 30px; border-radius: 0 0 10px 10px; border: 1px solid #e9ecef;">
            <div style="background: white; padding: 25px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
              <h2 style="color: #495057; margin-top: 0; border-bottom: 2px solid #e9ecef; padding-bottom: 10px;">Contact Information</h2>
              
              <table style="width: 100%; border-collapse: collapse;">
                <tr>
                  <td style="padding: 8px 0; font-weight: bold; color: #6c757d; width: 120px;">Name:</td>
                  <td style="padding: 8px 0;">${inquiry.name}</td>
                </tr>
                <tr>
                  <td style="padding: 8px 0; font-weight: bold; color: #6c757d;">Email:</td>
                  <td style="padding: 8px 0;"><a href="mailto:${inquiry.email}" style="color: #007bff; text-decoration: none;">${inquiry.email}</a></td>
                </tr>
                <tr>
                  <td style="padding: 8px 0; font-weight: bold; color: #6c757d;">Phone:</td>
                  <td style="padding: 8px 0;">${inquiry.phone || 'Not provided'}</td>
                </tr>
                <tr>
                  <td style="padding: 8px 0; font-weight: bold; color: #6c757d;">Subject:</td>
                  <td style="padding: 8px 0;">${inquiry.subject || 'No subject'}</td>
                </tr>
                <tr>
                  <td style="padding: 8px 0; font-weight: bold; color: #6c757d;">Type:</td>
                  <td style="padding: 8px 0;"><span style="background: #e3f2fd; color: #1976d2; padding: 4px 8px; border-radius: 4px; font-size: 12px;">${inquiry.inquiry_type}</span></td>
                </tr>
                <tr>
                  <td style="padding: 8px 0; font-weight: bold; color: #6c757d;">Date:</td>
                  <td style="padding: 8px 0;">${new Date(inquiry.created_at).toLocaleString()}</td>
                </tr>
                <tr>
                  <td style="padding: 8px 0; font-weight: bold; color: #6c757d;">ID:</td>
                  <td style="padding: 8px 0; font-family: monospace; background: #f8f9fa; padding: 4px 8px; border-radius: 4px;">${inquiry.id}</td>
                </tr>
              </table>
            </div>
            
            <div style="background: white; padding: 25px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); margin-top: 20px;">
              <h2 style="color: #495057; margin-top: 0; border-bottom: 2px solid #e9ecef; padding-bottom: 10px;">Message</h2>
              <div style="background: #f8f9fa; padding: 15px; border-radius: 6px; border-left: 4px solid #007bff;">
                <p style="margin: 0; white-space: pre-wrap;">${inquiry.message}</p>
              </div>
            </div>
            
            <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #e9ecef;">
              <p style="color: #6c757d; font-size: 14px; margin: 0;">
                This inquiry was submitted through the Positive7 website contact form.
              </p>
            </div>
          </div>
        </body>
        </html>
      `,
    };

    await transporter.sendMail(mailOptions);
    console.log(`✅ Email notification sent successfully to ${adminEmail}`);
    return true;
  } catch (error) {
    console.error('❌ Error sending email notification:', error);
    // Still log to console as fallback
    console.log('📧 Falling back to console logging:');
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
    console.log('📋 NEW INQUIRY RECEIVED');
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
    console.log(`👤 Name: ${inquiry.name}`);
    console.log(`📧 Email: ${inquiry.email}`);
    console.log(`📱 Phone: ${inquiry.phone || 'Not provided'}`);
    console.log(`📝 Subject: ${inquiry.subject || 'No subject'}`);
    console.log(`🏷️  Type: ${inquiry.inquiry_type}`);
    console.log(`📅 Date: ${new Date(inquiry.created_at).toLocaleString()}`);
    console.log(`🆔 ID: ${inquiry.id}`);
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
    console.log('💬 MESSAGE:');
    console.log(inquiry.message);
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
    return false;
  }
}
