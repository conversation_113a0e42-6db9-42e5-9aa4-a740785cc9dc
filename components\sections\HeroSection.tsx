'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { motion, AnimatePresence } from 'framer-motion';
import {
  ChevronLeft,
  ChevronRight,
  Play,
  MapPin,
  Calendar,
  Users,
  Star,
  ArrowRight
} from 'lucide-react';
import { COMPANY_INFO } from '@/lib/constants';
import { cn } from '@/lib/utils';
import type { Trip } from '@/types/database';

interface HeroTrip {
  id: string;
  title: string;
  slug: string;
  description: string | null;
  destination: string;
  featured_image_url: string | null;
}

interface HeroSectionProps {
  heroTrips: HeroTrip[];
}

// Fallback slides if no trips are available
const fallbackSlides = [
  {
    id: 'fallback-1',
    title: 'Discover Amazing Destinations',
    slug: 'explore',
    description: 'Experience the breathtaking beauty of India with our expertly crafted educational tours.',
    destination: 'India',
    featured_image_url: '/images/trips/gettyimages-1134041601-612x612-1.jpg',
  },
  {
    id: 'fallback-2',
    title: 'Educational Adventures Await',
    slug: 'learn',
    description: 'Immerse yourself in learning experiences that combine education with adventure.',
    destination: 'Various Locations',
    featured_image_url: '/images/trips/dusk-time-rishikesh-holy-town-travel-destination-india-1024x684.jpg',
  },
];

export default function HeroSection({ heroTrips }: HeroSectionProps) {
  // Use provided trips or fallback slides
  const slides = heroTrips.length > 0 ? heroTrips : fallbackSlides;

  const [currentSlide, setCurrentSlide] = useState(0);
  const [isPlaying, setIsPlaying] = useState(true);

  useEffect(() => {
    if (!isPlaying) return;

    const interval = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % slides.length);
    }, 5000);

    return () => clearInterval(interval);
  }, [isPlaying, slides.length]);

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % slides.length);
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + slides.length) % slides.length);
  };

  const goToSlide = (index: number) => {
    setCurrentSlide(index);
  };

  return (
    <section className="relative h-screen min-h-[600px] overflow-hidden">
      {/* Background Slides */}
      <AnimatePresence mode="wait">
        <motion.div
          key={currentSlide}
          initial={{ opacity: 0, scale: 1.1 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.9 }}
          transition={{ duration: 0.7 }}
          className="absolute inset-0"
        >
          <Image
            src={slides[currentSlide].featured_image_url || '/images/fallback-hero.jpg'}
            alt={slides[currentSlide].title}
            fill
            className="object-cover"
            priority
          />
          <div className="absolute inset-0 bg-black/40" />
        </motion.div>
      </AnimatePresence>

      {/* Content */}
      <div className="relative z-10 h-full flex items-center">
        <div className="container-custom">
          <div className="max-w-4xl">
            <AnimatePresence mode="wait">
              <motion.div
                key={currentSlide}
                initial={{ opacity: 0, y: 50 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -50 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                className="text-white"
              >
                {/* Company Quote */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.3 }}
                  className="mb-6"
                >
                  <p className="text-lg md:text-xl font-medium text-secondary-300 mb-2">
                    {COMPANY_INFO.heroQuote}
                  </p>
                  <div className="w-20 h-1 bg-secondary-400 rounded"></div>
                </motion.div>

                {/* Main Content */}
                <motion.h1
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.4 }}
                  className="hero-text mb-4"
                >
                  {slides[currentSlide].title}
                </motion.h1>

                <motion.h2
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.5 }}
                  className="text-xl md:text-2xl lg:text-3xl font-semibold text-secondary-300 mb-6"
                >
                  {slides[currentSlide].destination}
                </motion.h2>

                <motion.p
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.6 }}
                  className="text-lg md:text-xl text-gray-200 mb-8 max-w-2xl leading-relaxed"
                >
                  {slides[currentSlide].description || 'Discover amazing educational experiences with Positive7.'}
                </motion.p>

                {/* CTA Buttons */}
                <motion.div
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.7 }}
                  className="flex flex-col sm:flex-row gap-4"
                >
                  <Link
                    href={`/trips/${slides[currentSlide].slug}`}
                    className="inline-flex items-center px-8 py-4 bg-primary-600 text-white font-semibold rounded-lg hover:bg-primary-700 transition-all duration-300 transform hover:scale-105 group"
                  >
                    Explore {slides[currentSlide].destination}
                    <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
                  </Link>

                  <Link
                    href="/trips"
                    className="inline-flex items-center px-8 py-4 border-2 border-white text-white font-semibold rounded-lg hover:bg-white hover:text-gray-900 transition-all duration-300 transform hover:scale-105"
                  >
                    View All Trips
                  </Link>
                </motion.div>
              </motion.div>
            </AnimatePresence>
          </div>
        </div>
      </div>

      {/* Navigation Controls */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-20">
        <div className="flex items-center space-x-4">
          {/* Slide Indicators */}
          <div className="flex space-x-2">
            {slides.map((_, index) => (
              <button
                key={index}
                onClick={() => goToSlide(index)}
                className={cn(
                  'w-3 h-3 rounded-full transition-all duration-300',
                  currentSlide === index
                    ? 'bg-white scale-125'
                    : 'bg-white/50 hover:bg-white/75'
                )}
                aria-label={`Go to slide ${index + 1}`}
              />
            ))}
          </div>

          {/* Play/Pause Button */}
          <button
            onClick={() => setIsPlaying(!isPlaying)}
            className="p-2 bg-white/20 backdrop-blur-sm rounded-full hover:bg-white/30 transition-colors"
            aria-label={isPlaying ? 'Pause slideshow' : 'Play slideshow'}
          >
            <Play className={cn('h-4 w-4 text-white', isPlaying && 'opacity-50')} />
          </button>
        </div>
      </div>

      {/* Arrow Navigation */}
      <button
        onClick={prevSlide}
        className="absolute left-4 top-1/2 transform -translate-y-1/2 z-20 p-3 bg-white/20 backdrop-blur-sm rounded-full hover:bg-white/30 transition-colors group"
        aria-label="Previous slide"
      >
        <ChevronLeft className="h-6 w-6 text-white group-hover:scale-110 transition-transform" />
      </button>

      <button
        onClick={nextSlide}
        className="absolute right-4 top-1/2 transform -translate-y-1/2 z-20 p-3 bg-white/20 backdrop-blur-sm rounded-full hover:bg-white/30 transition-colors group"
        aria-label="Next slide"
      >
        <ChevronRight className="h-6 w-6 text-white group-hover:scale-110 transition-transform" />
      </button>

      {/* Quick Stats */}
      <motion.div
        initial={{ opacity: 0, y: 50 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8, delay: 1 }}
        className="absolute bottom-20 right-8 hidden lg:block z-20"
      >
        <div className="bg-white/10 backdrop-blur-md rounded-lg p-6 text-white">
          <h3 className="text-lg font-semibold mb-4">Why Choose Positive7?</h3>
          <div className="space-y-3">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center">
                <Users className="h-4 w-4" />
              </div>
              <span className="text-sm">1000+ Happy Students</span>
            </div>
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-secondary-600 rounded-full flex items-center justify-center">
                <MapPin className="h-4 w-4" />
              </div>
              <span className="text-sm">50+ Destinations</span>
            </div>
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-accent-600 rounded-full flex items-center justify-center">
                <Star className="h-4 w-4" />
              </div>
              <span className="text-sm">Gujarat Tourism Affiliated</span>
            </div>
          </div>
        </div>
      </motion.div>
    </section>
  );
}
