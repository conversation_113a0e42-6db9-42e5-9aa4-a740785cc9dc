"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/trips/[slug]/page",{

/***/ "(app-pages-browser)/./components/trips/TripDetailClient.tsx":
/*!***********************************************!*\
  !*** ./components/trips/TripDetailClient.tsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ TripDetailClient; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _components_layout_Header__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/layout/Header */ \"(app-pages-browser)/./components/layout/Header.tsx\");\n/* harmony import */ var _components_layout_Footer__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/layout/Footer */ \"(app-pages-browser)/./components/layout/Footer.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowLeft_Backpack_Calendar_CheckCircle_Clock_CreditCard_Heart_Home_Info_MapPin_Mountain_Package_Phone_Share2_Shield_Star_Train_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowLeft,Backpack,Calendar,CheckCircle,Clock,CreditCard,Heart,Home,Info,MapPin,Mountain,Package,Phone,Share2,Shield,Star,Train,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowLeft_Backpack_Calendar_CheckCircle_Clock_CreditCard_Heart_Home_Info_MapPin_Mountain_Package_Phone_Share2_Shield_Star_Train_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowLeft,Backpack,Calendar,CheckCircle,Clock,CreditCard,Heart,Home,Info,MapPin,Mountain,Package,Phone,Share2,Shield,Star,Train,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowLeft_Backpack_Calendar_CheckCircle_Clock_CreditCard_Heart_Home_Info_MapPin_Mountain_Package_Phone_Share2_Shield_Star_Train_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowLeft,Backpack,Calendar,CheckCircle,Clock,CreditCard,Heart,Home,Info,MapPin,Mountain,Package,Phone,Share2,Shield,Star,Train,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/share-2.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowLeft_Backpack_Calendar_CheckCircle_Clock_CreditCard_Heart_Home_Info_MapPin_Mountain_Package_Phone_Share2_Shield_Star_Train_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowLeft,Backpack,Calendar,CheckCircle,Clock,CreditCard,Heart,Home,Info,MapPin,Mountain,Package,Phone,Share2,Shield,Star,Train,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowLeft_Backpack_Calendar_CheckCircle_Clock_CreditCard_Heart_Home_Info_MapPin_Mountain_Package_Phone_Share2_Shield_Star_Train_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowLeft,Backpack,Calendar,CheckCircle,Clock,CreditCard,Heart,Home,Info,MapPin,Mountain,Package,Phone,Share2,Shield,Star,Train,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowLeft_Backpack_Calendar_CheckCircle_Clock_CreditCard_Heart_Home_Info_MapPin_Mountain_Package_Phone_Share2_Shield_Star_Train_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowLeft,Backpack,Calendar,CheckCircle,Clock,CreditCard,Heart,Home,Info,MapPin,Mountain,Package,Phone,Share2,Shield,Star,Train,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowLeft_Backpack_Calendar_CheckCircle_Clock_CreditCard_Heart_Home_Info_MapPin_Mountain_Package_Phone_Share2_Shield_Star_Train_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowLeft,Backpack,Calendar,CheckCircle,Clock,CreditCard,Heart,Home,Info,MapPin,Mountain,Package,Phone,Share2,Shield,Star,Train,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mountain.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowLeft_Backpack_Calendar_CheckCircle_Clock_CreditCard_Heart_Home_Info_MapPin_Mountain_Package_Phone_Share2_Shield_Star_Train_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowLeft,Backpack,Calendar,CheckCircle,Clock,CreditCard,Heart,Home,Info,MapPin,Mountain,Package,Phone,Share2,Shield,Star,Train,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowLeft_Backpack_Calendar_CheckCircle_Clock_CreditCard_Heart_Home_Info_MapPin_Mountain_Package_Phone_Share2_Shield_Star_Train_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowLeft,Backpack,Calendar,CheckCircle,Clock,CreditCard,Heart,Home,Info,MapPin,Mountain,Package,Phone,Share2,Shield,Star,Train,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/tram-front.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowLeft_Backpack_Calendar_CheckCircle_Clock_CreditCard_Heart_Home_Info_MapPin_Mountain_Package_Phone_Share2_Shield_Star_Train_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowLeft,Backpack,Calendar,CheckCircle,Clock,CreditCard,Heart,Home,Info,MapPin,Mountain,Package,Phone,Share2,Shield,Star,Train,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/home.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowLeft_Backpack_Calendar_CheckCircle_Clock_CreditCard_Heart_Home_Info_MapPin_Mountain_Package_Phone_Share2_Shield_Star_Train_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowLeft,Backpack,Calendar,CheckCircle,Clock,CreditCard,Heart,Home,Info,MapPin,Mountain,Package,Phone,Share2,Shield,Star,Train,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowLeft_Backpack_Calendar_CheckCircle_Clock_CreditCard_Heart_Home_Info_MapPin_Mountain_Package_Phone_Share2_Shield_Star_Train_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowLeft,Backpack,Calendar,CheckCircle,Clock,CreditCard,Heart,Home,Info,MapPin,Mountain,Package,Phone,Share2,Shield,Star,Train,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowLeft_Backpack_Calendar_CheckCircle_Clock_CreditCard_Heart_Home_Info_MapPin_Mountain_Package_Phone_Share2_Shield_Star_Train_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowLeft,Backpack,Calendar,CheckCircle,Clock,CreditCard,Heart,Home,Info,MapPin,Mountain,Package,Phone,Share2,Shield,Star,Train,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowLeft_Backpack_Calendar_CheckCircle_Clock_CreditCard_Heart_Home_Info_MapPin_Mountain_Package_Phone_Share2_Shield_Star_Train_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowLeft,Backpack,Calendar,CheckCircle,Clock,CreditCard,Heart,Home,Info,MapPin,Mountain,Package,Phone,Share2,Shield,Star,Train,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowLeft_Backpack_Calendar_CheckCircle_Clock_CreditCard_Heart_Home_Info_MapPin_Mountain_Package_Phone_Share2_Shield_Star_Train_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowLeft,Backpack,Calendar,CheckCircle,Clock,CreditCard,Heart,Home,Info,MapPin,Mountain,Package,Phone,Share2,Shield,Star,Train,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowLeft_Backpack_Calendar_CheckCircle_Clock_CreditCard_Heart_Home_Info_MapPin_Mountain_Package_Phone_Share2_Shield_Star_Train_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowLeft,Backpack,Calendar,CheckCircle,Clock,CreditCard,Heart,Home,Info,MapPin,Mountain,Package,Phone,Share2,Shield,Star,Train,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/backpack.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowLeft_Backpack_Calendar_CheckCircle_Clock_CreditCard_Heart_Home_Info_MapPin_Mountain_Package_Phone_Share2_Shield_Star_Train_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowLeft,Backpack,Calendar,CheckCircle,Clock,CreditCard,Heart,Home,Info,MapPin,Mountain,Package,Phone,Share2,Shield,Star,Train,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowLeft_Backpack_Calendar_CheckCircle_Clock_CreditCard_Heart_Home_Info_MapPin_Mountain_Package_Phone_Share2_Shield_Star_Train_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowLeft,Backpack,Calendar,CheckCircle,Clock,CreditCard,Heart,Home,Info,MapPin,Mountain,Package,Phone,Share2,Shield,Star,Train,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowLeft_Backpack_Calendar_CheckCircle_Clock_CreditCard_Heart_Home_Info_MapPin_Mountain_Package_Phone_Share2_Shield_Star_Train_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowLeft,Backpack,Calendar,CheckCircle,Clock,CreditCard,Heart,Home,Info,MapPin,Mountain,Package,Phone,Share2,Shield,Star,Train,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowLeft_Backpack_Calendar_CheckCircle_Clock_CreditCard_Heart_Home_Info_MapPin_Mountain_Package_Phone_Share2_Shield_Star_Train_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowLeft,Backpack,Calendar,CheckCircle,Clock,CreditCard,Heart,Home,Info,MapPin,Mountain,Package,Phone,Share2,Shield,Star,Train,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowLeft_Backpack_Calendar_CheckCircle_Clock_CreditCard_Heart_Home_Info_MapPin_Mountain_Package_Phone_Share2_Shield_Star_Train_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowLeft,Backpack,Calendar,CheckCircle,Clock,CreditCard,Heart,Home,Info,MapPin,Mountain,Package,Phone,Share2,Shield,Star,Train,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/Button */ \"(app-pages-browser)/./components/ui/Button.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\nfunction TripDetailClient(param) {\n    let { trip, relatedTrips } = param;\n    const containerVariants = {\n        hidden: {\n            opacity: 0\n        },\n        visible: {\n            opacity: 1,\n            transition: {\n                staggerChildren: 0.1\n            }\n        }\n    };\n    const itemVariants = {\n        hidden: {\n            opacity: 0,\n            y: 20\n        },\n        visible: {\n            opacity: 1,\n            y: 0\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Header__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                lineNumber: 58,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"flex-1\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                    variants: containerVariants,\n                    initial: \"hidden\",\n                    animate: \"visible\",\n                    className: \"min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.section, {\n                            variants: itemVariants,\n                            className: \"relative h-[70vh] overflow-hidden\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    src: trip.featured_image_url || \"/images/fallback-trip.jpg\",\n                                    alt: trip.title,\n                                    fill: true,\n                                    className: \"object-cover\",\n                                    priority: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                    lineNumber: 68,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 bg-gradient-to-t from-black/70 via-black/30 to-transparent\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                    lineNumber: 75,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute top-6 left-6 z-10\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                        href: \"/trips\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            variant: \"secondary\",\n                                            size: \"sm\",\n                                            className: \"bg-white/20 backdrop-blur-sm hover:bg-white/30\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowLeft_Backpack_Calendar_CheckCircle_Clock_CreditCard_Heart_Home_Info_MapPin_Mountain_Package_Phone_Share2_Shield_Star_Train_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                    lineNumber: 81,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Back to Trips\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                            lineNumber: 80,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                        lineNumber: 79,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                    lineNumber: 78,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute top-6 right-6 z-10 flex gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            variant: \"secondary\",\n                                            size: \"sm\",\n                                            className: \"bg-white/20 backdrop-blur-sm hover:bg-white/30\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowLeft_Backpack_Calendar_CheckCircle_Clock_CreditCard_Heart_Home_Info_MapPin_Mountain_Package_Phone_Share2_Shield_Star_Train_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                lineNumber: 90,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                            lineNumber: 89,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            variant: \"secondary\",\n                                            size: \"sm\",\n                                            className: \"bg-white/20 backdrop-blur-sm hover:bg-white/30\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowLeft_Backpack_Calendar_CheckCircle_Clock_CreditCard_Heart_Home_Info_MapPin_Mountain_Package_Phone_Share2_Shield_Star_Train_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                lineNumber: 93,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                            lineNumber: 92,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                    lineNumber: 88,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute bottom-0 left-0 right-0 p-8 text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"max-w-7xl mx-auto\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                            variants: itemVariants,\n                                            className: \"flex flex-wrap items-end justify-between gap-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1 min-w-0\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-4 mb-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"px-3 py-1 bg-blue-600 rounded-full text-sm font-medium\",\n                                                                    children: trip.destination\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                    lineNumber: 106,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowLeft_Backpack_Calendar_CheckCircle_Clock_CreditCard_Heart_Home_Info_MapPin_Mountain_Package_Phone_Share2_Shield_Star_Train_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                            className: \"w-4 h-4 fill-yellow-400 text-yellow-400\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                            lineNumber: 110,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium\",\n                                                                            children: \"4.8\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                            lineNumber: 111,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-white/80\",\n                                                                            children: \"(Reviews)\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                            lineNumber: 112,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                    lineNumber: 109,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                            lineNumber: 105,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                            className: \"text-4xl md:text-6xl font-bold mb-2\",\n                                                            children: trip.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                            lineNumber: 115,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xl text-white/90 mb-4\",\n                                                            children: trip.destination\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                            lineNumber: 116,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex flex-wrap items-center gap-6 text-white/80\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowLeft_Backpack_Calendar_CheckCircle_Clock_CreditCard_Heart_Home_Info_MapPin_Mountain_Package_Phone_Share2_Shield_Star_Train_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                            className: \"w-5 h-5\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                            lineNumber: 119,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: [\n                                                                                trip.duration_days,\n                                                                                \" Days\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                            lineNumber: 120,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                    lineNumber: 118,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowLeft_Backpack_Calendar_CheckCircle_Clock_CreditCard_Heart_Home_Info_MapPin_Mountain_Package_Phone_Share2_Shield_Star_Train_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                            className: \"w-5 h-5\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                            lineNumber: 123,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"Educational Tour\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                            lineNumber: 124,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                    lineNumber: 122,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowLeft_Backpack_Calendar_CheckCircle_Clock_CreditCard_Heart_Home_Info_MapPin_Mountain_Package_Phone_Share2_Shield_Star_Train_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                            className: \"w-5 h-5\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                            lineNumber: 127,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"capitalize\",\n                                                                            children: trip.difficulty\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                            lineNumber: 128,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                    lineNumber: 126,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                            lineNumber: 117,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                    lineNumber: 104,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-right\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-3xl font-bold mb-2\",\n                                                            children: [\n                                                                \"₹\",\n                                                                trip.price_per_person.toLocaleString()\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                            lineNumber: 133,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-white/80 mb-4\",\n                                                            children: \"per person\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                            lineNumber: 134,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            size: \"lg\",\n                                                            className: \"bg-gradient-to-r from-blue-600 to-green-600 hover:from-blue-700 hover:to-green-700\",\n                                                            children: \"Book Now\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                            lineNumber: 135,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                    lineNumber: 132,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                            lineNumber: 100,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                        lineNumber: 99,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                    lineNumber: 98,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                            lineNumber: 67,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-7xl mx-auto px-4 py-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid lg:grid-cols-3 gap-12\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"lg:col-span-2 space-y-12\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.section, {\n                                                    variants: itemVariants,\n                                                    className: \"bg-white rounded-2xl p-8 shadow-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                            className: \"text-3xl font-bold mb-6 text-gray-900\",\n                                                            children: \"Trip Overview\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                            lineNumber: 151,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-700 text-lg leading-relaxed mb-8\",\n                                                            children: trip.description || \"Discover amazing educational experiences and create unforgettable memories with Positive7.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                            lineNumber: 152,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid md:grid-cols-2 gap-6\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center gap-3\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowLeft_Backpack_Calendar_CheckCircle_Clock_CreditCard_Heart_Home_Info_MapPin_Mountain_Package_Phone_Share2_Shield_Star_Train_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                                    className: \"w-5 h-5 text-blue-600\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                                    lineNumber: 160,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"font-medium text-gray-900\",\n                                                                                            children: \"Duration\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                                            lineNumber: 162,\n                                                                                            columnNumber: 27\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"text-gray-600\",\n                                                                                            children: [\n                                                                                                trip.duration_days,\n                                                                                                \" Days\"\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                                            lineNumber: 163,\n                                                                                            columnNumber: 27\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                                    lineNumber: 161,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                            lineNumber: 159,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center gap-3\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowLeft_Backpack_Calendar_CheckCircle_Clock_CreditCard_Heart_Home_Info_MapPin_Mountain_Package_Phone_Share2_Shield_Star_Train_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                                    className: \"w-5 h-5 text-blue-600\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                                    lineNumber: 167,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"font-medium text-gray-900\",\n                                                                                            children: \"Type\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                                            lineNumber: 169,\n                                                                                            columnNumber: 27\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"text-gray-600\",\n                                                                                            children: \"Educational Tour\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                                            lineNumber: 170,\n                                                                                            columnNumber: 27\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                                    lineNumber: 168,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                            lineNumber: 166,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                    lineNumber: 158,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center gap-3\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowLeft_Backpack_Calendar_CheckCircle_Clock_CreditCard_Heart_Home_Info_MapPin_Mountain_Package_Phone_Share2_Shield_Star_Train_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                    className: \"w-5 h-5 text-blue-600\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                                    lineNumber: 176,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"font-medium text-gray-900\",\n                                                                                            children: \"Difficulty\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                                            lineNumber: 178,\n                                                                                            columnNumber: 27\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"text-gray-600 capitalize\",\n                                                                                            children: trip.difficulty\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                                            lineNumber: 179,\n                                                                                            columnNumber: 27\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                                    lineNumber: 177,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                            lineNumber: 175,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center gap-3\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowLeft_Backpack_Calendar_CheckCircle_Clock_CreditCard_Heart_Home_Info_MapPin_Mountain_Package_Phone_Share2_Shield_Star_Train_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                                    className: \"w-5 h-5 text-blue-600\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                                    lineNumber: 183,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"font-medium text-gray-900\",\n                                                                                            children: \"Destination\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                                            lineNumber: 185,\n                                                                                            columnNumber: 27\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"text-gray-600\",\n                                                                                            children: trip.destination\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                                            lineNumber: 186,\n                                                                                            columnNumber: 27\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                                    lineNumber: 184,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                            lineNumber: 182,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                    lineNumber: 174,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                            lineNumber: 157,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                    lineNumber: 150,\n                                                    columnNumber: 17\n                                                }, this),\n                                                trip.detailed_description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.section, {\n                                                    variants: itemVariants,\n                                                    className: \"bg-white rounded-2xl p-8 shadow-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                            className: \"text-3xl font-bold mb-6 text-gray-900\",\n                                                            children: \"About This Trip\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                            lineNumber: 196,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-700 text-lg leading-relaxed\",\n                                                            children: trip.detailed_description\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                            lineNumber: 197,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                    lineNumber: 195,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.section, {\n                                                    variants: itemVariants,\n                                                    className: \"bg-white rounded-2xl p-8 shadow-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                            className: \"text-3xl font-bold mb-6 text-gray-900\",\n                                                            children: \"Travel Information\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                            lineNumber: 205,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid md:grid-cols-2 gap-6\",\n                                                            children: [\n                                                                trip.mode_of_travel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-start gap-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowLeft_Backpack_Calendar_CheckCircle_Clock_CreditCard_Heart_Home_Info_MapPin_Mountain_Package_Phone_Share2_Shield_Star_Train_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                            className: \"w-5 h-5 text-blue-600 mt-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                            lineNumber: 209,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"font-medium text-gray-900 mb-1\",\n                                                                                    children: \"Mode of Travel\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                                    lineNumber: 211,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"text-gray-600\",\n                                                                                    children: trip.mode_of_travel\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                                    lineNumber: 212,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                            lineNumber: 210,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                    lineNumber: 208,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                trip.pickup_location && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-start gap-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowLeft_Backpack_Calendar_CheckCircle_Clock_CreditCard_Heart_Home_Info_MapPin_Mountain_Package_Phone_Share2_Shield_Star_Train_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                            className: \"w-5 h-5 text-green-600 mt-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                            lineNumber: 218,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"font-medium text-gray-900 mb-1\",\n                                                                                    children: \"Pickup Location\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                                    lineNumber: 220,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"text-gray-600\",\n                                                                                    children: trip.pickup_location\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                                    lineNumber: 221,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                            lineNumber: 219,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                    lineNumber: 217,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                trip.drop_location && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-start gap-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowLeft_Backpack_Calendar_CheckCircle_Clock_CreditCard_Heart_Home_Info_MapPin_Mountain_Package_Phone_Share2_Shield_Star_Train_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                            className: \"w-5 h-5 text-red-600 mt-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                            lineNumber: 227,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"font-medium text-gray-900 mb-1\",\n                                                                                    children: \"Drop Location\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                                    lineNumber: 229,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"text-gray-600\",\n                                                                                    children: trip.drop_location\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                                    lineNumber: 230,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                            lineNumber: 228,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                    lineNumber: 226,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                trip.property_used && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-start gap-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowLeft_Backpack_Calendar_CheckCircle_Clock_CreditCard_Heart_Home_Info_MapPin_Mountain_Package_Phone_Share2_Shield_Star_Train_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                            className: \"w-5 h-5 text-purple-600 mt-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                            lineNumber: 236,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"font-medium text-gray-900 mb-1\",\n                                                                                    children: \"Accommodation\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                                    lineNumber: 238,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"text-gray-600\",\n                                                                                    children: trip.property_used\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                                    lineNumber: 239,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                            lineNumber: 237,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                    lineNumber: 235,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                            lineNumber: 206,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                    lineNumber: 204,\n                                                    columnNumber: 17\n                                                }, this),\n                                                trip.activities && trip.activities.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.section, {\n                                                    variants: itemVariants,\n                                                    className: \"bg-white rounded-2xl p-8 shadow-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                            className: \"text-3xl font-bold mb-6 text-gray-900\",\n                                                            children: \"Activities Included\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                            lineNumber: 249,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid md:grid-cols-2 gap-4\",\n                                                            children: trip.activities.map((activity, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowLeft_Backpack_Calendar_CheckCircle_Clock_CreditCard_Heart_Home_Info_MapPin_Mountain_Package_Phone_Share2_Shield_Star_Train_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                            className: \"w-5 h-5 text-green-600\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                            lineNumber: 253,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-gray-700\",\n                                                                            children: activity\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                            lineNumber: 254,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, index, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                    lineNumber: 252,\n                                                                    columnNumber: 25\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                            lineNumber: 250,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        trip.optional_activities && trip.optional_activities.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mt-8\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-xl font-semibold mb-4 text-gray-900\",\n                                                                    children: \"Optional Activities\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                    lineNumber: 261,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"grid md:grid-cols-2 gap-4\",\n                                                                    children: trip.optional_activities.map((activity, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center gap-3\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowLeft_Backpack_Calendar_CheckCircle_Clock_CreditCard_Heart_Home_Info_MapPin_Mountain_Package_Phone_Share2_Shield_Star_Train_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                                    className: \"w-5 h-5 text-orange-600\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                                    lineNumber: 265,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-gray-700\",\n                                                                                    children: activity\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                                    lineNumber: 266,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, index, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                            lineNumber: 264,\n                                                                            columnNumber: 29\n                                                                        }, this))\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                    lineNumber: 262,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                            lineNumber: 260,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                    lineNumber: 248,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.section, {\n                                                    variants: itemVariants,\n                                                    className: \"bg-white rounded-2xl p-8 shadow-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                            className: \"text-3xl font-bold mb-6 text-gray-900\",\n                                                            children: \"What's Included & Excluded\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                            lineNumber: 277,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid md:grid-cols-2 gap-8\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"text-xl font-semibold mb-4 text-green-700 flex items-center gap-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowLeft_Backpack_Calendar_CheckCircle_Clock_CreditCard_Heart_Home_Info_MapPin_Mountain_Package_Phone_Share2_Shield_Star_Train_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                                    className: \"w-5 h-5\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                                    lineNumber: 282,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                \"Included\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                            lineNumber: 281,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"space-y-3\",\n                                                                            children: trip.inclusions && trip.inclusions.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-start gap-3\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowLeft_Backpack_Calendar_CheckCircle_Clock_CreditCard_Heart_Home_Info_MapPin_Mountain_Package_Phone_Share2_Shield_Star_Train_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                                            className: \"w-4 h-4 text-green-600 mt-1 flex-shrink-0\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                                            lineNumber: 288,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"text-gray-700\",\n                                                                                            children: item\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                                            lineNumber: 289,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, index, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                                    lineNumber: 287,\n                                                                                    columnNumber: 27\n                                                                                }, this))\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                            lineNumber: 285,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                    lineNumber: 280,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"text-xl font-semibold mb-4 text-red-700 flex items-center gap-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowLeft_Backpack_Calendar_CheckCircle_Clock_CreditCard_Heart_Home_Info_MapPin_Mountain_Package_Phone_Share2_Shield_Star_Train_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                                    className: \"w-5 h-5\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                                    lineNumber: 298,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                \"Not Included\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                            lineNumber: 297,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"space-y-3\",\n                                                                            children: trip.exclusions && trip.exclusions.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-start gap-3\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowLeft_Backpack_Calendar_CheckCircle_Clock_CreditCard_Heart_Home_Info_MapPin_Mountain_Package_Phone_Share2_Shield_Star_Train_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                                            className: \"w-4 h-4 text-red-600 mt-1 flex-shrink-0\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                                            lineNumber: 304,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"text-gray-700\",\n                                                                                            children: item\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                                            lineNumber: 305,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, index, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                                    lineNumber: 303,\n                                                                                    columnNumber: 27\n                                                                                }, this))\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                            lineNumber: 301,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                    lineNumber: 296,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                            lineNumber: 278,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                    lineNumber: 276,\n                                                    columnNumber: 17\n                                                }, this),\n                                                (trip.benefits && trip.benefits.length > 0 || trip.safety_supervision && trip.safety_supervision.length > 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.section, {\n                                                    variants: itemVariants,\n                                                    className: \"bg-white rounded-2xl p-8 shadow-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                            className: \"text-3xl font-bold mb-6 text-gray-900\",\n                                                            children: \"Safety & Benefits\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                            lineNumber: 316,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid md:grid-cols-2 gap-8\",\n                                                            children: [\n                                                                trip.benefits && trip.benefits.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"text-xl font-semibold mb-4 text-blue-700 flex items-center gap-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowLeft_Backpack_Calendar_CheckCircle_Clock_CreditCard_Heart_Home_Info_MapPin_Mountain_Package_Phone_Share2_Shield_Star_Train_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                    className: \"w-5 h-5\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                                    lineNumber: 322,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                \"Trip Benefits\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                            lineNumber: 321,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"space-y-3\",\n                                                                            children: trip.benefits.map((benefit, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-start gap-3\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowLeft_Backpack_Calendar_CheckCircle_Clock_CreditCard_Heart_Home_Info_MapPin_Mountain_Package_Phone_Share2_Shield_Star_Train_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                                            className: \"w-4 h-4 text-blue-600 mt-1 flex-shrink-0\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                                            lineNumber: 328,\n                                                                                            columnNumber: 33\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"text-gray-700\",\n                                                                                            children: benefit\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                                            lineNumber: 329,\n                                                                                            columnNumber: 33\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, index, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                                    lineNumber: 327,\n                                                                                    columnNumber: 31\n                                                                                }, this))\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                            lineNumber: 325,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                    lineNumber: 320,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                trip.safety_supervision && trip.safety_supervision.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"text-xl font-semibold mb-4 text-green-700 flex items-center gap-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowLeft_Backpack_Calendar_CheckCircle_Clock_CreditCard_Heart_Home_Info_MapPin_Mountain_Package_Phone_Share2_Shield_Star_Train_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                    className: \"w-5 h-5\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                                    lineNumber: 340,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                \"Safety & Supervision\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                            lineNumber: 339,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"space-y-3\",\n                                                                            children: trip.safety_supervision.map((safety, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-start gap-3\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowLeft_Backpack_Calendar_CheckCircle_Clock_CreditCard_Heart_Home_Info_MapPin_Mountain_Package_Phone_Share2_Shield_Star_Train_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                            className: \"w-4 h-4 text-green-600 mt-1 flex-shrink-0\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                                            lineNumber: 346,\n                                                                                            columnNumber: 33\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"text-gray-700\",\n                                                                                            children: safety\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                                            lineNumber: 347,\n                                                                                            columnNumber: 33\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, index, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                                    lineNumber: 345,\n                                                                                    columnNumber: 31\n                                                                                }, this))\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                            lineNumber: 343,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                    lineNumber: 338,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                            lineNumber: 317,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                    lineNumber: 315,\n                                                    columnNumber: 19\n                                                }, this),\n                                                trip.things_to_carry && trip.things_to_carry.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.section, {\n                                                    variants: itemVariants,\n                                                    className: \"bg-white rounded-2xl p-8 shadow-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                            className: \"text-3xl font-bold mb-6 text-gray-900 flex items-center gap-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowLeft_Backpack_Calendar_CheckCircle_Clock_CreditCard_Heart_Home_Info_MapPin_Mountain_Package_Phone_Share2_Shield_Star_Train_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                    className: \"w-8 h-8 text-orange-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                    lineNumber: 361,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Things to Carry\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                            lineNumber: 360,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid md:grid-cols-2 gap-4\",\n                                                            children: trip.things_to_carry.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-start gap-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowLeft_Backpack_Calendar_CheckCircle_Clock_CreditCard_Heart_Home_Info_MapPin_Mountain_Package_Phone_Share2_Shield_Star_Train_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            className: \"w-4 h-4 text-orange-600 mt-1 flex-shrink-0\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                            lineNumber: 367,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-gray-700\",\n                                                                            children: item\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                            lineNumber: 368,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, index, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                    lineNumber: 366,\n                                                                    columnNumber: 25\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                            lineNumber: 364,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                    lineNumber: 359,\n                                                    columnNumber: 19\n                                                }, this),\n                                                trip.available_dates && trip.available_dates.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.section, {\n                                                    variants: itemVariants,\n                                                    className: \"bg-white rounded-2xl p-8 shadow-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                            className: \"text-3xl font-bold mb-6 text-gray-900 flex items-center gap-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowLeft_Backpack_Calendar_CheckCircle_Clock_CreditCard_Heart_Home_Info_MapPin_Mountain_Package_Phone_Share2_Shield_Star_Train_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                    className: \"w-8 h-8 text-purple-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                    lineNumber: 379,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Available Dates\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                            lineNumber: 378,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                                                            children: trip.available_dates.map((date, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"bg-purple-50 border border-purple-200 rounded-lg p-4 text-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowLeft_Backpack_Calendar_CheckCircle_Clock_CreditCard_Heart_Home_Info_MapPin_Mountain_Package_Phone_Share2_Shield_Star_Train_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                            className: \"w-5 h-5 text-purple-600 mx-auto mb-2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                            lineNumber: 385,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-gray-800 font-medium\",\n                                                                            children: date\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                            lineNumber: 386,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, index, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                    lineNumber: 384,\n                                                                    columnNumber: 25\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                            lineNumber: 382,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                    lineNumber: 377,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.section, {\n                                                    variants: itemVariants,\n                                                    className: \"bg-white rounded-2xl p-8 shadow-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                            className: \"text-3xl font-bold mb-6 text-gray-900\",\n                                                            children: \"Payment & Cancellation Policy\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                            lineNumber: 395,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid md:grid-cols-2 gap-8\",\n                                                            children: [\n                                                                trip.payment_terms && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"text-xl font-semibold mb-4 text-green-700 flex items-center gap-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowLeft_Backpack_Calendar_CheckCircle_Clock_CreditCard_Heart_Home_Info_MapPin_Mountain_Package_Phone_Share2_Shield_Star_Train_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                                    className: \"w-5 h-5\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                                    lineNumber: 401,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                \"Payment Terms\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                            lineNumber: 400,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-700\",\n                                                                            children: trip.payment_terms\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                            lineNumber: 404,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                    lineNumber: 399,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                trip.cancellation_policy && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"text-xl font-semibold mb-4 text-red-700 flex items-center gap-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowLeft_Backpack_Calendar_CheckCircle_Clock_CreditCard_Heart_Home_Info_MapPin_Mountain_Package_Phone_Share2_Shield_Star_Train_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                                    className: \"w-5 h-5\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                                    lineNumber: 412,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                \"Cancellation Policy\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                            lineNumber: 411,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"space-y-2\",\n                                                                            children: Object.entries(trip.cancellation_policy).map((param)=>{\n                                                                                let [key, value] = param;\n                                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex justify-between\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"text-gray-600 capitalize\",\n                                                                                            children: key.replace(/_/g, \" \")\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                                            lineNumber: 418,\n                                                                                            columnNumber: 31\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"font-medium text-gray-800\",\n                                                                                            children: value\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                                            lineNumber: 419,\n                                                                                            columnNumber: 31\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, key, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                                    lineNumber: 417,\n                                                                                    columnNumber: 29\n                                                                                }, this);\n                                                                            })\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                            lineNumber: 415,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                    lineNumber: 410,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                            lineNumber: 396,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                    lineNumber: 394,\n                                                    columnNumber: 17\n                                                }, this),\n                                                trip.special_notes && trip.special_notes.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.section, {\n                                                    variants: itemVariants,\n                                                    className: \"bg-yellow-50 border border-yellow-200 rounded-2xl p-8\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                            className: \"text-2xl font-bold mb-6 text-yellow-800 flex items-center gap-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowLeft_Backpack_Calendar_CheckCircle_Clock_CreditCard_Heart_Home_Info_MapPin_Mountain_Package_Phone_Share2_Shield_Star_Train_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                    className: \"w-6 h-6\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                    lineNumber: 432,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Important Notes\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                            lineNumber: 431,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-3\",\n                                                            children: trip.special_notes.map((note, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-start gap-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowLeft_Backpack_Calendar_CheckCircle_Clock_CreditCard_Heart_Home_Info_MapPin_Mountain_Package_Phone_Share2_Shield_Star_Train_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                            className: \"w-4 h-4 text-yellow-600 mt-1 flex-shrink-0\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                            lineNumber: 438,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-yellow-800\",\n                                                                            children: note\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                            lineNumber: 439,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, index, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                    lineNumber: 437,\n                                                                    columnNumber: 25\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                            lineNumber: 435,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                    lineNumber: 430,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.section, {\n                                                    variants: itemVariants,\n                                                    className: \"lg:hidden bg-white rounded-2xl p-6 shadow-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-2xl font-bold mb-2\",\n                                                                children: [\n                                                                    \"₹\",\n                                                                    trip.price_per_person.toLocaleString()\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                lineNumber: 449,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-gray-600 mb-4\",\n                                                                children: \"per person\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                lineNumber: 450,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                size: \"lg\",\n                                                                className: \"w-full bg-gradient-to-r from-blue-600 to-green-600 hover:from-blue-700 hover:to-green-700\",\n                                                                children: \"Book This Trip\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                lineNumber: 451,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                        lineNumber: 448,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                    lineNumber: 447,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                            lineNumber: 148,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"lg:col-span-1 hidden lg:block\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                                variants: itemVariants,\n                                                className: \"sticky top-24 space-y-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-white rounded-2xl p-6 shadow-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-center mb-6\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-3xl font-bold mb-2\",\n                                                                        children: [\n                                                                            \"₹\",\n                                                                            trip.price_per_person.toLocaleString()\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                        lineNumber: 464,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-gray-600\",\n                                                                        children: \"per person\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                        lineNumber: 465,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    trip.commercial_price && trip.commercial_price !== trip.price_per_person && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm text-gray-500 line-through\",\n                                                                        children: [\n                                                                            \"₹\",\n                                                                            trip.commercial_price.toLocaleString()\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                        lineNumber: 467,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                lineNumber: 463,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-3 mb-6\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex justify-between items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-gray-600\",\n                                                                                children: \"Duration\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                                lineNumber: 474,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-medium\",\n                                                                                children: [\n                                                                                    trip.duration_days,\n                                                                                    \" Days\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                                lineNumber: 475,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                        lineNumber: 473,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex justify-between items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-gray-600\",\n                                                                                children: \"Group Size\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                                lineNumber: 478,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-medium\",\n                                                                                children: [\n                                                                                    trip.min_participants,\n                                                                                    \"-\",\n                                                                                    trip.max_participants\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                                lineNumber: 479,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                        lineNumber: 477,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex justify-between items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-gray-600\",\n                                                                                children: \"Difficulty\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                                lineNumber: 482,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-medium capitalize\",\n                                                                                children: trip.difficulty\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                                lineNumber: 483,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                        lineNumber: 481,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    trip.category && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex justify-between items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-gray-600\",\n                                                                                children: \"Category\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                                lineNumber: 487,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-medium\",\n                                                                                children: trip.category\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                                lineNumber: 488,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                        lineNumber: 486,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                lineNumber: 472,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                size: \"lg\",\n                                                                className: \"w-full mb-4 bg-gradient-to-r from-blue-600 to-green-600 hover:from-blue-700 hover:to-green-700\",\n                                                                children: \"Book This Trip\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                lineNumber: 493,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                        variant: \"outline\",\n                                                                        size: \"sm\",\n                                                                        className: \"w-full\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowLeft_Backpack_Calendar_CheckCircle_Clock_CreditCard_Heart_Home_Info_MapPin_Mountain_Package_Phone_Share2_Shield_Star_Train_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                                                className: \"w-4 h-4 mr-2\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                                lineNumber: 500,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            \"Call: +91 78780 05500\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                        lineNumber: 499,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                        variant: \"outline\",\n                                                                        size: \"sm\",\n                                                                        className: \"w-full\",\n                                                                        children: \"WhatsApp Inquiry\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                        lineNumber: 503,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                lineNumber: 498,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-center text-sm text-gray-500 mt-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: \"Contact us for group bookings and custom packages\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                    lineNumber: 509,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                lineNumber: 508,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                        lineNumber: 462,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-white rounded-2xl p-6 shadow-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"font-semibold text-gray-900 mb-4\",\n                                                                children: \"Why Choose This Trip?\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                lineNumber: 515,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center gap-3\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowLeft_Backpack_Calendar_CheckCircle_Clock_CreditCard_Heart_Home_Info_MapPin_Mountain_Package_Phone_Share2_Shield_Star_Train_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                className: \"w-4 h-4 text-green-600\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                                lineNumber: 518,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm text-gray-700\",\n                                                                                children: \"Safety First\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                                lineNumber: 519,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                        lineNumber: 517,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center gap-3\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowLeft_Backpack_Calendar_CheckCircle_Clock_CreditCard_Heart_Home_Info_MapPin_Mountain_Package_Phone_Share2_Shield_Star_Train_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                                className: \"w-4 h-4 text-blue-600\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                                lineNumber: 522,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm text-gray-700\",\n                                                                                children: \"Expert Guides\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                                lineNumber: 523,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                        lineNumber: 521,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center gap-3\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowLeft_Backpack_Calendar_CheckCircle_Clock_CreditCard_Heart_Home_Info_MapPin_Mountain_Package_Phone_Share2_Shield_Star_Train_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                                className: \"w-4 h-4 text-green-600\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                                lineNumber: 526,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm text-gray-700\",\n                                                                                children: \"All Inclusive\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                                lineNumber: 527,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                        lineNumber: 525,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center gap-3\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowLeft_Backpack_Calendar_CheckCircle_Clock_CreditCard_Heart_Home_Info_MapPin_Mountain_Package_Phone_Share2_Shield_Star_Train_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                                                className: \"w-4 h-4 text-purple-600\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                                lineNumber: 530,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm text-gray-700\",\n                                                                                children: \"24/7 Support\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                                lineNumber: 531,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                        lineNumber: 529,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                lineNumber: 516,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                        lineNumber: 514,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                lineNumber: 460,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                            lineNumber: 459,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                    lineNumber: 146,\n                                    columnNumber: 13\n                                }, this),\n                                relatedTrips.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.section, {\n                                    variants: itemVariants,\n                                    className: \"mt-16\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-3xl font-bold mb-8 text-gray-900\",\n                                            children: \"Related Trips\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                            lineNumber: 542,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid md:grid-cols-3 gap-6\",\n                                            children: relatedTrips.map((relatedTrip)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                    href: \"/trips/\".concat(relatedTrip.slug),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-white rounded-lg overflow-hidden shadow-md hover:shadow-lg transition-shadow\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative h-48\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                    src: relatedTrip.featured_image_url || \"/images/fallback-trip.jpg\",\n                                                                    alt: relatedTrip.title,\n                                                                    fill: true,\n                                                                    className: \"object-cover\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                    lineNumber: 548,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                lineNumber: 547,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"font-semibold text-gray-900 mb-2\",\n                                                                        children: relatedTrip.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                        lineNumber: 556,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-gray-600 text-sm mb-3 line-clamp-2\",\n                                                                        children: relatedTrip.description || \"Discover amazing experiences\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                        lineNumber: 557,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex justify-between items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm text-gray-500\",\n                                                                                children: [\n                                                                                    relatedTrip.duration_days,\n                                                                                    \" Days\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                                lineNumber: 561,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-semibold text-primary-600\",\n                                                                                children: [\n                                                                                    \"₹\",\n                                                                                    relatedTrip.price_per_person.toLocaleString()\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                                lineNumber: 562,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                        lineNumber: 560,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                                lineNumber: 555,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                        lineNumber: 546,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, relatedTrip.id, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                                    lineNumber: 545,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                            lineNumber: 543,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                                    lineNumber: 541,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                            lineNumber: 145,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                    lineNumber: 60,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                lineNumber: 59,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Footer__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\trips\\\\TripDetailClient.tsx\",\n                lineNumber: 574,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_c = TripDetailClient;\nvar _c;\n$RefreshReg$(_c, \"TripDetailClient\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/trips/TripDetailClient.tsx\n"));

/***/ })

});