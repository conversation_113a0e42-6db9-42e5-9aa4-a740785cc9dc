import { Metadata } from 'next'
import { Suspense } from 'react'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'
import AdminDashboard from '@/components/admin/AdminDashboard'
import LoadingSpinner from '@/components/ui/LoadingSpinner'
import AuthGuard from '@/components/auth/AuthGuard'

export const metadata: Metadata = {
  title: 'Admin Dashboard - Positive7 Educational Tours',
  description: 'Administrative dashboard for managing bookings, analytics, and business insights.',
  keywords: 'admin, dashboard, analytics, bookings, management, Positive7'
}

export default function AdminDashboardPage() {
  return (
    <AuthGuard requireAuth={true} requireAdmin={true} redirectTo="/auth/login">
      <Header />
      <main className="min-h-screen bg-gray-50">
        <Suspense fallback={
          <div className="flex items-center justify-center min-h-screen">
            <LoadingSpinner size="lg" />
          </div>
        }>
          <AdminDashboard />
        </Suspense>
      </main>
      <Footer />
    </AuthGuard>
  )
}
