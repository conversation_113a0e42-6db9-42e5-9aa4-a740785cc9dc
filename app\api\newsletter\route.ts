import { NextRequest, NextResponse } from 'next/server';

// GET /api/newsletter - Get newsletter subscriptions (Admin only)
export async function GET(request: NextRequest) {
  try {
    // Authentication removed - return empty array for now
    return NextResponse.json([]);
  } catch (error) {
    console.error('Error in GET /api/newsletter:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST /api/newsletter - Subscribe to newsletter
export async function POST(request: NextRequest) {
  try {
    const { email, name } = await request.json();

    if (!email) {
      return NextResponse.json(
        { error: 'Email is required' },
        { status: 400 }
      );
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { error: 'Invalid email format' },
        { status: 400 }
      );
    }

    // For now, just return success without saving to database
    return NextResponse.json({
      message: 'Successfully subscribed to newsletter!',
      data: { id: 'temp-' + Date.now(), email, name, is_active: true }
    }, { status: 201 });
  } catch (error) {
    console.error('Error in POST /api/newsletter:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// DELETE /api/newsletter - Unsubscribe from newsletter
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const email = searchParams.get('email');

    if (!email) {
      return NextResponse.json(
        { error: 'Email is required' },
        { status: 400 }
      );
    }

    // For now, just return success without database operations
    return NextResponse.json({
      message: 'Successfully unsubscribed from newsletter',
    });
  } catch (error) {
    console.error('Error in DELETE /api/newsletter:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
