"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/contact/page",{

/***/ "(app-pages-browser)/./components/layout/Footer.tsx":
/*!**************************************!*\
  !*** ./components/layout/Footer.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Footer; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Facebook_Heart_Instagram_Loader2_Mail_MapPin_MessageCircle_Phone_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,CheckCircle,Clock,Facebook,Heart,Instagram,Loader2,Mail,MapPin,MessageCircle,Phone,Youtube!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/facebook.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Facebook_Heart_Instagram_Loader2_Mail_MapPin_MessageCircle_Phone_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,CheckCircle,Clock,Facebook,Heart,Instagram,Loader2,Mail,MapPin,MessageCircle,Phone,Youtube!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/instagram.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Facebook_Heart_Instagram_Loader2_Mail_MapPin_MessageCircle_Phone_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,CheckCircle,Clock,Facebook,Heart,Instagram,Loader2,Mail,MapPin,MessageCircle,Phone,Youtube!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/youtube.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Facebook_Heart_Instagram_Loader2_Mail_MapPin_MessageCircle_Phone_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,CheckCircle,Clock,Facebook,Heart,Instagram,Loader2,Mail,MapPin,MessageCircle,Phone,Youtube!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Facebook_Heart_Instagram_Loader2_Mail_MapPin_MessageCircle_Phone_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,CheckCircle,Clock,Facebook,Heart,Instagram,Loader2,Mail,MapPin,MessageCircle,Phone,Youtube!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Facebook_Heart_Instagram_Loader2_Mail_MapPin_MessageCircle_Phone_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,CheckCircle,Clock,Facebook,Heart,Instagram,Loader2,Mail,MapPin,MessageCircle,Phone,Youtube!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Facebook_Heart_Instagram_Loader2_Mail_MapPin_MessageCircle_Phone_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,CheckCircle,Clock,Facebook,Heart,Instagram,Loader2,Mail,MapPin,MessageCircle,Phone,Youtube!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Facebook_Heart_Instagram_Loader2_Mail_MapPin_MessageCircle_Phone_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,CheckCircle,Clock,Facebook,Heart,Instagram,Loader2,Mail,MapPin,MessageCircle,Phone,Youtube!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Facebook_Heart_Instagram_Loader2_Mail_MapPin_MessageCircle_Phone_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,CheckCircle,Clock,Facebook,Heart,Instagram,Loader2,Mail,MapPin,MessageCircle,Phone,Youtube!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Facebook_Heart_Instagram_Loader2_Mail_MapPin_MessageCircle_Phone_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,CheckCircle,Clock,Facebook,Heart,Instagram,Loader2,Mail,MapPin,MessageCircle,Phone,Youtube!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Facebook_Heart_Instagram_Loader2_Mail_MapPin_MessageCircle_Phone_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,CheckCircle,Clock,Facebook,Heart,Instagram,Loader2,Mail,MapPin,MessageCircle,Phone,Youtube!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Facebook_Heart_Instagram_Loader2_Mail_MapPin_MessageCircle_Phone_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,CheckCircle,Clock,Facebook,Heart,Instagram,Loader2,Mail,MapPin,MessageCircle,Phone,Youtube!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Facebook_Heart_Instagram_Loader2_Mail_MapPin_MessageCircle_Phone_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,CheckCircle,Clock,Facebook,Heart,Instagram,Loader2,Mail,MapPin,MessageCircle,Phone,Youtube!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _lib_constants__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/constants */ \"(app-pages-browser)/./lib/constants.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction Footer() {\n    _s();\n    const currentYear = new Date().getFullYear();\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [submitStatus, setSubmitStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"idle\");\n    const [errorMessage, setErrorMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const handleNewsletterSubmit = async (e)=>{\n        e.preventDefault();\n        if (!email.trim()) return;\n        setIsSubmitting(true);\n        setSubmitStatus(\"idle\");\n        setErrorMessage(\"\");\n        try {\n            const response = await fetch(\"/api/newsletter\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    email: email.trim()\n                })\n            });\n            const data = await response.json();\n            if (response.ok) {\n                setSubmitStatus(\"success\");\n                setEmail(\"\");\n            } else {\n                setSubmitStatus(\"error\");\n                setErrorMessage(data.error || \"Failed to subscribe\");\n            }\n        } catch (error) {\n            setSubmitStatus(\"error\");\n            setErrorMessage(\"Network error. Please try again.\");\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    const containerVariants = {\n        hidden: {\n            opacity: 0\n        },\n        visible: {\n            opacity: 1,\n            transition: {\n                staggerChildren: 0.1\n            }\n        }\n    };\n    const itemVariants = {\n        hidden: {\n            opacity: 0,\n            y: 20\n        },\n        visible: {\n            opacity: 1,\n            y: 0,\n            transition: {\n                duration: 0.5\n            }\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"bg-gray-900 text-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                variants: containerVariants,\n                initial: \"hidden\",\n                whileInView: \"visible\",\n                viewport: {\n                    once: true\n                },\n                className: \"container-custom py-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                            variants: itemVariants,\n                            className: \"lg:col-span-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/\",\n                                    className: \"flex items-center mb-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative h-16 w-16\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                            src: \"/images/positive7-logo.png\",\n                                            alt: _lib_constants__WEBPACK_IMPORTED_MODULE_4__.COMPANY_INFO.name,\n                                            fill: true,\n                                            className: \"object-contain\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 101,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Footer.tsx\",\n                                        lineNumber: 100,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 99,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-300 mb-6 leading-relaxed\",\n                                    children: \"Gujarat Tourism affiliated outbound experiential learning company organizing educational trips, student tours, CAS Projects, adventure camps & workshops.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 110,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: _lib_constants__WEBPACK_IMPORTED_MODULE_4__.SOCIAL_LINKS.facebook,\n                                            target: \"_blank\",\n                                            rel: \"noopener noreferrer\",\n                                            className: \"p-2 bg-gray-800 rounded-full hover:bg-primary-600 transition-colors group\",\n                                            \"aria-label\": \"Facebook\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Facebook_Heart_Instagram_Loader2_Mail_MapPin_MessageCircle_Phone_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"h-5 w-5 group-hover:scale-110 transition-transform\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 123,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 116,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: _lib_constants__WEBPACK_IMPORTED_MODULE_4__.SOCIAL_LINKS.instagram,\n                                            target: \"_blank\",\n                                            rel: \"noopener noreferrer\",\n                                            className: \"p-2 bg-gray-800 rounded-full hover:bg-gradient-to-r hover:from-purple-500 hover:to-pink-500 transition-all group\",\n                                            \"aria-label\": \"Instagram\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Facebook_Heart_Instagram_Loader2_Mail_MapPin_MessageCircle_Phone_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"h-5 w-5 group-hover:scale-110 transition-transform\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 132,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 125,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: _lib_constants__WEBPACK_IMPORTED_MODULE_4__.SOCIAL_LINKS.youtube,\n                                            target: \"_blank\",\n                                            rel: \"noopener noreferrer\",\n                                            className: \"p-2 bg-gray-800 rounded-full hover:bg-red-600 transition-colors group\",\n                                            \"aria-label\": \"YouTube\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Facebook_Heart_Instagram_Loader2_Mail_MapPin_MessageCircle_Phone_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"h-5 w-5 group-hover:scale-110 transition-transform\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 141,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 134,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: _lib_constants__WEBPACK_IMPORTED_MODULE_4__.SOCIAL_LINKS.whatsapp,\n                                            target: \"_blank\",\n                                            rel: \"noopener noreferrer\",\n                                            className: \"p-2 bg-gray-800 rounded-full hover:bg-green-600 transition-colors group\",\n                                            \"aria-label\": \"WhatsApp\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Facebook_Heart_Instagram_Loader2_Mail_MapPin_MessageCircle_Phone_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-5 w-5 group-hover:scale-110 transition-transform\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 150,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 143,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 115,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Footer.tsx\",\n                            lineNumber: 98,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                            variants: itemVariants,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-lg font-semibold mb-6 text-white\",\n                                    children: \"Quick Links\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 157,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-3\",\n                                    children: _lib_constants__WEBPACK_IMPORTED_MODULE_4__.QUICK_LINKS.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: link.href,\n                                                className: \"text-gray-300 hover:text-secondary-400 transition-colors flex items-center group\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Facebook_Heart_Instagram_Loader2_Mail_MapPin_MessageCircle_Phone_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2 opacity-0 group-hover:opacity-100 transition-opacity\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Footer.tsx\",\n                                                        lineNumber: 165,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"group-hover:translate-x-1 transition-transform\",\n                                                        children: link.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Footer.tsx\",\n                                                        lineNumber: 166,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 161,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, link.name, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 160,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 158,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Footer.tsx\",\n                            lineNumber: 156,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                            variants: itemVariants,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-lg font-semibold mb-6 text-white\",\n                                    children: \"Contact Info\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 179,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Facebook_Heart_Instagram_Loader2_Mail_MapPin_MessageCircle_Phone_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"h-5 w-5 text-secondary-400 mt-1 flex-shrink-0\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 182,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-300 text-sm leading-relaxed\",\n                                                        children: [\n                                                            \"904, Shivalik Highstreet,\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Footer.tsx\",\n                                                                lineNumber: 185,\n                                                                columnNumber: 46\n                                                            }, this),\n                                                            \"B/S ITC Narmada Hotel,\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Footer.tsx\",\n                                                                lineNumber: 186,\n                                                                columnNumber: 43\n                                                            }, this),\n                                                            \"Mansi-Keshavbaug Road,\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Footer.tsx\",\n                                                                lineNumber: 187,\n                                                                columnNumber: 43\n                                                            }, this),\n                                                            \"Vastrapur, Ahmedabad-380015\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Footer.tsx\",\n                                                        lineNumber: 184,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 183,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 181,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Facebook_Heart_Instagram_Loader2_Mail_MapPin_MessageCircle_Phone_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"h-5 w-5 text-secondary-400 flex-shrink-0\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 194,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                            href: \"tel:\".concat(_lib_constants__WEBPACK_IMPORTED_MODULE_4__.COMPANY_INFO.phone),\n                                                            className: \"text-gray-300 hover:text-secondary-400 transition-colors\",\n                                                            children: _lib_constants__WEBPACK_IMPORTED_MODULE_4__.COMPANY_INFO.phone\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Footer.tsx\",\n                                                            lineNumber: 196,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Footer.tsx\",\n                                                            lineNumber: 202,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                            href: \"tel:\".concat(_lib_constants__WEBPACK_IMPORTED_MODULE_4__.COMPANY_INFO.alternatePhone),\n                                                            className: \"text-gray-300 hover:text-secondary-400 transition-colors text-sm\",\n                                                            children: _lib_constants__WEBPACK_IMPORTED_MODULE_4__.COMPANY_INFO.alternatePhone\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Footer.tsx\",\n                                                            lineNumber: 203,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 195,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 193,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Facebook_Heart_Instagram_Loader2_Mail_MapPin_MessageCircle_Phone_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"h-5 w-5 text-secondary-400 flex-shrink-0\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 213,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"mailto:\".concat(_lib_constants__WEBPACK_IMPORTED_MODULE_4__.COMPANY_INFO.email),\n                                                    className: \"text-gray-300 hover:text-secondary-400 transition-colors\",\n                                                    children: _lib_constants__WEBPACK_IMPORTED_MODULE_4__.COMPANY_INFO.email\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 214,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 212,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Facebook_Heart_Instagram_Loader2_Mail_MapPin_MessageCircle_Phone_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"h-5 w-5 text-secondary-400 flex-shrink-0\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 223,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-gray-300 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: \"Mon - Sat: 9:00 AM - 7:00 PM\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Footer.tsx\",\n                                                            lineNumber: 225,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: \"Sunday: 10:00 AM - 5:00 PM\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Footer.tsx\",\n                                                            lineNumber: 226,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 224,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 222,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 180,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Footer.tsx\",\n                            lineNumber: 178,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Footer.tsx\",\n                    lineNumber: 96,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Footer.tsx\",\n                lineNumber: 89,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                variants: itemVariants,\n                initial: \"hidden\",\n                whileInView: \"visible\",\n                viewport: {\n                    once: true\n                },\n                className: \"border-t border-gray-800\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container-custom py-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-lg font-semibold mb-2 text-white\",\n                                children: \"Stay Updated with Our Latest Adventures\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Footer.tsx\",\n                                lineNumber: 244,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400 mb-6\",\n                                children: \"Subscribe to our newsletter for travel tips, destination guides, and exclusive offers.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Footer.tsx\",\n                                lineNumber: 247,\n                                columnNumber: 13\n                            }, this),\n                            submitStatus === \"success\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4 p-3 bg-green-900/50 border border-green-700 rounded-lg flex items-center justify-center space-x-2 max-w-md mx-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Facebook_Heart_Instagram_Loader2_Mail_MapPin_MessageCircle_Phone_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-5 w-5 text-green-400 flex-shrink-0\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Footer.tsx\",\n                                        lineNumber: 254,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-green-300 text-sm\",\n                                        children: \"Successfully subscribed to newsletter!\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Footer.tsx\",\n                                        lineNumber: 255,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Footer.tsx\",\n                                lineNumber: 253,\n                                columnNumber: 15\n                            }, this),\n                            submitStatus === \"error\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4 p-3 bg-red-900/50 border border-red-700 rounded-lg flex items-center justify-center space-x-2 max-w-md mx-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Facebook_Heart_Instagram_Loader2_Mail_MapPin_MessageCircle_Phone_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"h-5 w-5 text-red-400 flex-shrink-0\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Footer.tsx\",\n                                        lineNumber: 261,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-red-300 text-sm\",\n                                        children: errorMessage\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Footer.tsx\",\n                                        lineNumber: 262,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Footer.tsx\",\n                                lineNumber: 260,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handleNewsletterSubmit,\n                                className: \"max-w-md mx-auto flex\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"email\",\n                                        value: email,\n                                        onChange: (e)=>setEmail(e.target.value),\n                                        placeholder: \"Enter your email\",\n                                        required: true,\n                                        disabled: isSubmitting,\n                                        className: \"flex-1 px-4 py-2 bg-gray-800 border border-gray-700 rounded-l-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent disabled:opacity-50\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Footer.tsx\",\n                                        lineNumber: 267,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        disabled: isSubmitting || !email.trim(),\n                                        className: \"px-6 py-2 bg-primary-600 text-white rounded-r-md hover:bg-primary-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center\",\n                                        children: isSubmitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Facebook_Heart_Instagram_Loader2_Mail_MapPin_MessageCircle_Phone_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"h-4 w-4 animate-spin mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 283,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"hidden sm:inline\",\n                                                    children: \"Subscribing...\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 284,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true) : \"Subscribe\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Footer.tsx\",\n                                        lineNumber: 276,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Footer.tsx\",\n                                lineNumber: 266,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Footer.tsx\",\n                        lineNumber: 243,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Footer.tsx\",\n                    lineNumber: 242,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Footer.tsx\",\n                lineNumber: 235,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-t border-gray-800 bg-gray-950\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container-custom py-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-gray-400 text-sm text-center md:text-left\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: [\n                                        \"\\xa9 \",\n                                        currentYear,\n                                        \" \",\n                                        _lib_constants__WEBPACK_IMPORTED_MODULE_4__.COMPANY_INFO.name,\n                                        \". All rights reserved.\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"hidden sm:inline\",\n                                            children: \" | Gujarat Tourism Affiliated\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 302,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 300,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Footer.tsx\",\n                                lineNumber: 299,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-6 text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/contact\",\n                                        className: \"text-gray-400 hover:text-secondary-400 transition-colors\",\n                                        children: \"Contact Us\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Footer.tsx\",\n                                        lineNumber: 307,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/about\",\n                                        className: \"text-gray-400 hover:text-secondary-400 transition-colors\",\n                                        children: \"About Us\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Footer.tsx\",\n                                        lineNumber: 313,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center text-gray-400\",\n                                        children: [\n                                            \"Made with \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Facebook_Heart_Instagram_Loader2_Mail_MapPin_MessageCircle_Phone_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"h-4 w-4 mx-1 text-red-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 320,\n                                                columnNumber: 27\n                                            }, this),\n                                            \" in India\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Footer.tsx\",\n                                        lineNumber: 319,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Footer.tsx\",\n                                lineNumber: 306,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Footer.tsx\",\n                        lineNumber: 298,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Footer.tsx\",\n                    lineNumber: 297,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Footer.tsx\",\n                lineNumber: 296,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\layout\\\\Footer.tsx\",\n        lineNumber: 87,\n        columnNumber: 5\n    }, this);\n}\n_s(Footer, \"mfyh8yHuaViqlF1Gxhucd9j4jkQ=\");\n_c = Footer;\nvar _c;\n$RefreshReg$(_c, \"Footer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/layout/Footer.tsx\n"));

/***/ })

});