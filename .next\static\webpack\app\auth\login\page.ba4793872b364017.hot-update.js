"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/auth/login/page",{

/***/ "(app-pages-browser)/./contexts/AuthContext.tsx":
/*!**********************************!*\
  !*** ./contexts/AuthContext.tsx ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: function() { return /* binding */ AuthProvider; },\n/* harmony export */   useAuth: function() { return /* binding */ useAuth; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_supabase_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/supabase-client */ \"(app-pages-browser)/./lib/supabase-client.ts\");\n/* __next_internal_client_entry_do_not_use__ useAuth,AuthProvider auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction useAuth() {\n    _s();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n}\n_s(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nfunction AuthProvider(param) {\n    let { children } = param;\n    _s1();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [session, setSession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const supabase = (0,_lib_supabase_client__WEBPACK_IMPORTED_MODULE_2__.createClientSupabase)();\n    // Fetch user profile from database\n    const fetchUserProfile = async (userId)=>{\n        try {\n            const { data, error } = await supabase.from(\"users\").select(\"*\").eq(\"id\", userId).single();\n            if (error) {\n                console.error(\"AuthContext: Error fetching user profile:\", error);\n                return null;\n            }\n            return data;\n        } catch (error) {\n            console.error(\"AuthContext: Error fetching user profile:\", error);\n            return null;\n        }\n    };\n    // Initialize auth state\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        console.log(\"AuthContext: useEffect triggered for auth initialization\");\n        const initializeAuth = async ()=>{\n            console.log(\"AuthContext: Starting auth initialization\");\n            try {\n                // Use getUser() for secure authentication instead of getSession()\n                console.log(\"AuthContext: Calling supabase.auth.getUser()\");\n                const { data: { user: authUser }, error } = await supabase.auth.getUser();\n                console.log(\"AuthContext: getUser() result - user:\", !!authUser, \"error:\", error);\n                if (error) {\n                    console.error(\"AuthContext: Error getting user:\", error);\n                    setLoading(false);\n                    return;\n                }\n                if (authUser) {\n                    console.log(\"AuthContext: Found authenticated user:\", authUser.id);\n                    // Get the session for other purposes\n                    const { data: { session } } = await supabase.auth.getSession();\n                    console.log(\"AuthContext: Got session:\", !!session);\n                    setSession(session);\n                    // Fetch user profile from our database\n                    console.log(\"AuthContext: Fetching user profile from database\");\n                    const userProfile = await fetchUserProfile(authUser.id);\n                    console.log(\"AuthContext: User profile fetched:\", !!userProfile);\n                    setUser(userProfile);\n                } else {\n                    console.log(\"AuthContext: No authenticated user found\");\n                }\n            } catch (error) {\n                console.error(\"AuthContext: Error initializing auth:\", error);\n            } finally{\n                console.log(\"AuthContext: Setting loading to false\");\n                setLoading(false);\n            }\n        };\n        initializeAuth();\n        // Listen for auth changes\n        const { data: { subscription } } = supabase.auth.onAuthStateChange(async (event, session)=>{\n            var _session_user;\n            console.log(\"AuthContext: Auth state changed:\", event, session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.id);\n            setSession(session);\n            if (session && event !== \"SIGNED_OUT\") {\n                console.log(\"AuthContext: Fetching user profile for:\", session.user.id);\n                // Verify the user is still authenticated\n                const { data: { user: authUser }, error } = await supabase.auth.getUser();\n                if (error || !authUser) {\n                    console.error(\"AuthContext: User verification failed:\", error);\n                    setUser(null);\n                    setSession(null);\n                    return;\n                }\n                const userProfile = await fetchUserProfile(authUser.id);\n                setUser(userProfile);\n            } else {\n                console.log(\"AuthContext: Clearing user data\");\n                setUser(null);\n            }\n            // Force a small delay to ensure state is updated\n            setTimeout(()=>{\n                console.log(\"AuthContext: State update complete - user:\", !!(session === null || session === void 0 ? void 0 : session.user), \"loading:\", false);\n            }, 100);\n        });\n        return ()=>subscription.unsubscribe();\n    }, []);\n    // Sign up function\n    const signUp = async (email, password, userData)=>{\n        try {\n            console.log(\"AuthContext: Attempting signup\");\n            const { data, error } = await supabase.auth.signUp({\n                email,\n                password,\n                options: {\n                    data: {\n                        full_name: userData.full_name,\n                        phone: userData.phone\n                    }\n                }\n            });\n            if (error) {\n                console.error(\"AuthContext: Signup error:\", error);\n                return {\n                    data: null,\n                    error: error.message\n                };\n            }\n            if (!data.user) {\n                return {\n                    data: null,\n                    error: \"Failed to create user account\"\n                };\n            }\n            // Create user profile in the database\n            const { error: profileError } = await supabase.from(\"users\").insert({\n                id: data.user.id,\n                email: data.user.email,\n                full_name: userData.full_name || \"\",\n                phone: userData.phone || \"\",\n                role: \"customer\",\n                created_at: new Date().toISOString(),\n                updated_at: new Date().toISOString()\n            });\n            if (profileError) {\n                console.error(\"AuthContext: Profile creation error:\", profileError);\n                return {\n                    data: null,\n                    error: \"Failed to create user profile\"\n                };\n            }\n            console.log(\"AuthContext: Signup successful\");\n            return {\n                data: data.user,\n                error: null\n            };\n        } catch (error) {\n            console.error(\"AuthContext: Signup network error:\", error);\n            return {\n                data: null,\n                error: \"Network error. Please try again.\"\n            };\n        }\n    };\n    // Sign in function\n    const signIn = async (email, password)=>{\n        try {\n            console.log(\"AuthContext: Attempting signin\");\n            const { data, error } = await supabase.auth.signInWithPassword({\n                email,\n                password\n            });\n            if (error) {\n                console.error(\"AuthContext: Signin error:\", error);\n                return {\n                    data: null,\n                    error: error.message\n                };\n            }\n            console.log(\"AuthContext: Signin successful\");\n            // The auth state change listener will handle updating user and session\n            return {\n                data: data.user,\n                error: null\n            };\n        } catch (error) {\n            console.error(\"AuthContext: Signin network error:\", error);\n            return {\n                data: null,\n                error: \"Network error. Please try again.\"\n            };\n        }\n    };\n    // Google sign in function\n    const signInWithGoogle = async ()=>{\n        try {\n            console.log(\"AuthContext: Attempting Google signin\");\n            const response = await fetch(\"/api/auth/google\", {\n                method: \"POST\",\n                credentials: \"include\"\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                console.error(\"AuthContext: Google signin error:\", data.error);\n                return {\n                    data: null,\n                    error: data.error\n                };\n            }\n            console.log(\"AuthContext: Google OAuth URL generated\");\n            // Redirect to Google OAuth\n            window.location.href = data.url;\n            return {\n                data: data,\n                error: null\n            };\n        } catch (error) {\n            console.error(\"AuthContext: Google signin network error:\", error);\n            return {\n                data: null,\n                error: \"Network error. Please try again.\"\n            };\n        }\n    };\n    // Sign out function\n    const signOut = async ()=>{\n        try {\n            console.log(\"AuthContext: Attempting signout\");\n            const { error } = await supabase.auth.signOut();\n            if (error) {\n                console.error(\"AuthContext: Signout error:\", error);\n                return {\n                    error: error.message\n                };\n            }\n            console.log(\"AuthContext: Signout successful\");\n            // The auth state change listener will handle clearing user and session\n            return {\n                error: null\n            };\n        } catch (error) {\n            console.error(\"AuthContext: Signout network error:\", error);\n            return {\n                error: \"Network error. Please try again.\"\n            };\n        }\n    };\n    // Reset password function\n    const resetPassword = async (email)=>{\n        try {\n            console.log(\"AuthContext: Reset password not implemented via API yet\");\n            return {\n                error: \"Password reset functionality will be available soon\"\n            };\n        } catch (error) {\n            return {\n                error: error.message\n            };\n        }\n    };\n    // Update profile function\n    const updateProfile = async (updates)=>{\n        if (!user) {\n            return {\n                data: null,\n                error: \"No user logged in\"\n            };\n        }\n        try {\n            console.log(\"AuthContext: Update profile not implemented via API yet\");\n            return {\n                data: null,\n                error: \"Profile update functionality will be available soon\"\n            };\n        } catch (error) {\n            return {\n                data: null,\n                error: error.message\n            };\n        }\n    };\n    // Refresh user data\n    const refreshUser = async ()=>{\n        console.log(\"AuthContext: Refreshing user data\");\n        if (session) {\n            const userProfile = await fetchUserProfile(session.user.id);\n            setUser(userProfile);\n        }\n    };\n    const value = {\n        user,\n        session,\n        loading,\n        signUp,\n        signIn,\n        signInWithGoogle,\n        signOut,\n        resetPassword,\n        updateProfile,\n        refreshUser\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 314,\n        columnNumber: 5\n    }, this);\n}\n_s1(AuthProvider, \"sIDOCMze9iVqwxkgWIhOu8vskSI=\");\n_c = AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./contexts/AuthContext.tsx\n"));

/***/ })

});