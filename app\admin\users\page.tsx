import { Metadata } from 'next'
import { Suspense } from 'react'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'
import UserManagement from '@/components/admin/UserManagement'
import LoadingSpinner from '@/components/ui/LoadingSpinner'

export const metadata: Metadata = {
  title: 'User Management - Admin Dashboard | Positive7 Educational Tours',
  description: 'Manage user accounts, roles, and permissions for Positive7 Educational Tours.',
  keywords: 'admin, user management, accounts, roles, permissions, Positive7'
}

export default function AdminUsersPage() {
  return (
    <>
      <Header />
      <main className="min-h-screen bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <Suspense fallback={
            <div className="flex items-center justify-center min-h-screen">
              <LoadingSpinner size="lg" />
            </div>
          }>
            <UserManagement />
          </Suspense>
        </div>
      </main>
      <Footer />
    </>
  )
}
