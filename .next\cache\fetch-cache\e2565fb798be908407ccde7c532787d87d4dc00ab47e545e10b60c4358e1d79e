{"kind": "FETCH", "data": {"headers": {"alt-svc": "h3=\":443\"; ma=86400", "cf-cache-status": "DYNAMIC", "cf-ray": "947573258ac5401f-BOM", "connection": "keep-alive", "content-encoding": "gzip", "content-location": "/trips?is_active=eq.true&order=is_featured.desc%2Ccreated_at.desc&select=id%2Ctitle%2Cslug%2Cdescription%2Cdestination%2Cduration_days%2Cprice_per_person%2Cdifficulty%2Cfeatured_image_url%2Cis_featured%2Cis_active", "content-profile": "public", "content-range": "0-12/*", "content-type": "application/json; charset=utf-8", "date": "Thu, 29 May 2025 10:53:45 GMT", "sb-gateway-version": "1", "sb-project-ref": "soaoag<PERSON><PERSON><PERSON>", "server": "cloudflare", "set-cookie": "__cf_bm=.fDsjQjaZPxhPmyIwNd13faLr6wrPMaMFg4n7c_7DCE-1748516025-*******-8YK0xxg9hH5fRPMDMMZpF_c880G6gIHEgWhZ9M7W.Ha8adPywDBwsQQB86rCEoK.Bn3PTs3kT9kcjM7WaZXDoK_XPt9PxRiq931SRcJCmww; path=/; expires=Thu, 29-May-25 11:23:45 GMT; domain=.supabase.co; HttpOnly; Secure; SameSite=None", "strict-transport-security": "max-age=31536000; includeSubDomains; preload", "transfer-encoding": "chunked", "vary": "Accept-Encoding", "x-content-type-options": "nosniff", "x-envoy-attempt-count": "1", "x-envoy-upstream-service-time": "4"}, "body": "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", "status": 200, "url": "https://soaoagcuubtzojytoati.supabase.co/rest/v1/trips?select=id%2Ctitle%2Cslug%2Cdescription%2Cdestination%2Cduration_days%2Cprice_per_person%2Cdifficulty%2Cfeatured_image_url%2Cis_featured%2Cis_active&is_active=eq.true&order=is_featured.desc%2Ccreated_at.desc"}, "revalidate": 31536000, "tags": []}