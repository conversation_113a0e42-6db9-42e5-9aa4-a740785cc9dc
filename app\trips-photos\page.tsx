import { Metadata } from 'next'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'
import TripsPhotosClient from '@/components/trips-photos/TripsPhotosClient'

export const metadata: Metadata = {
  title: 'Trips Photos - Positive7 Educational Tours',
  description: 'Browse and download photos from our recent educational trips and adventures. Relive the memories and experiences from our amazing journeys.',
  keywords: 'trip photos, educational tour photos, student trip memories, Positive7 gallery, travel photos'
}

// Sample trip photo albums data (this would come from database in real implementation)
const tripPhotoAlbums = [
  {
    id: '1',
    title: 'Anand Niketan Sarkhej Campus Trip to Polo Forest',
    date: '24th April – 25th April',
    coverImage: '/images/trips/WhatsApp-Image-2025-04-25-at-13.45.02-1.jpg',
    photoCount: 45,
    downloadLink: 'https://photos.app.goo.gl/3cS1Gujj1f2iDXn46',
    location: 'Polo Forest, Gujarat',
    participants: 'Anand Niketan Sarkhej Campus Students'
  },
  {
    id: '2',
    title: 'Anand <PERSON>j Campus Trip to Narara 2025',
    date: '23rd April – 25th April',
    coverImage: '/images/trips/WhatsApp-Image-2025-04-23-at-14.53.49.jpg',
    photoCount: 62,
    downloadLink: 'https://photos.app.goo.gl/wFMAR2GgecYeEyJ69',
    location: 'Narara, Gujarat',
    participants: 'Anand Niketan Sarkhej Campus Students'
  },
  {
    id: '3',
    title: 'Anand Niketan Sarkhej Campus Trip to Navsari 2025',
    date: '22nd April – 24th April',
    coverImage: '/images/trips/WhatsApp-Image-2025-04-23-at-10.47.16-2.jpg',
    photoCount: 38,
    downloadLink: 'https://photos.app.goo.gl/NxWyXacoXGbTsmDL7',
    location: 'Navsari, Gujarat',
    participants: 'Anand Niketan Sarkhej Campus Students'
  },
  {
    id: '4',
    title: 'Manali Adventure Trip 2024',
    date: '15th March – 22nd March',
    coverImage: '/images/trips/Manali-River.jpg',
    photoCount: 89,
    downloadLink: '#',
    location: 'Manali, Himachal Pradesh',
    participants: 'Various Schools'
  },
  {
    id: '5',
    title: 'Rishikesh Yoga & Adventure Camp',
    date: '10th February – 16th February',
    coverImage: '/images/trips/temple-rishikesh-india-1113888.jpg',
    photoCount: 67,
    downloadLink: '#',
    location: 'Rishikesh, Uttarakhand',
    participants: 'Multiple Schools'
  },
  {
    id: '6',
    title: 'Tirthan Valley Nature Expedition',
    date: '5th January – 13th January',
    coverImage: '/images/trips/Tirthan-Valley-Himalayan-1-scaled-qi2e45mk4qblqepqjw0jqlabp95dlyg00al0h5hit8.webp',
    photoCount: 54,
    downloadLink: '#',
    location: 'Tirthan Valley, Himachal Pradesh',
    participants: 'Nature Enthusiasts Group'
  }
]

export default function TripsPhotosPage() {
  return (
    <>
      <Header />
      <main className="flex-1">
        <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50">
          <div className="max-w-7xl mx-auto px-4 py-8">
            <TripsPhotosClient albums={tripPhotoAlbums} />
          </div>
        </div>
      </main>
      <Footer />
    </>
  )
}
