{"kind": "FETCH", "data": {"headers": {"alt-svc": "h3=\":443\"; ma=86400", "cf-cache-status": "DYNAMIC", "cf-ray": "94757982ecd30027-BOM", "connection": "keep-alive", "content-encoding": "gzip", "content-location": "/blog_posts?is_published=eq.true&limit=20&order=published_at.desc&select=id%2Ctitle%2Cslug%2Cexcerpt%2Ccontent%2Cfeatured_image_url%2Ccategory%2Ctags%2Cpublished_at%2Ccreated_at", "content-profile": "public", "content-range": "0-2/*", "content-type": "application/json; charset=utf-8", "date": "Thu, 29 May 2025 10:58:06 GMT", "sb-gateway-version": "1", "sb-project-ref": "soaoag<PERSON><PERSON><PERSON>", "server": "cloudflare", "set-cookie": "__cf_bm=TmYlEnor7F3siwQ9gdTJ_vO_h9eSYFJSkmvULmav8ek-1748516286-*******-JkvS1C5Zkna0rNvIsPcHUlJrmFjg.zoH1B8I_jDzcJfr1DvH_j1j5XYgEB6Cz4E5FamUNc1J6OEEV6E2DDx8e0kMCaJGI6nWRhTC3x02ibM; path=/; expires=Thu, 29-May-25 11:28:06 GMT; domain=.supabase.co; HttpOnly; Secure; SameSite=None", "strict-transport-security": "max-age=31536000; includeSubDomains; preload", "transfer-encoding": "chunked", "vary": "Accept-Encoding", "x-content-type-options": "nosniff", "x-envoy-attempt-count": "1", "x-envoy-upstream-service-time": "17"}, "body": "************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "status": 200, "url": "https://soaoagcuubtzojytoati.supabase.co/rest/v1/blog_posts?select=id%2Ctitle%2Cslug%2Cexcerpt%2Ccontent%2Cfeatured_image_url%2Ccategory%2Ctags%2Cpublished_at%2Ccreated_at&is_published=eq.true&order=published_at.desc&limit=20"}, "revalidate": 31536000, "tags": []}