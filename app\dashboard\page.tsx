import { Metadata } from 'next'
import { Suspense } from 'react'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'
import UserDashboard from '@/components/dashboard/UserDashboard'
import LoadingSpinner from '@/components/ui/LoadingSpinner'
import AuthGuard from '@/components/auth/AuthGuard'

export const metadata: Metadata = {
  title: 'My Dashboard - Positive7 Educational Tours',
  description: 'Manage your bookings, preferences, and trip history with Positive7 Educational Tours.',
  keywords: 'dashboard, bookings, trip history, preferences, Positive7, educational tours'
}

export default function DashboardPage() {
  return (
    <AuthGuard requireAuth={true} redirectTo="/auth/login">
      <Header />
      <main className="min-h-screen bg-gray-50">
        <Suspense fallback={
          <div className="flex items-center justify-center min-h-screen">
            <LoadingSpinner size="lg" />
          </div>
        }>
          <UserDashboard />
        </Suspense>
      </main>
      <Footer />
    </AuthGuard>
  )
}
