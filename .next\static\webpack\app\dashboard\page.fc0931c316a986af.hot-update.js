"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./contexts/AuthContext.tsx":
/*!**********************************!*\
  !*** ./contexts/AuthContext.tsx ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: function() { return /* binding */ AuthProvider; },\n/* harmony export */   useAuth: function() { return /* binding */ useAuth; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_supabase_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/supabase-client */ \"(app-pages-browser)/./lib/supabase-client.ts\");\n/* __next_internal_client_entry_do_not_use__ useAuth,AuthProvider auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction useAuth() {\n    _s();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n}\n_s(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nfunction AuthProvider(param) {\n    let { children } = param;\n    _s1();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [session, setSession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const supabase = (0,_lib_supabase_client__WEBPACK_IMPORTED_MODULE_2__.createClientSupabase)();\n    console.log(\"AuthProvider: Rendering with state - loading:\", loading, \"user:\", user === null || user === void 0 ? void 0 : user.email);\n    // Fetch user profile from database\n    const fetchUserProfile = async (userId)=>{\n        try {\n            const { data, error } = await supabase.from(\"users\").select(\"*\").eq(\"id\", userId).single();\n            if (error) {\n                console.error(\"AuthContext: Error fetching user profile:\", error);\n                return null;\n            }\n            return data;\n        } catch (error) {\n            console.error(\"AuthContext: Error fetching user profile:\", error);\n            return null;\n        }\n    };\n    // Initialize auth state\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        console.log(\"AuthContext: useEffect triggered for auth initialization\");\n        const initializeAuth = async ()=>{\n            console.log(\"AuthContext: Starting auth initialization\");\n            try {\n                // Use getUser() for secure authentication instead of getSession()\n                console.log(\"AuthContext: Calling supabase.auth.getUser()\");\n                const { data: { user: authUser }, error } = await supabase.auth.getUser();\n                console.log(\"AuthContext: getUser() result - user:\", !!authUser, \"error:\", error);\n                if (error) {\n                    console.error(\"AuthContext: Error getting user:\", error);\n                    setLoading(false);\n                    return;\n                }\n                if (authUser) {\n                    console.log(\"AuthContext: Found authenticated user:\", authUser.id);\n                    // Get the session for other purposes\n                    const { data: { session } } = await supabase.auth.getSession();\n                    console.log(\"AuthContext: Got session:\", !!session);\n                    setSession(session);\n                    // Fetch user profile from our database\n                    console.log(\"AuthContext: Fetching user profile from database\");\n                    const userProfile = await fetchUserProfile(authUser.id);\n                    console.log(\"AuthContext: User profile fetched:\", !!userProfile);\n                    setUser(userProfile);\n                } else {\n                    console.log(\"AuthContext: No authenticated user found\");\n                }\n            } catch (error) {\n                console.error(\"AuthContext: Error initializing auth:\", error);\n            } finally{\n                console.log(\"AuthContext: Setting loading to false\");\n                setLoading(false);\n            }\n        };\n        initializeAuth();\n        // Listen for auth changes\n        const { data: { subscription } } = supabase.auth.onAuthStateChange(async (event, session)=>{\n            var _session_user;\n            console.log(\"AuthContext: Auth state changed:\", event, session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.id);\n            setSession(session);\n            if (session && event !== \"SIGNED_OUT\") {\n                console.log(\"AuthContext: Fetching user profile for:\", session.user.id);\n                // Verify the user is still authenticated\n                const { data: { user: authUser }, error } = await supabase.auth.getUser();\n                if (error || !authUser) {\n                    console.error(\"AuthContext: User verification failed:\", error);\n                    setUser(null);\n                    setSession(null);\n                    return;\n                }\n                const userProfile = await fetchUserProfile(authUser.id);\n                setUser(userProfile);\n            } else {\n                console.log(\"AuthContext: Clearing user data\");\n                setUser(null);\n            }\n            // Force a small delay to ensure state is updated\n            setTimeout(()=>{\n                console.log(\"AuthContext: State update complete - user:\", !!(session === null || session === void 0 ? void 0 : session.user), \"loading:\", false);\n            }, 100);\n        });\n        return ()=>subscription.unsubscribe();\n    }, []);\n    // Sign up function\n    const signUp = async (email, password, userData)=>{\n        try {\n            console.log(\"AuthContext: Attempting signup\");\n            const { data, error } = await supabase.auth.signUp({\n                email,\n                password,\n                options: {\n                    data: {\n                        full_name: userData.full_name,\n                        phone: userData.phone\n                    }\n                }\n            });\n            if (error) {\n                console.error(\"AuthContext: Signup error:\", error);\n                return {\n                    data: null,\n                    error: error.message\n                };\n            }\n            if (!data.user) {\n                return {\n                    data: null,\n                    error: \"Failed to create user account\"\n                };\n            }\n            // Create user profile in the database\n            const { error: profileError } = await supabase.from(\"users\").insert({\n                id: data.user.id,\n                email: data.user.email,\n                full_name: userData.full_name || \"\",\n                phone: userData.phone || \"\",\n                role: \"customer\",\n                created_at: new Date().toISOString(),\n                updated_at: new Date().toISOString()\n            });\n            if (profileError) {\n                console.error(\"AuthContext: Profile creation error:\", profileError);\n                return {\n                    data: null,\n                    error: \"Failed to create user profile\"\n                };\n            }\n            console.log(\"AuthContext: Signup successful\");\n            return {\n                data: data.user,\n                error: null\n            };\n        } catch (error) {\n            console.error(\"AuthContext: Signup network error:\", error);\n            return {\n                data: null,\n                error: \"Network error. Please try again.\"\n            };\n        }\n    };\n    // Sign in function\n    const signIn = async (email, password)=>{\n        try {\n            console.log(\"AuthContext: Attempting signin\");\n            const { data, error } = await supabase.auth.signInWithPassword({\n                email,\n                password\n            });\n            if (error) {\n                console.error(\"AuthContext: Signin error:\", error);\n                return {\n                    data: null,\n                    error: error.message\n                };\n            }\n            console.log(\"AuthContext: Signin successful\");\n            // The auth state change listener will handle updating user and session\n            return {\n                data: data.user,\n                error: null\n            };\n        } catch (error) {\n            console.error(\"AuthContext: Signin network error:\", error);\n            return {\n                data: null,\n                error: \"Network error. Please try again.\"\n            };\n        }\n    };\n    // Google sign in function\n    const signInWithGoogle = async ()=>{\n        try {\n            console.log(\"AuthContext: Attempting Google signin\");\n            const response = await fetch(\"/api/auth/google\", {\n                method: \"POST\",\n                credentials: \"include\"\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                console.error(\"AuthContext: Google signin error:\", data.error);\n                return {\n                    data: null,\n                    error: data.error\n                };\n            }\n            console.log(\"AuthContext: Google OAuth URL generated\");\n            // Redirect to Google OAuth\n            window.location.href = data.url;\n            return {\n                data: data,\n                error: null\n            };\n        } catch (error) {\n            console.error(\"AuthContext: Google signin network error:\", error);\n            return {\n                data: null,\n                error: \"Network error. Please try again.\"\n            };\n        }\n    };\n    // Sign out function\n    const signOut = async ()=>{\n        try {\n            console.log(\"AuthContext: Attempting signout\");\n            const { error } = await supabase.auth.signOut();\n            if (error) {\n                console.error(\"AuthContext: Signout error:\", error);\n                return {\n                    error: error.message\n                };\n            }\n            console.log(\"AuthContext: Signout successful\");\n            // The auth state change listener will handle clearing user and session\n            return {\n                error: null\n            };\n        } catch (error) {\n            console.error(\"AuthContext: Signout network error:\", error);\n            return {\n                error: \"Network error. Please try again.\"\n            };\n        }\n    };\n    // Reset password function\n    const resetPassword = async (email)=>{\n        try {\n            console.log(\"AuthContext: Reset password not implemented via API yet\");\n            return {\n                error: \"Password reset functionality will be available soon\"\n            };\n        } catch (error) {\n            return {\n                error: error.message\n            };\n        }\n    };\n    // Update profile function\n    const updateProfile = async (updates)=>{\n        if (!user) {\n            return {\n                data: null,\n                error: \"No user logged in\"\n            };\n        }\n        try {\n            console.log(\"AuthContext: Update profile not implemented via API yet\");\n            return {\n                data: null,\n                error: \"Profile update functionality will be available soon\"\n            };\n        } catch (error) {\n            return {\n                data: null,\n                error: error.message\n            };\n        }\n    };\n    // Refresh user data\n    const refreshUser = async ()=>{\n        console.log(\"AuthContext: Refreshing user data\");\n        if (session) {\n            const userProfile = await fetchUserProfile(session.user.id);\n            setUser(userProfile);\n        }\n    };\n    const value = {\n        user,\n        session,\n        loading,\n        signUp,\n        signIn,\n        signInWithGoogle,\n        signOut,\n        resetPassword,\n        updateProfile,\n        refreshUser\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 316,\n        columnNumber: 5\n    }, this);\n}\n_s1(AuthProvider, \"sIDOCMze9iVqwxkgWIhOu8vskSI=\");\n_c = AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./contexts/AuthContext.tsx\n"));

/***/ })

});