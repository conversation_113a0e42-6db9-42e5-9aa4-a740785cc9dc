const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://soaoagcuubtzojytoati.supabase.co';
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseKey) {
  console.error('❌ Supabase key not found. Please set SUPABASE_SERVICE_ROLE_KEY or NEXT_PUBLIC_SUPABASE_ANON_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Comprehensive trip data based on scraped information from positive7.in
const tripsData = [
  {
    title: 'Manali',
    slug: 'manali',
    description: 'The most popular hill stations in Himachal, Manali offers the most magnificent views of the Pir Panjal and the Dhauladhar ranges covered with snow for most of the year.',
    detailed_description: 'Manali ~ One of the most popular hill stations in Himachal, Manali offers the most magnificent views of the Pir Panjal and the Dhauladhar ranges covered with snow for most parts of the year. Manali has many trekking options around it, making it a great base for exploring this side of Himalayas. River Beas provides great rafting options in the nearby town of Kullu. Its known for its eco-tourism in the reserve are both religious as well as wildlife.',
    destination: 'Himachal Pradesh',
    duration_days: 9,
    max_participants: 50,
    min_participants: 15,
    price_per_person: 27500,
    commercial_price: 27500,
    difficulty: 'moderate',
    category: 'Hill Station',
    mode_of_travel: '3rd TIER AC TRAIN (AHMEDABAD – DELHI – AHMEDABAD) + AC BUS (DELHI - CHANDIGARH – MANALI – CHANDIGARH – DELHI)',
    pickup_location: 'AHMEDABAD RLY. STATION',
    drop_location: 'AHMEDABAD RLY. STATION',
    property_used: 'Mastiff Grand or Similar',
    featured_image_url: '/images/trips/gettyimages-**********-612x612-1.jpg',
    activities: [
      'Trek to Jogini Fall',
      'Trek to Parsha Falls',
      'Snow Points',
      'Snow Activities',
      'River Rafting',
      'Local Sightseeing',
      'Explore Nature',
      'Bonfire',
      'Rock Garden',
      'Rose Garden',
      'Sukhmana Lake'
    ],
    optional_activities: [],
    benefits: [
      'Stay in 3*+ Category Resort',
      '24 hrs Hot & Cold water in washrooms',
      'Rooms will be shared on Quad sharing basis',
      'River Rafting, Snow activities & Snow dress with boots is part of the package',
      'Trained and verified Team',
      'First aid trained staff throughout the trip',
      'Trained female team members throughout the trip',
      'Fresh hygienic food from travel food partners during train journey',
      'No night journey by road'
    ],
    safety_supervision: [
      'Local Travel: Private, well-maintained buses with experienced drivers',
      'Accommodation: Safe, child-friendly resort with proper facilities, Separate accommodation for boys and girls with lady faculty for girls',
      'Supervision: 1:20 Ratio of P7 staff to participants plus Team Manager, ensuring close monitoring at all times',
      'First Aid: Dedicated medical kit and staff trained in first aid',
      'Travel Insurance: All participants are covered under travel insurance',
      '24 X 7 Trip Helpline Number. For any assistance we are available 24X7 on +91 **********'
    ],
    inclusions: [
      '3rd AC train fare from Ahmadabad to Delhi',
      'Local Travel',
      'Accommodation on quad sharing basis',
      'Insurance',
      'All Meals',
      'Sightseeing and activities like River Rafting',
      'Snow activities (including Snow Dress) as mentioned'
    ],
    exclusions: [
      'Personal Expenses such as Laundry, telephone, tips, gratuity, Mineral/soft drinks',
      'Any cost arising due to natural calamities like, landslides, road blockage, political disturbances, etc',
      'Anything which is not include in the inclusion'
    ],
    things_to_carry: [
      'Photo ID(Compulsory)',
      'Pack your regular cottons like 6 T-Shirts and 2 – 3 Jeans or track, 1 woolen, a pair of dry fit cloths for rafting and Night dress. (No short or sleeveless dresses allowed)',
      'Towel, sports shoes, cotton socks, slippers',
      'water bottle / bag',
      'Torch, Caps, sanitary requisites',
      'poly bag to pack wet soiled cloths etc.'
    ],
    available_dates: [
      '15th Feb – 23th Feb',
      '18th Feb – 26th Feb',
      '21st Feb – 1st March',
      '11th March – 19th March',
      '16th March – 24th March',
      '7th April – 15th April',
      '22nd April – 30th April',
      '2nd May – 10th May',
      '8th May – 16th May',
      '14th May – 22nd May',
      '26th May - 3rd June',
      '1st June – 9th June',
      '7th June – 15th June'
    ],
    payment_terms: '50% of the cost to be paid at the time of booking and the remaining 50% shall be paid 15 days before the Trip',
    cancellation_policy: {
      'minimum': '25%',
      'between_30_16_days': '50%',
      'between_15_8_days': '75%',
      'last_7_days': 'No Refund'
    },
    special_notes: [
      'Mode of travel: Private Tempo travelers throughout the trip',
      'AC will not be functional on hills',
      'Please note that this is a tentative itinerary. Final itinerary shall be given 7 days before the day of departure',
      'If Train Tickets are not available from Ahmedabad we will book the tickets via Udaipur / Vadodara',
      'We should not be held responsible for any unforeseen conditions like road block, strike, riots, illness, accidents, \'bandhs\' etc',
      'The sightseeing schedule is subject to change as per the operational feasibility and all activities are subject to weather condition'
    ],
    itinerary: {
      'day_1': {
        'title': 'Ahmedabad Railway Station',
        'description': 'Report at Ahmedabad Railway station to board our train to Delhi. Night will be in Train.'
      },
      'day_2': {
        'title': 'Delhi – Chandigarh',
        'description': 'Reach Delhi in the morning and get transferred to Chandigarh. Breakfast will be served en-route. Expected to reach Chandigarh by noon. Check-In to hotel and post lunch, go for sightseeing covering Rock Garden, Rose Garden & Sukhmana Lake. Later return back to the hotel and enjoy your dinner. Night will be in Chandigarh.'
      },
      'day_3': {
        'title': 'Manali',
        'description': 'Post breakfast Check-out and get transferred to Manali. Lunch will be served en-route. Expected to reach Manali by late afternoon. Check-in to hotel. Evening at leisure and later enjoy dinner. Night will be in Manali.'
      },
      'day_4': {
        'title': 'Jogni Waterfall Trek & Visits',
        'description': 'Early wakeup and will go for a walk along the river bed and return back for breakfast. Post breakfast we will go for a Mountain Trail to Parsha waterfall & return back to resort. Post Lunch go for adventure trekking to Jogini Fall – The trek route passes through narrow lane by the side of temple and it goes through apple orchids, tall pine trees & small water streams. The Gentle walk will take 30 minutes to reach the base of the waterfall. Small stretch of up slope will be there near the waterfall. Yogini Mata temple is at the base of the waterfall and later return to resort and enjoy your dinner. Night will be in Manali.'
      },
      'day_5': {
        'title': 'Solang valley',
        'description': 'Early Morning after breakfast, start your adventure trip to Solang valley, Atal tunnel and Sishu for snow activities. Later in the evening return back to the resort. En-route visit Hidimba Temple. Enjoy the dinner. Night will be in Manali.'
      },
      'day_6': {
        'title': 'River Rafting',
        'description': 'Post breakfast drive down to Kullu and enjoy thrilling River Rafting session and later return back to resort for lunch. Post lunch visit Tibetan Monastery and local market. Return back to resort and enjoy Music and dance over bonfire followed by dinner. Night will be in Manali.'
      },
      'day_7': {
        'title': 'Chandigarh',
        'description': 'Checkout post breakfast and get transferred Chandigarh. Lunch will be served en-route. Will reach Chandigarh by evening and check-in to our hotel. Evening explore the local market and return back to hotel. Relax and enjoy dinner. Night will be in Chandigarh.'
      },
      'day_8': {
        'title': 'Delhi – Ahmedabad',
        'description': 'In the morning post breakfast checkout & get transferred to Delhi railway station to board your train to Ahmedabad. Lunch & dinner will be served en-route. Night will be in train.'
      },
      'day_9': {
        'title': 'Ahmedabad',
        'description': 'We will reach Ahmedabad in the morning and the trip ends with fond memories to cherish for years to come.'
      }
    },
    gallery_images: [
      '/images/trips/gettyimages-**********-612x612-1.jpg',
      '/images/trips/Manali-River.jpg'
    ],
    is_active: true,
    is_featured: true,
    available_from: '2025-02-15',
    available_to: '2025-06-15'
  },
  {
    title: 'Rishikesh',
    slug: 'rishikesh',
    description: 'Rishikesh, nestled in the foothills of the Himalayas along the banks of the Ganges River, is a captivating destination known as the "Yoga Capital of the World."',
    detailed_description: 'Rishikesh, nestled in the foothills of the Himalayas along the banks of the Ganges River, is a captivating destination known as the "Yoga Capital of the World." Famous for its serene atmosphere and spiritual significance, Rishikesh attracts travelers seeking tranquility, wellness, and adventure. Visitors can participate in yoga and meditation retreats, explore ancient temples, and witness the mesmerizing Ganga Aarti ceremony at Triveni Ghat. Thrill-seekers can indulge in activities like white-water rafting, trekking, and bungee jumping. With its stunning natural beauty, vibrant culture, and rich spiritual heritage, Rishikesh offers a unique blend of relaxation and adventure.',
    destination: 'Uttarakhand',
    duration_days: 7,
    max_participants: 50,
    min_participants: 15,
    price_per_person: 21700,
    commercial_price: 21700,
    difficulty: 'easy',
    category: 'Spiritual',
    mode_of_travel: '3rd TIER AC TRAIN (AHMEDABAD – DELHI – AHMEDABAD) + AC BUS (DELHI - RISHIKESH – DELHI)',
    pickup_location: 'AHMEDABAD RLY. STATION',
    drop_location: 'AHMEDABAD RLY. STATION',
    property_used: 'PHOOL CHITTI RESORT or SIMILAR',
    featured_image_url: '/images/trips/dusk-time-rishikesh-holy-town-travel-destination-india-1024x684.jpg',
    activities: [
      'Trek To Patna Falls',
      'Nature Trail',
      'White Water River Rafting (9kms)',
      'Nature Walk along The River Tributary',
      'Bonfire',
      'Music',
      'Dance'
    ],
    optional_activities: [
      'Bungee Jump',
      'Giant Swing',
      'Flying Fox'
    ],
    benefits: [
      'Stay in 3*++ Category Resort',
      '24 hrs Hot & Cold water in washrooms',
      'Rooms will be shared on Quad sharing basis',
      'White Water River Rafting is part of the package',
      'Trained and verified Team',
      'First aid trained staff throughout the trip',
      'Trained female team members throughout the trip',
      'Fresh hygienic food from travel food partners during train journey',
      'No night journey by road'
    ],
    safety_supervision: [
      'Local Travel: Private, well-maintained buses with experienced drivers',
      'Accommodation: Safe, child-friendly resort with proper facilities, Separate accommodation for boys and girls with lady faculty for girls',
      'Supervision: 1:20 Ratio of P7 staff to participants plus Team Manager, ensuring close monitoring at all times',
      'First Aid: Dedicated medical kit and staff trained in first aid',
      'Travel Insurance: All participants are covered under travel insurance',
      '24 X 7 Trip Helpline Number. For any assistance we are available 24X7 on +91 **********'
    ],
    inclusions: [
      '3rd AC train fare from Ahmedabad to Delhi',
      'Local Travel',
      'Accommodation on quad sharing basis',
      'White water river rafting',
      'All Meals',
      'Travel Insurance',
      'Sightseeing and activities as mentioned'
    ],
    exclusions: [
      'Personal Expenses such as Laundry, telephone, tips, gratuity, Mineral/soft drinks',
      'Any cost arising due to natural calamities like landslides, road blockage, political disturbances, etc',
      'Anything which is not include in the inclusion'
    ],
    things_to_carry: [
      'Photo ID(Compulsory)',
      'Pack your regular cottons like 5 T-Shirts and 2 – 3 Jeans or track, 1 woolen, and Night dress. (No short or sleeveless dresses allowed)',
      'Towel, sports shoes, cotton socks, slippers',
      'water bottle / bag',
      'Torch, Caps, sanitary requisites',
      'poly bag to pack wet soiled cloths etc.',
      'carry a bed sheet and a light blanket / shawl'
    ],
    available_dates: [
      '23rd Feb – 1st March',
      '2nd March - 8th March',
      '16th March - 22nd March',
      '6th April - 12th April',
      '21st April – 27th April',
      '4th May - 10th May',
      '11th May - 17th May',
      '19th May - 25th May'
    ],
    payment_terms: '50% of the cost to be paid at the time of booking and the remaining 50% shall be paid 15 days before the Trip',
    cancellation_policy: {
      'minimum': '25%',
      'between_30_16_days': '50%',
      'between_15_8_days': '75%',
      'last_7_days': 'No Refund'
    },
    special_notes: [
      'Please note that this is a tentative itinerary. Final itinerary shall be given 7 days before the day of departure',
      'If Train Tickets are not available from Ahmedabad we will book the tickets via Udaipur / Vadodara',
      'We should not be held responsible for any unforeseen conditions like road block, strike, riots, illness, accidents, \'bandhs\' etc',
      'The sightseeing schedule is subject to change as per the operational feasibility and all activities are subject to weather condition'
    ],
    itinerary: {
      'day_1': {
        'title': 'Ahmedabad To Delhi',
        'description': 'Report at Ahmedabad Railway to board our train to Delhi. Night will be in Train.'
      },
      'day_2': {
        'title': 'Delhi To Haridwar',
        'description': 'Reach Delhi in the Morning and get transfer to Haridwar by private bus. Breakfast will be served en-route. On arrival, check-in to the Hotel and rest for a while. Later visit Har Ki Pauri and experience the Ganga Aarti. Return back to the resort. Night will be in Haridwar.'
      },
      'day_3': {
        'title': 'Rishikesh',
        'description': 'Check-out post early breakfast and get transferred to Rishikesh. En-route visit Rajaji National Park and enjoy the Jungle Safari. On arrival, check-in to the resort, enjoy your hot delicious lunch and later relax for a while. Evening we will go for a walk along the tributaries of river Ganga and return back to the resort for various games and activities. Night will be in Rishikesh.'
      },
      'day_4': {
        'title': 'Rishikesh',
        'description': 'An early wake up call, followed by a bird watching session, and back to resort for Breakfast. Post Breakfast, we gear up for an exciting session of Rafting. We cover a mild stretch of rafting for 09 Kms, covering some rapids of Grade I and II. You can enjoy the activity of body surfing and cliff jumping. We return to the camp for Lunch. Post lunch we take a hike to the famous Patna Waterfall. The waterfall is famous for its sprinkle showers. Have some leisure time to enjoy at the fall. We return to the camp for Tea/Coffee. Post the refreshments, will engage in an interactive team building session. Night will be in Rishikesh.'
      },
      'day_5': {
        'title': 'Bungy Jump',
        'description': 'Post Breakfast, we drive to Jumpin heights for some dare devil Adventure like Bungy Jump, Giant Swing & Flying fox. Its India\'s only fixed platform Bungy- where Jumps are operated from a professional cantilever, to distinguish itself from the joyrides in entertainment parks, and create instead, an extreme experience for the true adventure enthusiast in you. (Give you name in advance along with the form to block your Jumps) All jumps are optional and payment has to be made directly. Night will be in Rishikesh.'
      },
      'day_6': {
        'title': 'Rishikesh To Delhi',
        'description': 'Morning wake up, enjoy leisure time with friends. Post breakfast will check-out and get transfer to New Delhi to board our Train. Night will be in train.'
      },
      'day_7': {
        'title': 'Ahmedabad',
        'description': 'We will reach Ahmedabad in the Morning and the Trip ends with fond memories to cherish for years'
      }
    },
    gallery_images: [
      '/images/trips/dusk-time-rishikesh-holy-town-travel-destination-india-1024x684.jpg',
      '/images/trips/temple-rishikesh-india-1113888.jpg'
    ],
    is_active: true,
    is_featured: true,
    available_from: '2025-02-23',
    available_to: '2025-05-25'
  }
];

// Function to insert trip data
async function insertTripsData() {
  console.log('🚀 Starting to populate trips data...');

  for (const tripData of tripsData) {
    try {
      // Check if trip already exists
      const { data: existingTrip } = await supabase
        .from('trips')
        .select('id')
        .eq('slug', tripData.slug)
        .single();

      if (existingTrip) {
        // Update existing trip
        const { error } = await supabase
          .from('trips')
          .update(tripData)
          .eq('slug', tripData.slug);

        if (error) {
          console.error(`❌ Error updating trip ${tripData.title}:`, error);
        } else {
          console.log(`✅ Updated trip: ${tripData.title}`);
        }
      } else {
        // Insert new trip
        const { error } = await supabase
          .from('trips')
          .insert(tripData);

        if (error) {
          console.error(`❌ Error inserting trip ${tripData.title}:`, error);
        } else {
          console.log(`✅ Inserted trip: ${tripData.title}`);
        }
      }
    } catch (error) {
      console.error(`❌ Unexpected error with trip ${tripData.title}:`, error);
    }
  }

  console.log('\n📊 Trip data population complete!');
  console.log('🎯 Next: Add more trips by scraping additional pages from positive7.in');
}

// Run the script
insertTripsData().catch(console.error);
